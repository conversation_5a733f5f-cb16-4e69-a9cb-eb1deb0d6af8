#!/usr/bin/env node

/**
 * Modern esbuild-based TypeScript build script
 * Fast, standard, and reliable replacement for the custom build-ts.js
 */

import { build } from 'esbuild';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const srcDir = path.join(rootDir, 'backend', 'src');
const distDir = path.join(rootDir, 'dist');

/**
 * Clean the dist directory
 */
async function cleanDist() {
  try {
    await fs.rm(distDir, { recursive: true, force: true });
    console.log('✅ Cleaned dist directory');
  } catch (err) {
    console.log('ℹ️ No dist directory to clean');
  }
  
  try {
    await fs.mkdir(distDir, { recursive: true });
    console.log('✅ Created dist directory');
  } catch (err) {
    console.error('❌ Error creating dist directory:', err);
    process.exit(1);
  }
}

/**
 * Find all TypeScript entry points
 */
async function findEntryPoints(dir, entryPoints = []) {
  const files = await fs.readdir(dir, { withFileTypes: true });
  
  for (const file of files) {
    const fullPath = path.join(dir, file.name);
    
    if (file.isDirectory()) {
      // Skip certain directories
      if (!['node_modules', 'legacy', 'tests', '__tests__', 'test'].includes(file.name)) {
        await findEntryPoints(fullPath, entryPoints);
      }
    } else if (file.name.endsWith('.ts') && !file.name.endsWith('.test.ts') && !file.name.endsWith('.d.ts')) {
      entryPoints.push(fullPath);
    }
  }
  
  return entryPoints;
}

/**
 * Copy non-TypeScript files
 */
async function copyAssets() {
  try {
    // Copy package.json if it exists in src
    const srcPackageJson = path.join(srcDir, 'package.json');
    const distPackageJson = path.join(distDir, 'package.json');
    
    try {
      await fs.access(srcPackageJson);
      await fs.copyFile(srcPackageJson, distPackageJson);
      console.log('✅ Copied package.json');
    } catch {
      // No package.json in src, that's fine
    }
    
    // Copy other assets (json, txt, etc.)
    await copyNonTsFiles(srcDir);
    
    // Copy campaign data if it exists
    await copyCampaignData();
    
  } catch (error) {
    console.error('❌ Error copying assets:', error.message);
  }
}

/**
 * Copy non-TypeScript files recursively
 */
async function copyNonTsFiles(dir, baseDir = srcDir) {
  const files = await fs.readdir(dir, { withFileTypes: true });
  
  for (const file of files) {
    const srcPath = path.join(dir, file.name);
    const relativePath = path.relative(baseDir, srcPath);
    const destPath = path.join(distDir, relativePath);
    
    if (file.isDirectory()) {
      if (!['node_modules', 'legacy', 'tests', '__tests__', 'test'].includes(file.name)) {
        await fs.mkdir(destPath, { recursive: true });
        await copyNonTsFiles(srcPath, baseDir);
      }
    } else if (!file.name.endsWith('.ts') && !file.name.endsWith('.js')) {
      try {
        await fs.mkdir(path.dirname(destPath), { recursive: true });
        await fs.copyFile(srcPath, destPath);
        console.log(`✅ Copied: ${relativePath}`);
      } catch (error) {
        console.error(`❌ Error copying ${relativePath}:`, error.message);
      }
    }
  }
}

/**
 * Copy campaign data folder if it exists
 */
async function copyCampaignData() {
  const campaignsDir = path.join(rootDir, 'campaigns');
  const destCampaignsDir = path.join(distDir, 'campaigns');
  
  try {
    await fs.access(campaignsDir);
    await fs.mkdir(destCampaignsDir, { recursive: true });
    await execAsync(`cp -r ${campaignsDir}/* ${destCampaignsDir}`);
    console.log('✅ Copied campaigns directory');
  } catch (err) {
    console.log('ℹ️ No campaigns directory to copy');
  }
}

/**
 * Main build function using esbuild
 */
async function buildWithEsbuild() {
  console.log('🚀 Starting esbuild TypeScript build...');
  
  // Clean dist directory
  await cleanDist();
  
  // Find all TypeScript entry points
  const entryPoints = await findEntryPoints(srcDir);
  console.log(`Found ${entryPoints.length} TypeScript files to build`);
  
  try {
    // Build with esbuild
    await build({
      entryPoints,
      bundle: false,           // Don't bundle, keep file structure
      outdir: distDir,
      outbase: srcDir,         // Preserve directory structure
      platform: 'node',       // Target Node.js
      target: 'node22',        // Target Node.js 22
      format: 'esm',           // ESM format to support top-level await
      sourcemap: true,         // Generate source maps
      minify: false,           // Don't minify for debugging
      keepNames: true,         // Keep function names for debugging
      tsconfig: path.join(rootDir, 'tsconfig.json'),
      logLevel: 'info',
      color: true,
      // When bundle: false, esbuild just transpiles files individually
      // No need for external since we're not bundling
    });
    
    console.log('✅ esbuild compilation completed successfully');
    
    // Copy assets
    await copyAssets();
    
    console.log('✅ Build completed successfully!');
    
  } catch (error) {
    console.error('❌ Build failed:', error);
    process.exit(1);
  }
}

// Execute build
buildWithEsbuild().catch(err => {
  console.error('❌ Build failed:', err);
  process.exit(1);
});
