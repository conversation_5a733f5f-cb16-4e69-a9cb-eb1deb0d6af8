#!/usr/bin/env node

/**
 * Custom TypeScript build script that transpiles all TS files to JS
 * without doing type checking, enabling a staged migration approach.
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const srcDir = path.join(rootDir, 'backend', 'src');
const distDir = path.join(rootDir, 'dist');

/**
 * Clean the dist directory
 */
async function cleanDist() {
  try {
    await fs.rm(distDir, { recursive: true, force: true });
    console.log('✅ Cleaned dist directory');
  } catch (err) {
    // It's okay if the directory doesn't exist
    console.log('ℹ️ No dist directory to clean');
  }
  
  try {
    await fs.mkdir(distDir, { recursive: true });
    console.log('✅ Created dist directory');
  } catch (err) {
    console.error('❌ Error creating dist directory:', err);
    process.exit(1);
  }
}

/**
 * Find all TypeScript files in src directory
 */
async function findTsFiles(dir, fileList = []) {
  const files = await fs.readdir(dir, { withFileTypes: true });
  
  for (const file of files) {
    const fullPath = path.join(dir, file.name);
    
    if (file.isDirectory()) {
      if (!['node_modules', 'legacy', 'tests'].includes(file.name)) {
        await findTsFiles(fullPath, fileList);
      }
    } else if (file.name.endsWith('.ts') && !file.name.endsWith('.test.ts')) {
      fileList.push(fullPath);
    }
  }
  
  return fileList;
}

/**
 * Transpile a TypeScript file to JavaScript using tsc
 */
async function transpileFile(filePath) {
  const relativePath = path.relative(srcDir, filePath);
  const outputPath = path.join(distDir, relativePath.replace(/\.ts$/, '.js'));

  // Ensure output directory exists
  const outputDir = path.dirname(outputPath);
  await fs.mkdir(outputDir, { recursive: true });

  try {
    // Use tsc to transpile the file without type checking
    const command = `tsc "${filePath}" --outDir "${outputDir}" --target ES2022 --module CommonJS --esModuleInterop --skipLibCheck --noEmitOnError false --allowJs --declaration false`;

    await execAsync(command);
    console.log(`✅ Transpiled: ${relativePath}`);
    return true;
  } catch (error) {
    console.error(`❌ Error transpiling ${relativePath}:`, error.message);
    return false;
  }
}

/**
 * Copy non-TypeScript files
 */
async function copyNonTsFiles(dir, baseDir = srcDir) {
  const files = await fs.readdir(dir, { withFileTypes: true });
  
  for (const file of files) {
    const srcPath = path.join(dir, file.name);
    const relativePath = path.relative(baseDir, srcPath);
    const destPath = path.join(distDir, relativePath);
    
    if (file.isDirectory()) {
      if (!['node_modules', 'legacy', 'tests'].includes(file.name)) {
        await fs.mkdir(destPath, { recursive: true });
        await copyNonTsFiles(srcPath, baseDir);
      }
    } else if (!file.name.endsWith('.ts')) {
      try {
        await fs.copyFile(srcPath, destPath);
        console.log(`✅ Copied: ${relativePath}`);
      } catch (error) {
        console.error(`❌ Error copying ${relativePath}:`, error.message);
      }
    }
  }
}

/**
 * Copy campaign data folder if it exists
 */
async function copyCampaignData() {
  const campaignsDir = path.join(rootDir, 'campaigns');
  const destCampaignsDir = path.join(distDir, 'campaigns');
  
  try {
    await fs.access(campaignsDir);
    await fs.mkdir(destCampaignsDir, { recursive: true });
    await execAsync(`cp -r ${campaignsDir}/* ${destCampaignsDir}`);
    console.log('✅ Copied campaigns directory');
  } catch (err) {
    console.log('ℹ️ No campaigns directory to copy');
  }
}

/**
 * Transpile all TypeScript files at once using tsc
 */
async function transpileAllFiles() {
  try {
    console.log('� Transpiling all TypeScript files...');

    // Use tsc to transpile all files at once
    const command = `tsc "${srcDir}/**/*.ts" --outDir "${distDir}" --target ES2022 --module CommonJS --esModuleInterop --skipLibCheck --noEmitOnError false --allowJs --declaration false --rootDir "${srcDir}"`;

    await execAsync(command);
    console.log('✅ All TypeScript files transpiled successfully');
    return true;
  } catch (error) {
    console.error('❌ Error during transpilation:', error.message);

    // Fallback to individual file transpilation
    console.log('🔄 Falling back to individual file transpilation...');
    const tsFiles = await findTsFiles(srcDir);
    const results = await Promise.all(tsFiles.map(transpileFile));
    const successCount = results.filter(Boolean).length;

    console.log(`Transpiled ${successCount}/${tsFiles.length} TypeScript files`);
    return successCount > 0;
  }
}

/**
 * Main build function
 */
async function build() {
  console.log('🚀 Starting TypeScript build (transpile-only)...');

  // Clean dist directory
  await cleanDist();

  // Find all TypeScript files
  const tsFiles = await findTsFiles(srcDir);
  console.log(`Found ${tsFiles.length} TypeScript files to transpile`);

  // Transpile all TypeScript files
  await transpileAllFiles();

  // Copy non-TypeScript files
  await copyNonTsFiles(srcDir);

  // Copy campaign data
  await copyCampaignData();

  console.log('✅ Build completed successfully!');
}

// Execute build
build().catch(err => {
  console.error('❌ Build failed:', err);
  process.exit(1);
});
