# Overnight Development Automation

Simple, working automation system for CC-V1 project that runs overnight without human intervention.

## What It Does

- Runs `claude-flow swarm` autonomously
- One Claude instance responds to keep it active
- Auto-restarts if anything crashes
- Works on a nightly git branch
- Monitors and logs everything

## Quick Start

### Start Overnight Automation
```bash
./_dev-automation-beta/overnight-automation.sh
```

### View Running Session
```bash
tmux attach -t overnight-automation
```

### Stop Everything
```bash
./_dev-automation-beta/stop-overnight.sh
```

## How It Works

**Simple 3-pane tmux setup:**

```
+------------------------+
|     claude-flow        |
|    (autonomous)        |
+------------------------+
|  responder  | monitor  |
| (auto-feed) |(status)  |
+------------------------+
```

1. **claude-flow** runs autonomously, exploring and improving code
2. **responder** provides tasks when claude-flow asks "what next?"
3. **monitor** watches both and restarts if needed
## What It Works On

The automation focuses on:
- TypeScript compilation errors
- TODO/FIXME comments
- Failing tests
- Code quality improvements
- Security vulnerabilities
- Performance optimizations
- Documentation updates
- Dependency updates

## Logs

Everything is logged to:
- `logs/overnight_YYYYMMDD_HHMMSS/claude-flow.log`
- `logs/overnight_YYYYMMDD_HHMMSS/responder.log`
- `logs/overnight_YYYYMMDD_HHMMSS/monitor.log`

## Alternative Scripts

If you prefer the more complex setups (archived):
- `launch-orchestrator.sh` - 4-pane supervisor system
- `claude-pipe-orchestrator.sh` - Named pipe communication
- `start-overnight-orchestrator.sh` - Multi-session approach

## No Rocket Science

This is intentionally simple:
- Shell scripts, not Python
- tmux, not complex orchestration
- One responder, not multiple agents
- Direct communication, not APIs

It just works.
