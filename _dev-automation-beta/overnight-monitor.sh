#!/bin/bash
# Overnight monitor and auto-nudger

LOG_DIR="$1"
REPO_ROOT="/home/<USER>/github/cc-v1"

echo "🌙 Overnight Automation Monitor"
echo "==============================="
echo "Monitoring claude-flow and providing auto-nudges..."
echo ""

nudge_count=0
last_nudge=0

while true; do
    current_time=$(date +%s)
    
    # Clear screen and show status
    clear
    echo "🌙 Overnight Automation Monitor - $(date)"
    echo "=========================================="
    echo ""
    
    # Check if claude-flow is running
    if pgrep -f "claude-flow" > /dev/null; then
        echo "✅ claude-flow: ACTIVE"
    else
        echo "❌ claude-flow: NOT RUNNING - RESTARTING..."
        tmux send-keys -t overnight-automation:0.0 C-c Enter
        sleep 2
        tmux send-keys -t overnight-automation:0.0 "claude-flow swarm 'Resume overnight exploration' --claude 2>&1 | tee -a $LOG_DIR/claude-flow.log" Enter
    fi
    
    # Check responder
    if pgrep -f "claude chat.*responder" > /dev/null; then
        echo "✅ responder: READY"
    else
        echo "❌ responder: NOT RUNNING - RESTARTING..."
        tmux send-keys -t overnight-automation:0.1 C-c Enter
        sleep 1
        tmux send-keys -t overnight-automation:0.1 "claude chat < prompts/overnight-responder.txt 2>&1 | tee -a $LOG_DIR/responder.log" Enter
    fi
    
    echo ""
    echo "📊 Session Stats:"
    echo "- Nudges sent: $nudge_count"
    echo "- Last nudge: $(date -d @$last_nudge 2>/dev/null || echo 'Never')"
    echo "- Uptime: $(( (current_time - $(stat -c %Y $LOG_DIR)) / 60 )) minutes"
    
    echo ""
    echo "📝 Recent Activity:"
    echo "-------------------"
    if [ -f "$LOG_DIR/claude-flow.log" ]; then
        tail -5 "$LOG_DIR/claude-flow.log" | grep -E "(Exploring|Found|TODO|Error|Complete)" | tail -3 || echo "Waiting for activity..."
    fi
    
    # Auto-nudge every 10 minutes if no activity
    if [ $((current_time - last_nudge)) -gt 600 ]; then
        echo ""
        echo "⏰ Sending auto-nudge to keep exploration active..."
        
        # Send nudge to responder
        tmux send-keys -t overnight-automation:0.1 "" Enter
        tmux send-keys -t overnight-automation:0.1 "The swarm needs direction. Please provide an exploration task from your rotation." Enter
        
        nudge_count=$((nudge_count + 1))
        last_nudge=$current_time
    fi
    
    echo ""
    echo "🔄 Next check in 30 seconds..."
    echo "Press Ctrl+C to stop monitoring"
    
    sleep 30
done
