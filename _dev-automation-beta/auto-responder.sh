#!/bin/bash
# Auto-responder for claude-flow prompts

LOG_DIR="$1"
REPO_ROOT="/home/<USER>/github/cc-v1"

echo "🤖 Auto-responder monitoring claude-flow..."

# Template-based responses for development cycle
responses=(
    "Use the analyze-current-state template: cat _dev-automation-beta/templates/feature/analyze-current-state.yaml"
    "Apply the bug-fix template: cat _dev-automation-beta/templates/bug-fix/analyze-and-fix.yaml"
    "Run the testing template: cat _dev-automation-beta/templates/testing/run-test-suite.yaml"
    "Use the integration template: cat _dev-automation-beta/templates/feature/complete-integration.yaml"
    "Apply the deployment template: cat _dev-automation-beta/templates/deployment/prepare-deployment.yaml"
    "Follow the template prompts exactly and verify each step"
    "Check template success criteria before moving to next template"
    "Use the automation framework for systematic development"
)

response_index=0

while true; do
    # Check if claude-flow is asking for input OR if it's been idle
    log_lines=$(tail -10 "$LOG_DIR/claude-flow.log" 2>/dev/null || echo "")

    if echo "$log_lines" | grep -q -E "(What would you like|What should I|How can I help|What's next|waiting for input)" || \
       echo "$log_lines" | grep -q -E "(Pro Tips|Continue working)" && [ $(($(date +%s) % 60)) -eq 0 ]; then

        echo "📝 Sending template-based instruction to claude-flow..."

        # Send response to claude-flow
        response="${responses[$response_index]}"
        tmux send-keys -t automated-user:0.0 "$response" Enter

        # Rotate to next response
        response_index=$(( (response_index + 1) % ${#responses[@]} ))

        echo "✅ Sent: $response"
        sleep 30  # Wait before checking again
    fi

    sleep 5  # Check every 5 seconds
done
