#!/bin/bash

# Run claude-flow swarm in a continuous loop with message filtering
echo "🐝 Starting continuous swarm execution..."

while true; do
    echo "🚀 Launching swarm..."
    npx claude-flow swarm "Read CLAUDE.md for priorities. Find and fix all TODOs, bugs, and test failures. Improve code quality. Work autonomously without asking questions." --executor --strategy development --max-agents 5 --parallel 2>&1 | grep -v "Compiled swarm module not found"
    
    exit_code=${PIPESTATUS[0]}
    
    if [ $exit_code -eq 0 ]; then
        echo "✅ Swarm completed successfully"
    else
        echo "⚠️  Swarm exited with code: $exit_code"
    fi
    
    echo "⏳ Restarting in 5 seconds..."
    sleep 5
done