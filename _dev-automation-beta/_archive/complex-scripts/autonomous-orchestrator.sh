#!/bin/bash
# Fully Autonomous Claude-Flow Orchestrator
# Claude instances communicate directly with claude-flow - no human needed

set -e

REPO_ROOT="/home/<USER>/github/cc-v1"
ORCHESTRATOR_DIR="$REPO_ROOT/_dev-automation-beta/top-level-orchestrator"
TMUX_SESSION="autonomous-orchestrator"
LOG_DIR="$ORCHESTRATOR_DIR/logs/$(date +%Y%m%d_%H%M%S)"

mkdir -p "$LOG_DIR"
mkdir -p "$ORCHESTRATOR_DIR/prompts"

echo "🤖 Autonomous Claude-Flow Orchestrator"
echo "=====================================
echo "NO HUMAN INTERVENTION REQUIRED"

# Kill existing session
tmux kill-session -t $TMUX_SESSION 2>/dev/null || true

# Create tmux session with 2 panes side by side
tmux new-session -d -s $TMUX_SESSION
tmux split-window -h -t $TMUX_SESSION:0

# Left pane: Claude-flow swarm
echo "🚀 Starting claude-flow swarm..."
tmux send-keys -t $TMUX_SESSION:0.0 "cd $REPO_ROOT" Enter
tmux send-keys -t $TMUX_SESSION:0.0 "claude-flow swarm 'Read CLAUDE.md and autonomously improve the CC-V1 project based on priorities, TODOs, and test failures' --claude 2>&1 | tee $LOG_DIR/claude-flow.log" Enter

# Right pane: Claude orchestrator that feeds claude-flow
echo "🧠 Starting autonomous Claude orchestrator..."

# Create the orchestrator prompt that will continuously interact with claude-flow
cat > "$ORCHESTRATOR_DIR/prompts/autonomous-orchestrator.txt" << 'EOF'
You are an autonomous orchestrator for a claude-flow swarm. Your job is to keep the swarm continuously active and productive by responding to its prompts and providing new objectives when it's idle.

CRITICAL RULES:
1. You are communicating DIRECTLY with the claude-flow swarm in the left pane
2. Watch for any prompts or questions from claude-flow
3. When you see claude-flow asking "What would you like me to do?" or similar, IMMEDIATELY respond with exploration suggestions
4. NEVER wait for human input - you must act autonomously
5. Keep the swarm busy 24/7

RESPONSE STRATEGIES:

When claude-flow asks what to do, respond with variations of:
- "Read CLAUDE.md to understand current priorities, then search for TODO and FIXME comments across the codebase"
- "Run the test suite and investigate any failures you find"
- "Look for TypeScript compilation errors and fix them"
- "Search for console.log statements that should be removed from production code"
- "Check for unused dependencies or security vulnerabilities with npm audit"
- "Explore the _dev-automation-beta directory for automation improvements"
- "Review recent git commits and look for follow-up work needed"
- "Find large files that might benefit from refactoring"
- "Look for error handling that could be improved"
- "Search for hardcoded values that should be configuration"

When claude-flow completes a task, immediately suggest the next area:
- "Great work! Now let's explore [different area]"
- "Excellent. Next, investigate [another pattern]"
- "Well done. How about checking [different component]"

If claude-flow seems idle for more than 30 seconds, proactively nudge:
- "Are you still working on something? If not, try exploring [suggestion]"
- "If you're finished with that, let's look at [new area]"
- "Ready for the next exploration? Consider [different task]"

IMPORTANT: 
- Keep responses short and action-oriented
- Always suggest concrete explorations, not vague ideas
- Rotate through different areas to ensure comprehensive coverage
- If you see errors, encourage investigation
- Never say "let me know" or "what would you like" - always provide direction

Start by watching the left pane. As soon as you see any prompt from claude-flow, respond immediately with an exploration suggestion. Keep the swarm working continuously.
EOF

# Start the orchestrator Claude that will interact with claude-flow
tmux send-keys -t $TMUX_SESSION:0.1 "cd $ORCHESTRATOR_DIR" Enter
tmux send-keys -t $TMUX_SESSION:0.1 "while true; do" Enter
tmux send-keys -t $TMUX_SESSION:0.1 "  claude chat < prompts/autonomous-orchestrator.txt 2>&1 | tee -a $LOG_DIR/orchestrator.log" Enter
tmux send-keys -t $TMUX_SESSION:0.1 "  echo 'Orchestrator cycle complete, restarting...'" Enter
tmux send-keys -t $TMUX_SESSION:0.1 "  sleep 5" Enter
tmux send-keys -t $TMUX_SESSION:0.1 "done" Enter

# Create a monitor script that ensures everything keeps running
cat > "$ORCHESTRATOR_DIR/continuous-monitor.sh" << 'EOF'
#!/bin/bash
# Ensures claude-flow and orchestrator keep running

TMUX_SESSION="autonomous-orchestrator"
LOG_DIR="$1"

while true; do
    # Check if tmux session exists
    if ! tmux has-session -t $TMUX_SESSION 2>/dev/null; then
        echo "$(date): Tmux session died, restarting entire orchestrator..."
        bash /home/<USER>/github/cc-v1/_dev-automation-beta/autonomous-orchestrator.sh
        exit 0
    fi
    
    # Check if claude-flow is running
    if ! pgrep -f "claude-flow swarm" > /dev/null; then
        echo "$(date): claude-flow died, restarting..."
        tmux send-keys -t $TMUX_SESSION:0.0 C-c Enter
        sleep 2
        tmux send-keys -t $TMUX_SESSION:0.0 "claude-flow swarm 'Continue exploring CC-V1 project - check git status and CLAUDE.md for context' --claude 2>&1 | tee -a $LOG_DIR/claude-flow.log" Enter
    fi
    
    # Check if orchestrator claude is running
    if ! pgrep -f "claude chat.*orchestrator" > /dev/null; then
        echo "$(date): Orchestrator died, restarting..."
        tmux send-keys -t $TMUX_SESSION:0.1 C-c Enter
        sleep 2
        tmux send-keys -t $TMUX_SESSION:0.1 "claude chat < /home/<USER>/github/cc-v1/_dev-automation-beta/top-level-orchestrator/prompts/autonomous-orchestrator.txt 2>&1 | tee -a $LOG_DIR/orchestrator.log" Enter
    fi
    
    sleep 30
done
EOF

chmod +x "$ORCHESTRATOR_DIR/continuous-monitor.sh"

# Start the continuous monitor in background
nohup "$ORCHESTRATOR_DIR/continuous-monitor.sh" "$LOG_DIR" > "$LOG_DIR/monitor.log" 2>&1 &
MONITOR_PID=$!

echo ""
echo "✅ Autonomous Orchestrator Started!"
echo "==================================="
echo "PID: $MONITOR_PID (monitor process)"
echo "Logs: $LOG_DIR"
echo ""
echo "The system is now running FULLY AUTONOMOUSLY:"
echo "- Claude-flow swarm explores based on CLAUDE.md"
echo "- Orchestrator Claude keeps it continuously active"
echo "- Monitor ensures everything stays running"
echo "- NO HUMAN INTERVENTION REQUIRED"
echo ""
echo "View progress: tmux attach -t $TMUX_SESSION"
echo "Stop everything: tmux kill-session -t $TMUX_SESSION && kill $MONITOR_PID"
echo ""
echo "The system will run indefinitely until stopped."