#!/bin/bash
# Quick test of the orchestrator setup

echo "🧪 Testing Claude-Flow Orchestrator Components"
echo "============================================"

# Test 1: Check claude-flow is installed
echo ""
echo "1. Checking claude-flow installation..."
if command -v claude-flow &> /dev/null; then
    echo "✅ claude-flow is installed"
    claude-flow --version
else
    echo "❌ claude-flow not found. Install with: npm install -g claude-flow@alpha"
    exit 1
fi

# Test 2: Check claude CLI
echo ""
echo "2. Checking claude CLI..."
if command -v claude &> /dev/null; then
    echo "✅ claude CLI is installed"
else
    echo "❌ claude CLI not found. Please install claude CLI"
    exit 1
fi

# Test 3: Check tmux
echo ""
echo "3. Checking tmux..."
if command -v tmux &> /dev/null; then
    echo "✅ tmux is installed"
    tmux -V
else
    echo "❌ tmux not found. Install with: sudo apt-get install tmux"
    exit 1
fi

# Test 4: Test claude-flow swarm command
echo ""
echo "4. Testing claude-flow swarm command..."
echo "Running: claude-flow swarm 'test objective' --help"
claude-flow swarm --help

echo ""
echo "✅ All components present!"
echo ""
echo "To start the orchestrator:"
echo "  ./launch-orchestrator.sh"
echo ""
echo "To start with overnight monitoring:"
echo "  ./start-overnight-orchestrator.sh"