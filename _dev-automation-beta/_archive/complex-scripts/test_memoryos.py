#!/usr/bin/env python3
"""
Test script to actually verify MemoryOS functionality
"""

import sys
import os
from pathlib import Path

# Add the core directory to path
sys.path.insert(0, str(Path(__file__).parent / "core"))

# Import the automation engine (handle hyphenated filename)
import importlib.util
spec = importlib.util.spec_from_file_location("automation_engine", "core/automation-engine.py")
automation_engine = importlib.util.module_from_spec(spec)
spec.loader.exec_module(automation_engine)
DevAutomationEngine = automation_engine.DevAutomationEngine

def test_memoryos():
    print("🧪 Testing MemoryOS functionality...")
    
    try:
        engine = DevAutomationEngine()
        
        if not engine.memory_system:
            print("❌ MemoryOS not initialized")
            return False
        
        print("✅ MemoryOS initialized")
        
        # Test adding memory
        print("📝 Testing memory addition...")
        engine.memory_system.add_memory(
            user_input="What is the capital of France?", 
            agent_response="The capital of France is Paris."
        )
        print("✅ Memory added successfully")
        
        # Test retrieval
        print("🔍 Testing memory retrieval...")
        response = engine.memory_system.get_response("What did we just discuss about France?")
        print(f"📤 MemoryOS Response: {response[:200]}...")
        
        if "Paris" in response or "France" in response:
            print("✅ MemoryOS actually working - can recall previous conversation!")
            return True
        else:
            print("⚠️  MemoryOS responded but didn't recall the conversation properly")
            return False
            
    except Exception as e:
        print(f"❌ MemoryOS test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_memoryos()
    if success:
        print("\n🎉 MemoryOS is actually working!")
    else:
        print("\n💥 MemoryOS is NOT working properly")
    sys.exit(0 if success else 1)