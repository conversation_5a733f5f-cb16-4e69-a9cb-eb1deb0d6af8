#!/usr/bin/env python3
"""
Test script to verify MemoryOS memory retrieval across sessions
"""

import sys
import os
from pathlib import Path

# Add the core directory to path
sys.path.insert(0, str(Path(__file__).parent / "core"))

# Import the automation engine
import importlib.util
spec = importlib.util.spec_from_file_location("automation_engine", "core/automation-engine.py")
automation_engine = importlib.util.module_from_spec(spec)
spec.loader.exec_module(automation_engine)
DevAutomationEngine = automation_engine.DevAutomationEngine

def test_memory_retrieval_across_sessions():
    print("🧪 Testing MemoryOS memory retrieval across sessions...")
    
    try:
        # Session 1: Store some information
        print("\n📝 SESSION 1: Storing information...")
        engine1 = DevAutomationEngine()
        
        if not engine1.memory_system:
            print("❌ MemoryOS not initialized")
            return False
        
        # Add multiple memories to trigger mid-term storage
        for i in range(12):  # Exceed short-term capacity (10)
            engine1.memory_system.add_memory(
                user_input=f"Question {i}: What is the capital of country {i}?", 
                agent_response=f"The capital of country {i} is City{i}."
            )
        
        print("✅ Added 12 memories to trigger mid-term storage")
        
        # Force mid-term analysis to move memories from short to mid-term
        engine1.memory_system.force_mid_term_analysis()
        print("✅ Forced mid-term analysis")
        
        # Session 2: Try to retrieve information (new engine instance)
        print("\n🔍 SESSION 2: Testing retrieval in new session...")
        engine2 = DevAutomationEngine()
        
        response = engine2.memory_system.get_response("What did we discuss about countries and capitals?")
        print(f"📤 Response: {response}")
        
        # Check if it mentions any of the stored information
        if "capital" in response.lower() and any(f"city{i}" in response.lower() for i in range(12)):
            print("✅ MemoryOS retrieval working - found stored information!")
            return True
        else:
            print("❌ MemoryOS retrieval FAILED - no stored information found")
            print("🔍 Let's check what's in the retrieval logs...")
            return False
            
    except Exception as e:
        print(f"❌ Memory retrieval test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_memory_retrieval_across_sessions()
    if success:
        print("\n🎉 MemoryOS retrieval actually working!")
    else:
        print("\n💥 MemoryOS retrieval is NOT working properly")
    sys.exit(0 if success else 1)