#!/bin/bash

# Run claude-flow swarm with Claude CLI integration
echo "🐝 Starting continuous swarm execution with Claude CLI..."

while true; do
    echo "🚀 Launching swarm with Claude CLI..."
    npx claude-flow swarm "Read CLAUDE.md for priorities. Find and fix all TODOs, bugs, and test failures. Improve code quality. Work autonomously without asking questions." --claude --strategy development --max-agents 5 --parallel
    
    exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        echo "✅ Swarm completed successfully"
    else
        echo "⚠️  Swarm exited with code: $exit_code"
    fi
    
    echo "⏳ Restarting in 5 seconds..."
    sleep 5
done