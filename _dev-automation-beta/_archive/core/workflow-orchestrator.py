#!/usr/bin/env python3
"""
Workflow Orchestrator - Manages automated development workflows
Integrates with the automation engine to provide sequential task execution
"""

import os
import sys
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# Import our automation engine
import importlib.util
spec = importlib.util.spec_from_file_location("automation_engine", Path(__file__).parent / "automation-engine.py")
automation_engine = importlib.util.module_from_spec(spec)
spec.loader.exec_module(automation_engine)
DevAutomationEngine = automation_engine.DevAutomationEngine

class WorkflowOrchestrator:
    """
    Orchestrates multi-step development workflows using the automation engine
    """
    
    def __init__(self):
        self.engine = DevAutomationEngine()
        self.workflows = self._load_workflows()
        
        print(f"🎭 Workflow Orchestrator initialized")
        print(f"📋 Available workflows: {len(self.workflows)}")
    
    def _load_workflows(self) -> Dict[str, Any]:
        """Load predefined workflow configurations"""
        return {
            "bug-fix-cycle": {
                "name": "Complete Bug Fix Cycle",
                "description": "Analyze bug → Fix → Test → Deploy → Verify",
                "steps": [
                    {"template": "Analyze and Fix Bug", "wait_for_completion": True},
                    {"template": "Run Testing Suite", "wait_for_completion": True},
                    {"template": "Prepare Deployment", "wait_for_completion": True},
                    {"template": "Verify Deployment", "wait_for_completion": False}
                ]
            },
            "feature-development": {
                "name": "Feature Development Workflow",
                "description": "Analyze → Implement → Test → Deploy",
                "steps": [
                    {"template": "Analyze Current State", "wait_for_completion": True},
                    {"template": "Complete Frontend Integration", "wait_for_completion": True},
                    {"template": "Run Testing Suite", "wait_for_completion": True},
                    {"template": "Prepare Deployment", "wait_for_completion": False}
                ]
            },
            "deployment-pipeline": {
                "name": "Safe Deployment Pipeline", 
                "description": "Test → Prepare → Deploy → Verify",
                "steps": [
                    {"template": "Run Testing Suite", "wait_for_completion": True},
                    {"template": "Prepare Deployment", "wait_for_completion": True},
                    {"template": "Verify Deployment", "wait_for_completion": False}
                ]
            }
        }
    
    def execute_workflow(self, workflow_name: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a complete workflow"""
        if workflow_name not in self.workflows:
            return {"error": f"Workflow '{workflow_name}' not found"}
        
        workflow = self.workflows[workflow_name]
        context = context or {}
        
        print(f"🚀 Starting workflow: {workflow['name']}")
        print(f"📋 Description: {workflow['description']}")
        
        results = []
        workflow_success = True
        
        for i, step in enumerate(workflow['steps'], 1):
            template_name = step['template']
            wait_for_completion = step.get('wait_for_completion', True)
            
            print(f"\n🔄 Step {i}/{len(workflow['steps'])}: {template_name}")
            
            # Execute the template
            result = self.engine.execute_template(template_name, context)
            results.append({
                "step": i,
                "template": template_name,
                "result": result,
                "timestamp": datetime.now().isoformat()
            })
            
            if not result.get('success', False):
                print(f"❌ Step {i} failed: {result.get('error', 'Unknown error')}")
                workflow_success = False
                if wait_for_completion:
                    print("🛑 Workflow stopped due to failed step")
                    break
            else:
                print(f"✅ Step {i} completed successfully")
        
        # Update workflow position in state
        if workflow_success:
            new_position = f"completed-{workflow_name}"
        else:
            new_position = f"failed-{workflow_name}-step-{i}"
        
        self._update_workflow_state(new_position, workflow_name, results)
        
        return {
            "workflow": workflow_name,
            "success": workflow_success,
            "steps_completed": len(results),
            "total_steps": len(workflow['steps']),
            "results": results,
            "final_position": new_position
        }
    
    def _update_workflow_state(self, position: str, workflow: str, results: List[Dict]):
        """Update the workflow state in the automation engine"""
        try:
            # Get current state
            current_state = self.engine.state.copy()
            current_state['workflow_position'] = position
            current_state['last_workflow'] = workflow
            current_state['last_workflow_results'] = results[-3:] if results else []  # Keep last 3 results
            current_state['last_updated'] = datetime.now().isoformat()
            
            # Save updated state
            self.engine._save_state(current_state)
            self.engine.state = current_state
            
        except Exception as e:
            print(f"⚠️  Failed to update workflow state: {e}")
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """Get current workflow status"""
        state = self.engine.state
        
        return {
            "current_position": state.get('workflow_position', 'idle'),
            "last_workflow": state.get('last_workflow', 'none'),
            "available_workflows": list(self.workflows.keys()),
            "engine_status": self.engine.get_status()
        }
    
    def list_workflows(self) -> Dict[str, Any]:
        """List all available workflows"""
        return {
            workflow_id: {
                "name": config["name"],
                "description": config["description"],
                "steps": len(config["steps"]),
                "templates": [step["template"] for step in config["steps"]]
            }
            for workflow_id, config in self.workflows.items()
        }

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Workflow Orchestrator')
    parser.add_argument('--workflow', type=str, help='Execute specific workflow')
    parser.add_argument('--context', type=str, help='Context for workflow execution')
    parser.add_argument('--list', action='store_true', help='List available workflows')
    parser.add_argument('--status', action='store_true', help='Show workflow status')
    
    args = parser.parse_args()
    
    orchestrator = WorkflowOrchestrator()
    
    if args.list:
        workflows = orchestrator.list_workflows()
        print(f"\n📋 Available Workflows ({len(workflows)}):")
        for workflow_id, info in workflows.items():
            print(f"   • {info['name']} ({workflow_id})")
            print(f"     {info['description']}")
            print(f"     Steps: {' → '.join(info['templates'])}")
            print()
        return
    
    if args.status:
        status = orchestrator.get_workflow_status()
        print(f"\n📊 Workflow Status:")
        print(f"   Current Position: {status['current_position']}")
        print(f"   Last Workflow: {status['last_workflow']}")
        print(f"   Available: {', '.join(status['available_workflows'])}")
        return
    
    if args.workflow:
        context = {}
        if args.context:
            context['context'] = args.context
        
        result = orchestrator.execute_workflow(args.workflow, context)
        
        print(f"\n🎯 Workflow Results:")
        print(f"   Success: {result['success']}")
        print(f"   Steps: {result['steps_completed']}/{result['total_steps']}")
        print(f"   Final Position: {result['final_position']}")
        
        if result['success']:
            print("✅ Workflow completed successfully!")
        else:
            print("❌ Workflow failed or incomplete")
        
        return
    
    # Default: show help
    parser.print_help()

if __name__ == "__main__":
    main()