#!/usr/bin/env python3
"""
Analytics system for Development Automation Framework
Tracks template usage, success rates, and effectiveness metrics
"""

import sqlite3
import json
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

class AutomationAnalytics:
    """
    Analyzes automation framework usage and effectiveness
    """
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            self.db_path = Path(__file__).parent.parent / "memory" / "automation.db"
        else:
            self.db_path = Path(db_path)
        
        print(f"📊 Analytics system initialized")
        print(f"💾 Database: {self.db_path}")
    
    def get_template_usage_stats(self, days: int = 7) -> Dict[str, Any]:
        """Get template usage statistics for the last N days"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get usage stats
            cursor.execute('''
                SELECT 
                    template_name,
                    COUNT(*) as total_executions,
                    COUNT(CASE WHEN success = 1 THEN 1 END) as successful_executions,
                    COUNT(CASE WHEN success = 0 THEN 1 END) as failed_executions,
                    AVG(CASE WHEN success = 1 THEN 1.0 ELSE 0.0 END) as success_rate
                FROM execution_history 
                WHERE timestamp >= datetime('now', '-{} days')
                GROUP BY template_name
                ORDER BY total_executions DESC
            '''.format(days))
            
            results = cursor.fetchall()
            conn.close()
            
            stats = {}
            for row in results:
                template_name, total, successful, failed, success_rate = row
                stats[template_name] = {
                    "total_executions": total,
                    "successful_executions": successful,
                    "failed_executions": failed,
                    "success_rate": round(success_rate * 100, 1) if success_rate else 0
                }
            
            return {
                "period_days": days,
                "templates": stats,
                "summary": {
                    "total_templates_used": len(stats),
                    "total_executions": sum(s["total_executions"] for s in stats.values()),
                    "overall_success_rate": round(
                        (sum(s["successful_executions"] for s in stats.values()) / 
                         sum(s["total_executions"] for s in stats.values()) * 100)
                        if sum(s["total_executions"] for s in stats.values()) > 0 else 0, 1
                    )
                }
            }
            
        except Exception as e:
            return {"error": f"Failed to get template stats: {e}"}
    
    def get_execution_timeline(self, hours: int = 24) -> Dict[str, Any]:
        """Get execution timeline for the last N hours"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    template_name,
                    timestamp,
                    success
                FROM execution_history 
                WHERE timestamp >= datetime('now', '-{} hours')
                ORDER BY timestamp DESC
            '''.format(hours))
            
            results = cursor.fetchall()
            conn.close()
            
            timeline = []
            for row in results:
                template_name, timestamp, success = row
                timeline.append({
                    "template": template_name,
                    "timestamp": timestamp,
                    "success": bool(success),
                    "status": "✅ Success" if success else "❌ Failed"
                })
            
            return {
                "period_hours": hours,
                "total_executions": len(timeline),
                "timeline": timeline
            }
            
        except Exception as e:
            return {"error": f"Failed to get timeline: {e}"}
    
    def get_effectiveness_report(self) -> Dict[str, Any]:
        """Generate comprehensive effectiveness report"""
        
        # Get various time periods
        daily_stats = self.get_template_usage_stats(1)
        weekly_stats = self.get_template_usage_stats(7)
        monthly_stats = self.get_template_usage_stats(30)
        timeline = self.get_execution_timeline(24)
        
        # Identify most/least effective templates
        weekly_templates = weekly_stats.get("templates", {})
        if weekly_templates:
            most_used = max(weekly_templates.items(), key=lambda x: x[1]["total_executions"])
            highest_success = max(weekly_templates.items(), key=lambda x: x[1]["success_rate"])
            lowest_success = min(weekly_templates.items(), key=lambda x: x[1]["success_rate"])
        else:
            most_used = ("None", {"total_executions": 0})
            highest_success = ("None", {"success_rate": 0})
            lowest_success = ("None", {"success_rate": 0})
        
        return {
            "report_generated": datetime.now().isoformat(),
            "period_analysis": {
                "daily": daily_stats.get("summary", {}),
                "weekly": weekly_stats.get("summary", {}),
                "monthly": monthly_stats.get("summary", {})
            },
            "template_insights": {
                "most_used_template": {
                    "name": most_used[0],
                    "executions": most_used[1]["total_executions"]
                },
                "highest_success_rate": {
                    "name": highest_success[0],
                    "rate": highest_success[1]["success_rate"]
                },
                "needs_improvement": {
                    "name": lowest_success[0],
                    "rate": lowest_success[1]["success_rate"]
                } if lowest_success[1]["success_rate"] < 80 else None
            },
            "recent_activity": {
                "last_24h_executions": timeline.get("total_executions", 0),
                "recent_timeline": timeline.get("timeline", [])[:5]  # Last 5 executions
            },
            "recommendations": self._generate_recommendations(weekly_templates)
        }
    
    def _generate_recommendations(self, templates: Dict[str, Dict]) -> List[str]:
        """Generate improvement recommendations based on usage patterns"""
        recommendations = []
        
        if not templates:
            return ["No template usage data available for analysis"]
        
        # Check success rates
        low_success_templates = [
            name for name, stats in templates.items() 
            if stats["success_rate"] < 70 and stats["total_executions"] > 1
        ]
        
        if low_success_templates:
            recommendations.append(
                f"Review and improve templates with low success rates: {', '.join(low_success_templates)}"
            )
        
        # Check usage patterns
        unused_templates = []  # This would require comparing with available templates
        if unused_templates:
            recommendations.append(
                f"Consider removing or updating unused templates: {', '.join(unused_templates)}"
            )
        
        # Check for frequent failures
        high_failure_templates = [
            name for name, stats in templates.items()
            if stats["failed_executions"] > stats["successful_executions"] and stats["total_executions"] > 2
        ]
        
        if high_failure_templates:
            recommendations.append(
                f"Templates failing more than succeeding need attention: {', '.join(high_failure_templates)}"
            )
        
        if not recommendations:
            recommendations.append("Template performance looks good! Consider adding more specialized templates.")
        
        return recommendations

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Automation Analytics')
    parser.add_argument('--stats', type=int, default=7, help='Template usage stats for N days')
    parser.add_argument('--timeline', type=int, default=24, help='Execution timeline for N hours')
    parser.add_argument('--report', action='store_true', help='Generate effectiveness report')
    
    args = parser.parse_args()
    
    analytics = AutomationAnalytics()
    
    if args.report:
        report = analytics.get_effectiveness_report()
        
        print(f"\n📊 Automation Effectiveness Report")
        print(f"Generated: {report['report_generated']}")
        
        print(f"\n📈 Period Analysis:")
        for period, data in report['period_analysis'].items():
            if data:
                print(f"   {period.title()}: {data.get('total_executions', 0)} executions, "
                      f"{data.get('overall_success_rate', 0)}% success rate")
        
        print(f"\n🎯 Template Insights:")
        insights = report['template_insights']
        print(f"   Most Used: {insights['most_used_template']['name']} "
              f"({insights['most_used_template']['executions']} times)")
        print(f"   Highest Success: {insights['highest_success_rate']['name']} "
              f"({insights['highest_success_rate']['rate']}%)")
        
        if insights.get('needs_improvement'):
            print(f"   Needs Improvement: {insights['needs_improvement']['name']} "
                  f"({insights['needs_improvement']['rate']}%)")
        
        print(f"\n🕐 Recent Activity:")
        print(f"   Last 24h: {report['recent_activity']['last_24h_executions']} executions")
        
        print(f"\n💡 Recommendations:")
        for rec in report['recommendations']:
            print(f"   • {rec}")
        
        return
    
    if args.stats:
        stats = analytics.get_template_usage_stats(args.stats)
        
        if 'error' in stats:
            print(f"❌ {stats['error']}")
            return
        
        print(f"\n📊 Template Usage Stats (Last {args.stats} days)")
        print(f"Summary: {stats['summary']['total_executions']} executions, "
              f"{stats['summary']['overall_success_rate']}% success rate")
        
        print(f"\n📋 Individual Templates:")
        for template, data in stats['templates'].items():
            print(f"   • {template}: {data['total_executions']} executions, "
                  f"{data['success_rate']}% success rate")
        
        return
    
    if args.timeline:
        timeline = analytics.get_execution_timeline(args.timeline)
        
        if 'error' in timeline:
            print(f"❌ {timeline['error']}")
            return
        
        print(f"\n🕐 Execution Timeline (Last {args.timeline} hours)")
        print(f"Total: {timeline['total_executions']} executions")
        
        for execution in timeline['timeline'][:10]:  # Show last 10
            print(f"   {execution['timestamp']} - {execution['template']} - {execution['status']}")
        
        return
    
    # Default: show help
    parser.print_help()

if __name__ == "__main__":
    main()