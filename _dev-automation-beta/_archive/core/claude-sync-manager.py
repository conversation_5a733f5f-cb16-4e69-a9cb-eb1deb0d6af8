#!/usr/bin/env python3
"""
CLAUDE.md Synchronization Manager
Intelligent management of CLAUDE.md files across the codebase with workflow awareness
"""

import os
import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import yaml

class ClaudeSyncManager:
    """
    Manages CLAUDE.md synchronization with workflow state and context awareness
    """
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent.parent
        self.automation_db_path = self.project_root / "_dev-automation-beta" / "memory" / "automation.db"
        self.sync_state_path = self.project_root / "_dev-automation-beta" / "memory" / "claude-sync-state.json"
        self.context_memory_path = self.project_root / "_dev-automation-beta" / "memory" / "context-memory.json"
        
        # Initialize database connection
        self._init_database()
        
        # Load context mappings
        self.context_mappings = self._load_context_mappings()
        
        print(f"🔄 CLAUDE.md Sync Manager initialized")
        print(f"📁 Project root: {self.project_root}")
    
    def _init_database(self):
        """Initialize database connection and tables"""
        try:
            self.db = sqlite3.connect(str(self.automation_db_path))
            self.db.execute("""
                CREATE TABLE IF NOT EXISTS claude_sync_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    sync_type TEXT NOT NULL,
                    context_focus TEXT,
                    success BOOLEAN NOT NULL,
                    details TEXT
                )
            """)
            self.db.commit()
        except Exception as e:
            print(f"⚠️  Database initialization failed: {e}")
            self.db = None
    
    def _load_context_mappings(self) -> Dict[str, str]:
        """Load directory to context focus mappings"""
        return {
            '/frontend/components/features/operator/': 'operator-development',
            '/backend/src/services/operator/': 'operator-backend',
            '/frontend/components/features/assistant/': 'assistant-development',
            '/backend/src/services/ai/': 'ai-backend',
            '/_docs/TECHNICAL/': 'technical-documentation',
            '/_docs/GUIDES/': 'guide-documentation',
            '/_docs/FEATURES/': 'feature-documentation', 
            '/tests/': 'testing-development',
            '/scripts/': 'deployment-operations',
            '/_dev-automation-beta/': 'automation-framework',
            '/prisma/': 'database-development',
            '/frontend/components/shared/': 'shared-components',
            '/backend/src/trpc/': 'api-development'
        }
    
    def get_current_workflow_context(self) -> Dict[str, Any]:
        """Get current workflow context from automation state"""
        try:
            if not self.db:
                return {"error": "Database not available"}
            
            # Get latest template execution
            cursor = self.db.execute("""
                SELECT template_name, context, timestamp, status
                FROM template_executions 
                ORDER BY timestamp DESC 
                LIMIT 1
            """)
            latest_execution = cursor.fetchone()
            
            # Get active workflow state
            state_file = self.project_root / "_dev-automation-beta" / "memory" / "session-state.json"
            if state_file.exists():
                with open(state_file, 'r') as f:
                    session_state = json.load(f)
            else:
                session_state = {}
            
            return {
                "latest_template": {
                    "name": latest_execution[0] if latest_execution else "None",
                    "context": latest_execution[1] if latest_execution else "",
                    "timestamp": latest_execution[2] if latest_execution else "",
                    "status": latest_execution[3] if latest_execution else ""
                },
                "session_state": session_state,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": f"Failed to get workflow context: {e}"}
    
    def get_recent_experience_insights(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent experience insights from automation database"""
        try:
            if not self.db:
                return []
            
            cursor = self.db.execute("""
                SELECT template_name, insights, learnings, timestamp
                FROM experience_data
                ORDER BY timestamp DESC
                LIMIT ?
            """, (limit,))
            
            experiences = []
            for row in cursor.fetchall():
                experiences.append({
                    "template": row[0],
                    "insights": row[1],
                    "learnings": row[2],
                    "timestamp": row[3]
                })
            
            return experiences
            
        except Exception as e:
            print(f"⚠️  Failed to get experience insights: {e}")
            return []
    
    def determine_context_focus(self, current_path: str) -> str:
        """Determine context focus based on current path"""
        current_path = str(current_path)
        
        for path_pattern, focus in self.context_mappings.items():
            if path_pattern in current_path:
                return focus
        
        return 'general-development'
    
    def get_relevant_templates(self, context_focus: str) -> List[Dict[str, Any]]:
        """Get templates relevant to the current context focus"""
        template_mappings = {
            'operator-development': [
                'Complete Frontend Integration',
                'Analyze and Fix Bug',
                'Run Testing Suite'
            ],
            'operator-backend': [
                'Analyze and Fix Bug',
                'Run Testing Suite',
                'Prepare Deployment'
            ],
            'testing-development': [
                'Run Testing Suite',
                'Analyze Current State'
            ],
            'automation-framework': [
                'Capture Template Experience',
                'Update Lessons Learned',
                'Analyze Current State'
            ],
            'general-development': [
                'Analyze Current State',
                'Analyze and Fix Bug',
                'Run Testing Suite'
            ]
        }
        
        relevant_templates = template_mappings.get(context_focus, template_mappings['general-development'])
        
        # Load template details from index
        template_index_path = self.project_root / "_dev-automation-beta" / "TEMPLATE_INDEX.yaml"
        if template_index_path.exists():
            with open(template_index_path, 'r') as f:
                template_index = yaml.safe_load(f)
                
            detailed_templates = []
            for template_name in relevant_templates:
                # Find template details in index
                for category in template_index.get('templates', {}).values():
                    for template in category:
                        if template['name'] == template_name:
                            detailed_templates.append({
                                'name': template['name'],
                                'path': template['path'],
                                'complexity': template['complexity'],
                                'estimated_duration': template['estimated_duration'],
                                'success_rate': template['success_rate'],
                                'best_for': template['best_for']
                            })
            
            return detailed_templates
        
        return [{'name': name, 'path': 'unknown'} for name in relevant_templates]
    
    def generate_master_claude_md(self) -> str:
        """Generate master CLAUDE.md with full context"""
        try:
            # Load base AGENTS.md content
            agents_md_path = self.project_root / "AGENTS.md"
            if agents_md_path.exists():
                with open(agents_md_path, 'r') as f:
                    base_content = f.read()
            else:
                base_content = "# AI Development Guidelines\n\nAGENTS.md not found."
            
            # Get current workflow context
            workflow_context = self.get_current_workflow_context()
            
            # Get recent experience insights
            recent_insights = self.get_recent_experience_insights()
            
            # Get template usage statistics
            template_stats = self._get_template_usage_stats()
            
            # Generate enhanced content
            enhanced_content = f"""{base_content}

## 🔄 CURRENT DEVELOPMENT CONTEXT
**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

### Latest Template Execution
- **Template:** {workflow_context.get('latest_template', {}).get('name', 'None')}
- **Context:** {workflow_context.get('latest_template', {}).get('context', 'N/A')}
- **Status:** {workflow_context.get('latest_template', {}).get('status', 'N/A')}
- **Timestamp:** {workflow_context.get('latest_template', {}).get('timestamp', 'N/A')}

### Session State
- **Workflow Position:** {workflow_context.get('session_state', {}).get('workflow_position', 'Unknown')}
- **Priority:** {workflow_context.get('session_state', {}).get('priority', 'Unknown')}
- **Active Tasks:** {len(workflow_context.get('session_state', {}).get('active_tasks', []))} tasks

## 💡 RECENT EXPERIENCE INSIGHTS
"""
            
            # Add recent insights
            for i, insight in enumerate(recent_insights[:3], 1):
                enhanced_content += f"""
### Insight {i}: {insight['template']}
- **Key Learning:** {insight.get('learnings', 'No specific learning recorded')}
- **When:** {insight.get('timestamp', 'Unknown')}
"""
            
            # Add template usage statistics
            enhanced_content += f"""
## 📊 AUTOMATION FRAMEWORK STATUS
- **Total Template Executions:** {template_stats.get('total_executions', 0)}
- **Success Rate:** {template_stats.get('success_rate', 'Unknown')}
- **Most Used Template:** {template_stats.get('most_used', 'Unknown')}
- **Database Status:** {'✅ Connected' if self.db else '❌ Disconnected'}

## 🎯 QUICK ACTIONS
Based on current context and recent patterns:

1. **If experiencing TypeScript errors:** Use "Analyze and Fix Bug" template
2. **Before any deployment:** Use "Run Testing Suite" → "Prepare Deployment"
3. **After completing work:** Use "Update Lessons Learned" template
4. **For complex integrations:** Use "Complete Frontend Integration" template
5. **To understand current state:** Use "Analyze Current State" template

**Remember:** All complex multi-step tasks MUST use the automation framework templates. No ad-hoc development!
"""
            
            return enhanced_content
            
        except Exception as e:
            print(f"⚠️  Failed to generate master CLAUDE.md: {e}")
            return f"# Error generating CLAUDE.md\n\nError: {e}"
    
    def generate_context_specific_claude_md(self, location: str) -> str:
        """Generate location-specific CLAUDE.md"""
        try:
            context_focus = self.determine_context_focus(location)
            relevant_templates = self.get_relevant_templates(context_focus)
            
            # Load condensed base content
            base_summary = """# AI Development Guidelines (Context-Specific)

## Mandatory Automation Policy
- All complex multi-step tasks MUST use automation framework templates
- Always provide evidence-based verification, never assume fixes work
- Use systematic approaches instead of ad-hoc development
- Capture experience data after every template execution
"""
            
            context_content = f"""{base_summary}

## 📍 CURRENT LOCATION CONTEXT: {context_focus.upper()}
**Path:** {location}
**Focus Area:** {context_focus.replace('-', ' ').title()}

## 🎯 RELEVANT TEMPLATES FOR THIS CONTEXT
"""
            
            # Add relevant templates
            for template in relevant_templates:
                context_content += f"""
### {template['name']}
- **Complexity:** {template.get('complexity', 'Unknown')}
- **Duration:** {template.get('estimated_duration', 'Unknown')}
- **Success Rate:** {template.get('success_rate', 'Unknown')}
- **Best For:** {', '.join(template.get('best_for', ['General use']))}
"""
            
            context_content += f"""
## 🔧 QUICK ACTIONS FOR {context_focus.upper()}
"""
            
            # Add context-specific quick actions
            quick_actions = self._get_context_quick_actions(context_focus)
            for action in quick_actions:
                context_content += f"- {action}\n"
            
            context_content += f"""
## 💡 CONTEXT-SPECIFIC REMINDERS
- Current workflow state is preserved across sessions
- Use location-appropriate templates for best results
- All experience is captured and shared across the team
- Framework learns from every execution to improve future efficiency
"""
            
            return context_content
            
        except Exception as e:
            print(f"⚠️  Failed to generate context-specific CLAUDE.md: {e}")
            return f"# Error generating context-specific CLAUDE.md\n\nError: {e}"
    
    def _get_context_quick_actions(self, context_focus: str) -> List[str]:
        """Get quick actions specific to context focus"""
        actions_map = {
            'operator-development': [
                "Check tRPC import paths (common issue in operator components)",
                "Verify export vs import patterns",
                "Test component integration with `npm run test`",
                "Use 'Complete Frontend Integration' for complex operator features"
            ],
            'operator-backend': [
                "Check tRPC router definitions",
                "Verify database schema alignment",
                "Test API endpoints with actual requests",
                "Use 'Analyze and Fix Bug' for service issues"
            ],
            'testing-development': [
                "Always run TypeScript compilation check first",
                "Use 'Run Testing Suite' for comprehensive verification",
                "Check for flaky tests and document patterns",
                "Verify test coverage for critical paths"
            ],
            'automation-framework': [
                "Use 'Capture Template Experience' after template modifications",
                "Update template index when adding new templates",
                "Test template execution with various contexts",
                "Document automation opportunities discovered"
            ]
        }
        
        return actions_map.get(context_focus, [
            "Use 'Analyze Current State' to understand context",
            "Follow systematic approach with appropriate templates",
            "Always verify changes with concrete evidence",
            "Capture learnings for future reference"
        ])
    
    def _get_template_usage_stats(self) -> Dict[str, Any]:
        """Get template usage statistics from database"""
        try:
            if not self.db:
                return {}
            
            cursor = self.db.execute("SELECT COUNT(*) FROM template_executions")
            total = cursor.fetchone()[0] if cursor.fetchone() else 0
            
            cursor = self.db.execute("""
                SELECT 
                    template_name, 
                    COUNT(*) as usage_count,
                    AVG(CASE WHEN status = 'completed' THEN 1.0 ELSE 0.0 END) as success_rate
                FROM template_executions 
                GROUP BY template_name 
                ORDER BY usage_count DESC 
                LIMIT 1
            """)
            most_used = cursor.fetchone()
            
            return {
                'total_executions': total,
                'most_used': most_used[0] if most_used else 'Unknown',
                'success_rate': f"{most_used[2]*100:.1f}%" if most_used else 'Unknown'
            }
            
        except Exception as e:
            print(f"⚠️  Failed to get template stats: {e}")
            return {}
    
    def sync_claude_md_files(self, force: bool = False) -> Dict[str, bool]:
        """Sync all CLAUDE.md files in the project"""
        results = {}
        
        # Generate master CLAUDE.md
        master_content = self.generate_master_claude_md()
        master_path = self.project_root / "CLAUDE.md"
        
        try:
            with open(master_path, 'w') as f:
                f.write(master_content)
            results['CLAUDE.md'] = True
            self._log_sync('CLAUDE.md', 'master', 'general', True, "Master CLAUDE.md updated")
        except Exception as e:
            results['CLAUDE.md'] = False
            self._log_sync('CLAUDE.md', 'master', 'general', False, f"Error: {e}")
        
        # Update context-specific CLAUDE.md files
        context_locations = [
            ('_docs/CLAUDE.md', '_docs/'),
            ('_docs/FEATURES/CLAUDE.md', '_docs/FEATURES/'),
            ('_docs/GUIDES/CLAUDE.md', '_docs/GUIDES/'),
            ('_docs/TECHNICAL/CLAUDE.md', '_docs/TECHNICAL/'),
            ('_docs/operator-demo/CLAUDE.md', '_docs/operator-demo/')
        ]
        
        for claude_file, location in context_locations:
            try:
                context_content = self.generate_context_specific_claude_md(location)
                file_path = self.project_root / claude_file
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(file_path, 'w') as f:
                    f.write(context_content)
                    
                results[claude_file] = True
                context_focus = self.determine_context_focus(location)
                self._log_sync(claude_file, 'context-specific', context_focus, True, f"Context-specific CLAUDE.md updated")
                
            except Exception as e:
                results[claude_file] = False
                self._log_sync(claude_file, 'context-specific', 'unknown', False, f"Error: {e}")
        
        return results
    
    def _log_sync(self, file_path: str, sync_type: str, context_focus: str, success: bool, details: str):
        """Log sync operation to database"""
        if self.db:
            try:
                self.db.execute("""
                    INSERT INTO claude_sync_log (timestamp, file_path, sync_type, context_focus, success, details)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (datetime.now().isoformat(), file_path, sync_type, context_focus, success, details))
                self.db.commit()
            except Exception as e:
                print(f"⚠️  Failed to log sync operation: {e}")

def main():
    """Main function for command-line usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CLAUDE.md Synchronization Manager')
    parser.add_argument('--sync-all', action='store_true', help='Sync all CLAUDE.md files')
    parser.add_argument('--update-context', action='store_true', help='Update context from automation state')
    parser.add_argument('--location', help='Generate context-specific CLAUDE.md for location')
    parser.add_argument('--force', action='store_true', help='Force sync even if files are newer')
    
    args = parser.parse_args()
    
    sync_manager = ClaudeSyncManager()
    
    if args.sync_all:
        print("🔄 Syncing all CLAUDE.md files...")
        results = sync_manager.sync_claude_md_files(force=args.force)
        
        for file_path, success in results.items():
            status = "✅ Success" if success else "❌ Failed"
            print(f"{status}: {file_path}")
            
    elif args.update_context:
        print("🔄 Updating context from automation state...")
        context = sync_manager.get_current_workflow_context()
        print(f"Current workflow context: {context}")
        
    elif args.location:
        print(f"📍 Generating context-specific CLAUDE.md for: {args.location}")
        content = sync_manager.generate_context_specific_claude_md(args.location)
        print("Generated content preview:")
        print(content[:500] + "..." if len(content) > 500 else content)
        
    else:
        print("🔄 CLAUDE.md Sync Manager")
        print("Use --help for available options")

if __name__ == "__main__":
    main()