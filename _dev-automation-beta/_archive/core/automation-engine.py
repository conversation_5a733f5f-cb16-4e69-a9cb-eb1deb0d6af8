#!/usr/bin/env python3
"""
Development Automation Engine -beta
Inspired by MemoryOS architecture for CC-V1 project automation
"""

import os
import sys
import json
import yaml
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# Simple SQLite-based state management (no external dependencies)
import sqlite3

class DevAutomationEngine:
    """
    Main automation engine that orchestrates development workflows
    using structured prompt templates and persistent memory
    """
    
    def __init__(self, config_path: str = "config/automation-config.yaml"):
        self.base_path = Path(__file__).parent.parent
        self.config_path = self.base_path / config_path
        self.config = self._load_config()
        
        # Initialize simple SQLite-based state management
        self.db_path = self.base_path / "memory" / "automation.db"
        self.db_path.parent.mkdir(exist_ok=True)
        self._init_database()
        
        # Load current state
        self.state = self._load_state()
        
        print(f"🤖 Development Automation Engine initialized")
        print(f"📁 Base path: {self.base_path}")
        print(f"💾 State persistence: SQLite database")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load automation configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            # Create default config
            default_config = {
                'version': '0.1.0-beta',
                'project': 'cc-v1',
                'memory': {
                    'backend': 'sqlite',
                    'persist_state': True,
                    'learning_enabled': False
                },
                'templates': {
                    'base_path': 'templates',
                    'auto_discovery': True
                },
                'logging': {
                    'level': 'INFO',
                    'file': 'logs/execution.log'
                }
            }
            
            # Ensure config directory exists
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w') as f:
                yaml.dump(default_config, f, default_flow_style=False)
            
            return default_config
    
    def _init_database(self):
        """Initialize SQLite database for state persistence"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create tables
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS execution_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    template_name TEXT NOT NULL,
                    context TEXT,
                    success BOOLEAN NOT NULL,
                    result TEXT
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS session_state (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ SQLite database initialized")
        except Exception as e:
            print(f"⚠️  Database initialization failed: {e}")
            print("🔄 Continuing without persistent state")
    
    def _load_state(self) -> Dict[str, Any]:
        """Load current development state"""
        state_file = self.base_path / "memory" / "session-state.json"
        
        if state_file.exists():
            with open(state_file, 'r') as f:
                state = json.load(f)
        else:
            state = {
                'session_id': datetime.now().isoformat(),
                'current_context': 'demo-operator-integration',
                'active_tasks': [
                    'Connect live-demo components to main operator interface',
                    'Update operator dashboard routing for demo features',
                    'Update environment configuration for new services'
                ],
                'completed_tasks': [
                    'Set up parallel operator structure in CC-V1 backend',
                    'Migrate core Gemini services from demo',
                    'Migrate Twilio services with WebSocket handling',
                    'Port advanced audio processing pipeline'
                ],
                'workflow_position': 'frontend-integration',
                'priority': 'high',
                'last_updated': datetime.now().isoformat()
            }
            self._save_state(state)
        
        return state
    
    def _save_state(self, state: Dict[str, Any]):
        """Save current development state"""
        state['last_updated'] = datetime.now().isoformat()
        state_file = self.base_path / "memory" / "session-state.json"
        state_file.parent.mkdir(exist_ok=True)
        
        with open(state_file, 'w') as f:
            json.dump(state, f, indent=2)
    
    def get_available_templates(self) -> List[Dict[str, Any]]:
        """Discover available prompt templates"""
        templates_path = self.base_path / self.config['templates']['base_path']
        templates = []
        
        if templates_path.exists():
            for template_file in templates_path.rglob("*.yaml"):
                try:
                    with open(template_file, 'r') as f:
                        template = yaml.safe_load(f)
                        template['file_path'] = str(template_file)
                        templates.append(template)
                except Exception as e:
                    print(f"⚠️  Error loading template {template_file}: {e}")
        
        return templates
    
    def execute_template(self, template_name: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a specific prompt template"""
        templates = self.get_available_templates()
        template = next((t for t in templates if t.get('name') == template_name), None)
        
        if not template:
            return {'error': f'Template {template_name} not found'}
        
        # Merge context with current state
        execution_context = {
            **self.state,
            **(context or {}),
            'timestamp': datetime.now().isoformat(),
            'template': template_name
        }
        
        # Generate prompt with context
        prompt = self._generate_prompt(template, execution_context)
        
        # Store execution in database
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO execution_history (timestamp, template_name, context, success, result)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                template_name,
                json.dumps(execution_context),
                True,
                prompt[:500]  # Store first 500 chars of generated prompt
            ))
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"⚠️  Database storage failed: {e}")
        
        # Log execution
        self._log_execution(template_name, execution_context, prompt)
        
        # Add lessons learned suggestion for all templates
        next_steps = template.get('next_steps', [])
        if 'policy/update-lessons-learned' not in next_steps:
            next_steps.append('policy/update-lessons-learned')
        
        return {
            'success': True,
            'template': template_name,
            'prompt': prompt,
            'context': execution_context,
            'next_steps': next_steps,
            'suggestion': f"💡 After completing this task, consider running: 'Update Lessons Learned' to capture what worked/failed"
        }
    
    def _generate_prompt(self, template: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Generate final prompt from template and context"""
        prompt_template = template.get('execution', {}).get('prompt', '')
        
        # Add default parameters from template
        template_params = template.get('parameters', [])
        for param in template_params:
            param_name = param.get('name')
            if param_name and param_name not in context:
                context[param_name] = param.get('default', '')
        
        # Simple string formatting - could be enhanced with Jinja2
        try:
            prompt = prompt_template.format(**context)
        except KeyError as e:
            prompt = f"Template formatting error: Missing key {e}\\n\\nAvailable context keys: {list(context.keys())}\\n\\nOriginal template:\\n{prompt_template}"
        
        return prompt
    
    def _log_execution(self, template_name: str, context: Dict[str, Any], prompt: str):
        """Log template execution"""
        log_path = self.base_path / self.config['logging']['file']
        log_path.parent.mkdir(exist_ok=True)
        
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'template': template_name,
            'context_keys': list(context.keys()),
            'prompt_length': len(prompt),
            'success': True
        }
        
        with open(log_path, 'a') as f:
            f.write(json.dumps(log_entry) + '\\n')
    
    def run_workflow_loop(self, workflow_config: str):
        """Run templates in a loop until completion criteria met"""
        print(f"🔄 Starting workflow loop: {workflow_config}")
        
        # This would implement the actual workflow loop
        # For now, just execute the next logical template based on state
        
        current_context = self.state.get('current_context', 'demo-operator-integration')
        
        if self.state.get('workflow_position') == 'frontend-integration':
            next_template = 'Complete Frontend Integration'
        elif self.state.get('workflow_position') == 'configuration':
            next_template = 'Update Environment Configuration'
        else:
            next_template = 'Analyze Current State'
        
        result = self.execute_template(next_template, {'context': current_context})
        print(f"✅ Executed: {next_template}")
        print(f"📝 Generated prompt (first 200 chars): {result.get('prompt', '')[:200]}...")
        
        return result
    
    def get_status(self) -> Dict[str, Any]:
        """Get current automation status"""
        templates = self.get_available_templates()
        
        # Get execution count from database
        execution_count = 0
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM execution_history')
            execution_count = cursor.fetchone()[0]
            conn.close()
        except Exception:
            pass
        
        return {
            'session_id': self.state.get('session_id'),
            'current_context': self.state.get('current_context'),
            'workflow_position': self.state.get('workflow_position'),
            'active_tasks': len(self.state.get('active_tasks', [])),
            'completed_tasks': len(self.state.get('completed_tasks', [])),
            'available_templates': len(templates),
            'memory_backend': 'SQLite',
            'total_executions': execution_count,
            'last_updated': self.state.get('last_updated')
        }

def main():
    parser = argparse.ArgumentParser(description='Development Automation Engine -beta')
    parser.add_argument('--init', action='store_true', help='Initialize automation framework')
    parser.add_argument('--template', type=str, help='Execute specific template')
    parser.add_argument('--context', type=str, help='Execution context')
    parser.add_argument('--loop', type=str, help='Run workflow loop')
    parser.add_argument('--status', action='store_true', help='Show current status')
    parser.add_argument('--list-templates', action='store_true', help='List available templates')
    
    args = parser.parse_args()
    
    if args.init:
        print("🚀 Initializing Development Automation Framework...")
        engine = DevAutomationEngine()
        print("✅ Framework initialized successfully")
        return
    
    engine = DevAutomationEngine()
    
    if args.status:
        status = engine.get_status()
        print("\\n📊 Current Automation Status:")
        for key, value in status.items():
            print(f"   {key}: {value}")
        return
    
    if args.list_templates:
        templates = engine.get_available_templates()
        print(f"\\n📋 Available Templates ({len(templates)}):")
        for template in templates:
            print(f"   • {template.get('name', 'Unnamed')} ({template.get('category', 'general')})")
        return
    
    if args.template:
        context = {'context': args.context} if args.context else {}
        result = engine.execute_template(args.template, context)
        
        if result.get('success'):
            print(f"✅ Template executed: {args.template}")
            print(f"📝 Generated prompt:")
            print("-" * 80)
            print(result['prompt'])
            print("-" * 80)
        else:
            print(f"❌ Template execution failed: {result.get('error')}")
        return
    
    if args.loop:
        result = engine.run_workflow_loop(args.loop)
        print(f"🔄 Workflow loop completed")
        return
    
    # Default: show help
    parser.print_help()

if __name__ == "__main__":
    main()