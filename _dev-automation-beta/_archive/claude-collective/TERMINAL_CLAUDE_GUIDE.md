# Terminal Claude Orchestration Guide

## Overview

This system orchestrates multiple terminal-based Claude instances (`claude-cli` and `claude-flow`) to work collaboratively while keeping them alive overnight. It uses a vector store similar to Roo Code for semantic memory and context awareness.

## Key Differences from API-Based Approach

1. **Uses Your Claude Subscription** - No per-token API costs
2. **Terminal Persistence** - Keeps terminal instances alive with nudges
3. **Overnight Guardian** - NightWatch agent prevents timeouts
4. **Vector Memory** - Semantic search across code and interactions

## Architecture

```
┌─────────────────────────────────────────────────────────┐
│                Terminal Claude Orchestrator              │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐   │
│  │ Orchestrator│  │   Coder 1   │  │   Coder 2   │   │
│  │(claude-flow)│  │(claude-flow)│  │(claude-flow)│   │
│  └──────┬──────┘  └──────┬──────┘  └──────┬──────┘   │
│         │                 │                 │          │
│  ┌──────▼────────────────▼─────────────────▼──────┐   │
│  │          Vector Store (Qdrant/Chroma)           │   │
│  │        Semantic Memory & Code Quadrants         │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐   │
│  │  Reviewer   │  │ Researcher  │  │ NightWatch  │   │
│  │ (claude-cli)│  │ (claude-cli)│  │ (claude-cli)│   │
│  └─────────────┘  └─────────────┘  └─────────────┘   │
└─────────────────────────────────────────────────────────┘
```

## Prerequisites

### 1. Install Claude CLI Tools

```bash
# Install claude-cli (official Anthropic CLI)
pip install claude-cli

# Install claude-flow (with MCP support)
npm install -g claude-flow
```

### 2. Authenticate

```bash
# For claude-cli
claude auth login

# For claude-flow
claude-flow auth
```

### 3. Install Vector Store (Optional)

For Qdrant (recommended):
```bash
# Docker approach
docker run -p 6333:6333 qdrant/qdrant

# Or Python client only
pip install qdrant-client
```

For ChromaDB (alternative):
```bash
pip install chromadb
```

## Quick Start

### 1. Basic Start (No Vector Store)

```bash
cd _dev-automation-beta/claude-collective
python3 start-terminal-orchestration.py --no-vector
```

### 2. Full Start (With Vector Store)

```bash
# Start Qdrant first
docker run -d -p 6333:6333 -v ./qdrant_storage:/qdrant/storage qdrant/qdrant

# Start orchestration
python3 start-terminal-orchestration.py
```

### 3. Start with Demo Tasks

```bash
python3 start-terminal-orchestration.py --demo-tasks
```

## How It Works

### 1. Instance Spawning

The system spawns terminal Claude instances with specific roles:

- **Orchestrator** (1x claude-flow) - Routes tasks and coordinates
- **Coder** (2x claude-flow) - Implements features with MCP tools
- **Reviewer** (1x claude-cli) - Reviews code and quality
- **Researcher** (1x claude-cli) - Researches solutions
- **NightWatch** (1x claude-cli) - Keeps others alive overnight

### 2. Keep-Alive Mechanism

```
Every 60 seconds:
  - Check each instance's idle time
  - Send contextual nudges to idle instances
  - Read and process responses
  - Update heartbeat timestamps

Overnight (10pm - 6am):
  - More aggressive nudging (every 90 seconds)
  - NightWatch actively monitors others
  - Automatic restart of dead instances
```

### 3. Task Distribution

Tasks are distributed through filesystem:

```
terminal_state/
├── tasks/
│   ├── task_001.json    # Pending task
│   └── task_002.json    # Assigned task
├── status/
│   ├── orchestrator_status.json
│   └── coder_1_status.json
└── messages/
    └── inter_agent_messages.json
```

### 4. Vector Memory

Similar to Roo Code, the system uses vector embeddings for:

- **Code Understanding** - Semantic search across codebase
- **Context Retrieval** - Find relevant past interactions
- **Quadrant Organization** - Group code by domain (frontend/backend/etc)

## Task Format

Create task files in `terminal_state/tasks/`:

```json
{
  "id": "task_implement_auth",
  "type": "implement",
  "description": "Implement JWT authentication for the API",
  "priority": "high",
  "context": {
    "files": ["server/auth.ts", "server/middleware.ts"],
    "requirements": ["Use existing User model", "30-day token expiry"]
  }
}
```

## Monitoring

### Real-Time Status

The system shows status every minute:

```
📊 Terminal Claude Status - 2024-01-15T10:30:00
============================================================
🟢 orchestrator_abc123
   Role: orchestrator | Type: claude-flow
   ⚡ Idle: 0.5 min | Nudges: 2
   📋 Task: routing_analysis

🟢 coder_1_def456
   Role: coder | Type: claude-flow
   ⚡ Idle: 1.2 min | Nudges: 5
   📋 Task: implement_auth

🟢 nightwatch_ghi789
   Role: nightwatch | Type: claude-cli
   ⚡ Idle: 0.1 min | Nudges: 0
```

### File-Based Monitoring

```bash
# Watch task queue
watch -n 5 'ls -la terminal_state/tasks/*.json | grep -v completed'

# Monitor instance status
tail -f terminal_state/status/*_status.json

# View vector store metrics
cat terminal_state/vector_metrics.json
```

## Nudge Strategies

The system uses contextual nudges to keep instances engaged:

1. **Status Checks** - "What are you currently working on?"
2. **Progress Updates** - "Please provide a brief update on your progress."
3. **Blocker Queries** - "Are there any blockers or issues?"
4. **Focus Checks** - "What's your current focus area?"
5. **Insight Requests** - "Any interesting findings to share?"

## Overnight Operation

### Automatic Features

1. **NightWatch Guardian** - Dedicated instance for overnight monitoring
2. **Aggressive Nudging** - Every 90 seconds vs 5 minutes
3. **Auto-Restart** - Dead instances automatically replaced
4. **State Persistence** - All context saved to vector store

### Manual Overnight Start

```bash
# Start with explicit overnight mode
nohup python3 start-terminal-orchestration.py > overnight.log 2>&1 &

# Monitor logs
tail -f overnight.log
```

## Troubleshooting

### Instance Won't Start

```bash
# Check authentication
claude auth status
claude-flow auth status

# Verify installations
which claude
which claude-flow
```

### Vector Store Issues

```bash
# Test Qdrant connection
curl http://localhost:6333/collections

# Use fallback
python3 start-terminal-orchestration.py --no-vector
```

### Instance Keeps Dying

1. Check system resources (terminal processes use memory)
2. Reduce instance count: `--instances 3`
3. Check logs for specific errors
4. Ensure proper authentication

## Advanced Configuration

### Custom Roles

Edit `terminal_claude_orchestrator.py`:

```python
self.instance_config = {
    TerminalAgentRole.ORCHESTRATOR: (1, TerminalClaudeType.CLAUDE_FLOW),
    TerminalAgentRole.CODER: (3, TerminalClaudeType.CLAUDE_FLOW),  # More coders
    TerminalAgentRole.TESTER: (1, TerminalClaudeType.CLAUDE_CLI),   # Add tester
}
```

### Vector Store Tuning

```python
# In vector_quadrant_store.py
self.vector_dim = 1024  # Higher dimension embeddings
self.min_chunk_size = 200  # Larger chunks
self.max_chunk_size = 2000
```

### Nudge Frequency

```python
# In terminal_claude_orchestrator.py
if is_overnight:
    return idle_time > 60  # 1 minute overnight
else:
    return idle_time > 600  # 10 minutes during day
```

## Best Practices

1. **Start Small** - Begin with 3-4 instances
2. **Monitor Initially** - Watch the first hour closely
3. **Use Vector Store** - Significantly improves context awareness
4. **Regular Commits** - Instances can see git history with MCP
5. **Clear Task Descriptions** - Better routing and execution

## Integration with Existing System

The terminal orchestrator can work alongside the browser-based and API-based systems:

```python
# Hybrid approach
orchestrator = TerminalClaudeOrchestrator(base_path)  # Terminal instances
browser_orch = BrowserOrchestrator(base_path)        # Claude.ai tabs
api_orch = LangGraphOrchestrator(base_path)          # API-based

# Share vector store
shared_vector_store = VectorQuadrantStore(base_path)
```

This creates a powerful multi-modal Claude collective that leverages your subscription effectively!