<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claude Collective Orchestration Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/mermaid@10/dist/mermaid.min.js"></script>
    <style>
        .quadrant-card {
            transition: all 0.3s ease;
        }
        .quadrant-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Claude Collective Orchestration Dashboard</h1>
            <p class="text-gray-600">Real-time monitoring of agent network and quadrant memory system</p>
            <div class="mt-4 flex items-center space-x-4">
                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                    🟢 System Active
                </span>
                <span class="text-sm text-gray-500">Last Update: <span id="lastUpdate">Loading...</span></span>
            </div>
        </div>

        <!-- Agent Status Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">Orchestrator</h3>
                    <span class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></span>
                </div>
                <p class="text-sm text-gray-600">Tasks Completed: <span class="font-bold">0</span></p>
                <p class="text-sm text-gray-600">Status: <span class="text-green-600">Active</span></p>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">NightWatch</h3>
                    <span class="w-3 h-3 bg-blue-500 rounded-full"></span>
                </div>
                <p class="text-sm text-gray-600">Monitoring Active: <span class="font-bold">Yes</span></p>
                <p class="text-sm text-gray-600">Last Check: <span class="text-blue-600">2 min ago</span></p>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">Researcher</h3>
                    <span class="w-3 h-3 bg-gray-400 rounded-full"></span>
                </div>
                <p class="text-sm text-gray-600">Tasks Completed: <span class="font-bold">0</span></p>
                <p class="text-sm text-gray-600">Status: <span class="text-gray-600">Idle</span></p>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">Developer</h3>
                    <span class="w-3 h-3 bg-gray-400 rounded-full"></span>
                </div>
                <p class="text-sm text-gray-600">Tasks Completed: <span class="font-bold">0</span></p>
                <p class="text-sm text-gray-600">Status: <span class="text-gray-600">Idle</span></p>
            </div>
        </div>

        <!-- Quadrant Memory Visualization -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold mb-6">Quadrant Memory System</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <div class="quadrant-card bg-blue-50 rounded-lg p-6 border-2 border-blue-200">
                    <h3 class="text-lg font-semibold text-blue-800 mb-2">Frontend</h3>
                    <p class="text-sm text-blue-600 mb-2">UI, React, Next.js</p>
                    <div class="space-y-1">
                        <p class="text-xs text-gray-600">Memories: <span class="font-bold">0</span></p>
                        <p class="text-xs text-gray-600">Active Files: <span class="font-bold">0</span></p>
                    </div>
                </div>
                
                <div class="quadrant-card bg-green-50 rounded-lg p-6 border-2 border-green-200">
                    <h3 class="text-lg font-semibold text-green-800 mb-2">Backend</h3>
                    <p class="text-sm text-green-600 mb-2">APIs, Services, Database</p>
                    <div class="space-y-1">
                        <p class="text-xs text-gray-600">Memories: <span class="font-bold">0</span></p>
                        <p class="text-xs text-gray-600">Active Files: <span class="font-bold">0</span></p>
                    </div>
                </div>
                
                <div class="quadrant-card bg-purple-50 rounded-lg p-6 border-2 border-purple-200">
                    <h3 class="text-lg font-semibold text-purple-800 mb-2">Infrastructure</h3>
                    <p class="text-sm text-purple-600 mb-2">DevOps, Config, Deploy</p>
                    <div class="space-y-1">
                        <p class="text-xs text-gray-600">Memories: <span class="font-bold">0</span></p>
                        <p class="text-xs text-gray-600">Active Files: <span class="font-bold">0</span></p>
                    </div>
                </div>
                
                <div class="quadrant-card bg-yellow-50 rounded-lg p-6 border-2 border-yellow-200">
                    <h3 class="text-lg font-semibold text-yellow-800 mb-2">Documentation</h3>
                    <p class="text-sm text-yellow-600 mb-2">Docs, Tests, Examples</p>
                    <div class="space-y-1">
                        <p class="text-xs text-gray-600">Memories: <span class="font-bold">0</span></p>
                        <p class="text-xs text-gray-600">Active Files: <span class="font-bold">0</span></p>
                    </div>
                </div>
                
                <div class="quadrant-card bg-gray-50 rounded-lg p-6 border-2 border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Shared</h3>
                    <p class="text-sm text-gray-600 mb-2">Utils, Types, Common</p>
                    <div class="space-y-1">
                        <p class="text-xs text-gray-600">Memories: <span class="font-bold">0</span></p>
                        <p class="text-xs text-gray-600">Active Files: <span class="font-bold">0</span></p>
                    </div>
                </div>
                
                <div class="quadrant-card bg-orange-50 rounded-lg p-6 border-2 border-orange-200">
                    <h3 class="text-lg font-semibold text-orange-800 mb-2">External</h3>
                    <p class="text-sm text-orange-600 mb-2">Third-party, Integrations</p>
                    <div class="space-y-1">
                        <p class="text-xs text-gray-600">Memories: <span class="font-bold">0</span></p>
                        <p class="text-xs text-gray-600">Active Files: <span class="font-bold">0</span></p>
                    </div>
                </div>
            </div>
            
            <!-- Mermaid Diagram -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-4">Quadrant Relationships</h3>
                <div id="mermaidDiagram" class="mermaid">
                    graph TD
                        Frontend[Frontend]
                        Backend[Backend]
                        Infrastructure[Infrastructure]
                        Documentation[Documentation]
                        Shared[Shared]
                        External[External]
                        
                        Frontend --> Shared
                        Backend --> Shared
                        Frontend --> Backend
                        Infrastructure --> Backend
                        Documentation --> Frontend
                        Documentation --> Backend
                        Documentation --> Shared
                </div>
            </div>
        </div>

        <!-- Task Queue -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-2xl font-bold mb-4">Task Queue</h2>
            <div class="space-y-2">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <span class="text-sm font-medium">No active tasks</span>
                    <span class="text-xs text-gray-500">Queue empty</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({ startOnLoad: true });
        
        // Update timestamp
        function updateTimestamp() {
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
        }
        
        // Fetch and update data
        async function fetchData() {
            try {
                // In production, this would fetch from the actual API
                // For now, just update the timestamp
                updateTimestamp();
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        }
        
        // Initial load
        fetchData();
        
        // Refresh every 30 seconds
        setInterval(fetchData, 30000);
    </script>
</body>
</html>