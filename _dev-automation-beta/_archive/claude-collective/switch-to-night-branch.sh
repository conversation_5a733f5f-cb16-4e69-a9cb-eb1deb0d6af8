#!/bin/bash
# Manually switch to or create a nightly branch

cd /home/<USER>/github/cc-v1

# Get current date
DATE=$(date +%Y-%m-%d)
BRANCH_NAME="night_$DATE"

echo "🔀 Switching to nightly branch: $BRANCH_NAME"

# Check current branch
CURRENT_BRANCH=$(git branch --show-current)
echo "📍 Current branch: $CURRENT_BRANCH"

# Check for uncommitted changes
if [[ -n $(git status --porcelain) ]]; then
    echo "⚠️  Warning: You have uncommitted changes"
    echo ""
    git status --short
    echo ""
    read -p "Do you want to stash these changes? (y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git stash push -m "Auto-stash before switching to $BRANCH_NAME"
        echo "✅ Changes stashed"
    else
        echo "❌ Cannot switch branches with uncommitted changes"
        exit 1
    fi
fi

# Ensure we're up to date with develop
echo "📥 Updating from develop..."
git checkout develop
git pull origin develop

# Create or switch to nightly branch
if git show-ref --verify --quiet refs/heads/$BRANCH_NAME; then
    echo "🔄 Branch $BRANCH_NAME already exists, switching to it..."
    git checkout $BRANCH_NAME
else
    echo "🌙 Creating new nightly branch: $BRANCH_NAME"
    git checkout -b $BRANCH_NAME
fi

echo ""
echo "✅ Successfully switched to: $BRANCH_NAME"
echo "🔒 All changes will be isolated from develop"
echo ""
echo "Next steps:"
echo "1. The orchestrator will auto-commit changes to this branch"
echo "2. After 5 commits, a draft PR will be prepared"
echo "3. Review and merge PRs manually in the morning"