#!/usr/bin/env python3
"""
Base Agent class for Claude Collective async agents
"""

import asyncio
import json
import logging
import sys
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, Optional, Any

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import from core directory
from core.async_orchestrator import Task, AgentRole

@dataclass
class AgentConfig:
    """Configuration for an agent"""
    name: str
    role: AgentRole
    automation_path: Path
    state_path: Path
    log_path: Path
    orchestrator_url: str = "http://localhost:8100"

class BaseAgent(ABC):
    """Base class for all Claude Collective agents"""
    
    def __init__(self, config: AgentConfig):
        self.config = config
        self.running = False
        self.tasks_completed = 0
        self.last_task_time = None
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # State management
        self.state = self._load_state()
        
    def _setup_logging(self) -> logging.Logger:
        """Setup agent-specific logging"""
        log_file = self.config.log_path / f"{self.config.name}.log"
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        logger = logging.getLogger(self.config.name)
        logger.setLevel(logging.INFO)
        
        # File handler
        fh = logging.FileHandler(log_file)
        fh.setLevel(logging.INFO)
        
        # Console handler
        ch = logging.StreamHandler()
        ch.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        fh.setFormatter(formatter)
        ch.setFormatter(formatter)
        
        logger.addHandler(fh)
        logger.addHandler(ch)
        
        return logger
        
    def _load_state(self) -> Dict[str, Any]:
        """Load agent state from disk"""
        state_file = self.config.state_path / f"{self.config.name}_state.json"
        
        if state_file.exists():
            with open(state_file, 'r') as f:
                return json.load(f)
        
        return {
            "name": self.config.name,
            "role": self.config.role.value,
            "started": None,
            "tasks_completed": 0,
            "last_task": None
        }
        
    def _save_state(self):
        """Save agent state to disk"""
        state_file = self.config.state_path / f"{self.config.name}_state.json"
        state_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(state_file, 'w') as f:
            json.dump(self.state, f, indent=2)
            
    async def start(self):
        """Start the agent"""
        self.logger.info(f"Starting {self.config.name} agent with role {self.config.role.value}")
        self.running = True
        self.state["started"] = time.time()
        self._save_state()
        
        # Start main loop
        await self._main_loop()
        
    async def stop(self):
        """Stop the agent"""
        self.logger.info(f"Stopping {self.config.name} agent")
        self.running = False
        
    async def _main_loop(self):
        """Main agent loop"""
        while self.running:
            try:
                # Get next task
                task = await self._get_next_task()
                
                if task:
                    self.logger.info(f"Processing task: {task.id}")
                    
                    # Process task
                    result = await self.process_task(task)
                    
                    # Submit result
                    await self._submit_result(task, result)
                    
                    # Update state
                    self.tasks_completed += 1
                    self.last_task_time = time.time()
                    self.state["tasks_completed"] = self.tasks_completed
                    self.state["last_task"] = {
                        "id": task.id,
                        "type": task.type,
                        "completed_at": self.last_task_time
                    }
                    self._save_state()
                    
                else:
                    # No task available, wait briefly
                    await asyncio.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"Error in main loop: {e}", exc_info=True)
                await asyncio.sleep(5)  # Wait before retrying
                
    async def _get_next_task(self) -> Optional[Task]:
        """Get next task from orchestrator"""
        # This would connect to the orchestrator API
        # For now, return None to simulate no task available
        return None
        
    async def _submit_result(self, task: Task, result: str):
        """Submit task result to orchestrator"""
        # This would submit to the orchestrator API
        self.logger.info(f"Task {task.id} completed with result: {result[:100]}...")
        
    @abstractmethod
    async def process_task(self, task: Task) -> str:
        """Process a task - must be implemented by subclasses"""
        pass
        
    @abstractmethod
    def get_context_prompt(self) -> str:
        """Get role-specific context prompt - must be implemented by subclasses"""
        pass

# CLI runner
async def main():
    """Main entry point for running an agent"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Claude Collective Base Agent")
    parser.add_argument("--name", required=True, help="Agent name")
    parser.add_argument("--role", required=True, help="Agent role")
    parser.add_argument("--base-path", default="/home/<USER>/github/cc-v1/_dev-automation-beta")
    
    args = parser.parse_args()
    
    # This is a base class, can't be run directly
    print("Error: BaseAgent cannot be run directly. Use a specific agent implementation.")
    sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())