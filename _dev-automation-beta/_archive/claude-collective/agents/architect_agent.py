#!/usr/bin/env python3
"""
Architect Agent - High-level system design and planning
Inspired by Roo Code's architect role
"""

import asyncio
import subprocess
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from agents.base_agent import BaseAgent, AgentConfig
from core.async_orchestrator import Task, AgentRole

class ArchitectAgent(BaseAgent):
    """Architect agent for high-level planning and design"""
    
    def __init__(self, config: AgentConfig):
        super().__init__(config)
        self.role_prompt = self.get_context_prompt()
        
    def get_context_prompt(self) -> str:
        """Get architect-specific context prompt"""
        return """You are the Architect in the Claude Collective system.
        
Your responsibilities:
1. Analyze project requirements and create high-level designs
2. Break down complex problems into manageable tasks
3. Define system architecture and component interactions
4. Create technical specifications for other agents
5. Review and validate implementation approaches

When analyzing tasks:
- Focus on the big picture and system-wide implications
- Consider scalability, maintainability, and best practices
- Provide clear, actionable specifications
- Identify potential risks and edge cases
- Suggest optimal technology choices

Your output should be structured and detailed enough for other agents to implement."""

    async def process_task(self, task: Task) -> str:
        """Process an architecture/planning task"""
        self.logger.info(f"Architect processing task: {task.type}")
        
        # Prepare context with architect-specific prompt
        full_context = f"{self.role_prompt}\n\nTask: {task.context}"
        
        # Use automation engine for complex analysis
        cmd = [
            sys.executable,
            str(self.config.automation_path),
            "--template", "Analyze Current State",
            "--context", full_context
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10 minute timeout for complex analysis
            )
            
            if result.returncode == 0:
                return self._format_architecture_result(result.stdout)
            else:
                return f"Architecture analysis failed: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return "Architecture analysis timeout - task too complex"
        except Exception as e:
            return f"Architecture analysis error: {str(e)}"
            
    def _format_architecture_result(self, raw_result: str) -> str:
        """Format the result with architecture-specific structure"""
        return f"""## Architecture Analysis

{raw_result}

### Next Steps for Implementation
1. Developer agents should implement the specified components
2. QA should prepare test scenarios based on requirements
3. Researcher should gather additional context if needed

*Generated by Architect Agent*"""

# CLI runner
async def main():
    """Run the architect agent"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Claude Collective Architect Agent")
    parser.add_argument("--base-path", default="/home/<USER>/github/cc-v1/_dev-automation-beta")
    parser.add_argument("--name", default="architect_1")
    
    args = parser.parse_args()
    
    config = AgentConfig(
        name=args.name,
        role=AgentRole.ARCHITECT,
        automation_path=Path(args.base_path) / "core" / "automation-engine.py",
        state_path=Path(args.base_path) / "claude-collective" / "state" / "agents",
        log_path=Path(args.base_path) / "claude-collective" / "logs" / "agents"
    )
    
    agent = ArchitectAgent(config)
    await agent.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nArchitect agent stopped by user")