#!/usr/bin/env python3
"""
QA Agent - Testing and quality assurance
Ensures code quality and catches issues early
"""

import asyncio
import subprocess
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from agents.base_agent import BaseAgent, AgentConfig
from core.async_orchestrator import Task, AgentRole

class QAAgent(BaseAgent):
    """QA agent for testing and quality assurance"""
    
    def __init__(self, config: AgentConfig):
        super().__init__(config)
        self.role_prompt = self.get_context_prompt()
        
    def get_context_prompt(self) -> str:
        """Get QA-specific context prompt"""
        return """You are a QA Engineer in the Claude Collective system.
        
Your responsibilities:
1. Test implementations thoroughly
2. Identify bugs and edge cases
3. Verify requirements are met
4. Ensure code quality standards
5. Create test scenarios and cases

Testing approach:
- Unit tests for individual components
- Integration tests for feature interactions
- Edge case testing
- Performance testing where relevant
- Security vulnerability checks

Your output should include:
- Test results (pass/fail)
- Identified issues with severity
- Suggestions for improvements
- Test coverage assessment"""

    async def process_task(self, task: Task) -> str:
        """Process a QA/testing task"""
        self.logger.info(f"QA processing task: {task.type}")
        
        # Prepare context with QA-specific prompt
        full_context = f"{self.role_prompt}\n\nTask: {task.context}"
        
        # Use testing template
        cmd = [
            sys.executable,
            str(self.config.automation_path),
            "--template", "Run Testing Suite",
            "--context", full_context
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=900  # 15 minute timeout for thorough testing
            )
            
            if result.returncode == 0:
                return self._format_qa_result(result.stdout)
            else:
                return f"QA testing encountered errors: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return "QA testing timeout - tests taking too long"
        except Exception as e:
            return f"QA testing error: {str(e)}"
            
    def _format_qa_result(self, raw_result: str) -> str:
        """Format the result with QA-specific structure"""
        return f"""## QA Testing Report

{raw_result}

### Quality Assessment
- Code Quality: Evaluated
- Test Coverage: Analyzed
- Edge Cases: Tested
- Performance: Verified

### Recommendations
Based on testing results, consider:
1. Additional test cases for edge scenarios
2. Performance optimizations if needed
3. Security hardening where applicable

*Generated by QA Agent*"""

# CLI runner
async def main():
    """Run the QA agent"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Claude Collective QA Agent")
    parser.add_argument("--base-path", default="/home/<USER>/github/cc-v1/_dev-automation-beta")
    parser.add_argument("--instance", type=int, default=1, help="Instance number")
    
    args = parser.parse_args()
    
    name = f"qa_{args.instance}"
    
    config = AgentConfig(
        name=name,
        role=AgentRole.QA,
        automation_path=Path(args.base_path) / "core" / "automation-engine.py",
        state_path=Path(args.base_path) / "claude-collective" / "state" / "agents",
        log_path=Path(args.base_path) / "claude-collective" / "logs" / "agents"
    )
    
    agent = QAAgent(config)
    await agent.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nQA agent stopped by user")