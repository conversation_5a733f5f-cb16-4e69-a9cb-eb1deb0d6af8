#!/usr/bin/env python3
"""
Researcher Agent - Information gathering and context building
Inspired by Roo Code's researcher role
"""

import asyncio
import subprocess
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from agents.base_agent import BaseAgent, AgentConfig
from core.async_orchestrator import Task, AgentRole

class ResearcherAgent(BaseAgent):
    """Researcher agent for information gathering"""
    
    def __init__(self, config: AgentConfig):
        super().__init__(config)
        self.role_prompt = self.get_context_prompt()
        
    def get_context_prompt(self) -> str:
        """Get researcher-specific context prompt"""
        return """You are a Researcher in the Claude Collective system.
        
Your responsibilities:
1. Gather information about the codebase
2. Analyze existing implementations
3. Find relevant documentation and examples
4. Identify patterns and best practices
5. Provide context for other agents

Research approach:
- Search for similar implementations
- Analyze code structure and dependencies
- Find relevant documentation
- Identify potential issues or conflicts
- Summarize findings clearly

Your output should include:
- Key findings and insights
- Relevant code examples
- Documentation references
- Potential risks or considerations
- Recommendations for implementation"""

    async def process_task(self, task: Task) -> str:
        """Process a research task"""
        self.logger.info(f"Researcher processing task: {task.type}")
        
        # Prepare context with researcher-specific prompt
        full_context = f"{self.role_prompt}\n\nTask: {task.context}"
        
        # Use analysis template for research
        cmd = [
            sys.executable,
            str(self.config.automation_path),
            "--template", "Analyze Current State",
            "--context", full_context
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10 minute timeout
            )
            
            if result.returncode == 0:
                return self._format_research_result(result.stdout)
            else:
                return f"Research failed: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return "Research timeout - scope too broad"
        except Exception as e:
            return f"Research error: {str(e)}"
            
    def _format_research_result(self, raw_result: str) -> str:
        """Format the result with research-specific structure"""
        return f"""## Research Findings

{raw_result}

### Summary
Key insights from the research:
1. Existing patterns identified
2. Relevant implementations found
3. Potential challenges noted
4. Best practices documented

### Recommendations
Based on research, the team should:
1. Follow established patterns where applicable
2. Consider identified risks in implementation
3. Leverage existing code where possible
4. Document any deviations from standards

*Generated by Researcher Agent*"""

# CLI runner
async def main():
    """Run the researcher agent"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Claude Collective Researcher Agent")
    parser.add_argument("--base-path", default="/home/<USER>/github/cc-v1/_dev-automation-beta")
    parser.add_argument("--name", default="researcher_1")
    
    args = parser.parse_args()
    
    config = AgentConfig(
        name=args.name,
        role=AgentRole.RESEARCHER,
        automation_path=Path(args.base_path) / "core" / "automation-engine.py",
        state_path=Path(args.base_path) / "claude-collective" / "state" / "agents",
        log_path=Path(args.base_path) / "claude-collective" / "logs" / "agents"
    )
    
    agent = ResearcherAgent(config)
    await agent.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nResearcher agent stopped by user")