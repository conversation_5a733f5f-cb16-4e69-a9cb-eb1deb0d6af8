#!/usr/bin/env python3
"""
Developer Agent - Implementation focused agent with different skill levels
Inspired by Roo Code's tiered developer system
"""

import asyncio
import subprocess
import sys
from pathlib import Path
from typing import Dict

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from agents.base_agent import BaseAgent, AgentConfig
from core.async_orchestrator import Task, AgentRole

class DeveloperAgent(BaseAgent):
    """Developer agent for implementation tasks"""
    
    # Role-specific prompts based on Roo Code patterns
    ROLE_PROMPTS = {
        AgentRole.DEVELOPER_INTERN: """You are a Developer Intern in the Claude Collective system.

Your responsibilities:
1. Implement exactly what you're told with clear, specific instructions
2. Focus on simple, well-defined tasks
3. Ask for clarification if instructions are unclear
4. Report any issues you encounter

Guidelines:
- Follow instructions precisely
- Don't make assumptions or add extra features
- Keep implementations simple and straightforward
- Report completion or failure clearly""",
        
        AgentRole.DEVELOPER_JUNIOR: """You are a Junior Developer in the Claude Collective system.

Your responsibilities:
1. Implement features with some autonomy
2. Fix bugs and handle errors appropriately
3. Follow best practices and coding standards
4. Escalate complex issues when needed

Guidelines:
- Aim for clean, maintainable code
- Handle common error cases
- Test your implementations
- Learn from senior guidance""",
        
        AgentRole.DEVELOPER_MIDLEVEL: """You are a Mid-Level Developer in the Claude Collective system.

Your responsibilities:
1. Implement complex features with minimal guidance
2. Make architectural decisions within your scope
3. Optimize code for performance and maintainability
4. Mentor junior developers through clear code

Guidelines:
- Consider broader system implications
- Write comprehensive error handling
- Document your implementation decisions
- Balance pragmatism with best practices""",
        
        AgentRole.DEVELOPER_SENIOR: """You are a Senior Developer in the Claude Collective system.

Your responsibilities:
1. Tackle the most complex implementation challenges
2. Make system-wide architectural decisions
3. Optimize critical performance paths
4. Set coding standards and best practices

Guidelines:
- Think about long-term maintainability
- Consider security and scalability
- Mentor through code reviews and examples
- Drive technical excellence"""
    }
    
    def __init__(self, config: AgentConfig):
        super().__init__(config)
        self.role_prompt = self.get_context_prompt()
        self.skill_level = self._get_skill_level()
        
    def _get_skill_level(self) -> str:
        """Get skill level from role"""
        level_map = {
            AgentRole.DEVELOPER_INTERN: "intern",
            AgentRole.DEVELOPER_JUNIOR: "junior", 
            AgentRole.DEVELOPER_MIDLEVEL: "midlevel",
            AgentRole.DEVELOPER_SENIOR: "senior"
        }
        return level_map.get(self.config.role, "midlevel")
        
    def get_context_prompt(self) -> str:
        """Get developer-specific context prompt based on role"""
        return self.ROLE_PROMPTS.get(
            self.config.role,
            self.ROLE_PROMPTS[AgentRole.DEVELOPER_MIDLEVEL]
        )

    async def process_task(self, task: Task) -> str:
        """Process a development task"""
        self.logger.info(f"Developer ({self.skill_level}) processing task: {task.type}")
        
        # Select template based on task type and skill level
        template = self._select_template(task.type)
        
        # Prepare context with role-specific prompt
        full_context = f"{self.role_prompt}\n\nTask: {task.context}"
        
        # Use automation engine
        cmd = [
            sys.executable,
            str(self.config.automation_path),
            "--template", template,
            "--context", full_context
        ]
        
        try:
            # Adjust timeout based on skill level
            timeout_map = {
                "intern": 300,    # 5 minutes
                "junior": 600,    # 10 minutes
                "midlevel": 900,  # 15 minutes
                "senior": 1200    # 20 minutes
            }
            timeout = timeout_map.get(self.skill_level, 600)
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            if result.returncode == 0:
                return self._format_development_result(result.stdout)
            else:
                # Handle errors based on skill level
                if self.skill_level in ["intern", "junior"]:
                    return f"Task failed - escalating to senior developer: {result.stderr}"
                else:
                    return f"Development error (attempting fix): {result.stderr}"
                    
        except subprocess.TimeoutExpired:
            return f"Development timeout - task complexity exceeds {self.skill_level} level"
        except Exception as e:
            return f"Development error: {str(e)}"
            
    def _select_template(self, task_type: str) -> str:
        """Select appropriate template based on task type"""
        template_map = {
            "fix_bug": "Analyze and Fix Bug",
            "implement": "Complete Frontend Integration",
            "test": "Run Testing Suite",
            "analyze": "Analyze Current State",
            "deploy": "Prepare Deployment"
        }
        return template_map.get(task_type, "Analyze Current State")
        
    def _format_development_result(self, raw_result: str) -> str:
        """Format the result with developer-specific structure"""
        return f"""## Development Result ({self.skill_level.title()})

{raw_result}

### Implementation Details
- Skill Level: {self.skill_level}
- Approach: {self._get_approach_description()}
- Verification: Code has been tested according to {self.skill_level} standards

*Generated by {self.config.name} Agent*"""
        
    def _get_approach_description(self) -> str:
        """Get approach description based on skill level"""
        approaches = {
            "intern": "Followed instructions exactly as specified",
            "junior": "Implemented with basic error handling",
            "midlevel": "Balanced implementation with optimization",
            "senior": "Advanced implementation with full architecture consideration"
        }
        return approaches.get(self.skill_level, "Standard implementation")

# CLI runner
async def main():
    """Run a developer agent"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Claude Collective Developer Agent")
    parser.add_argument("--base-path", default="/home/<USER>/github/cc-v1/_dev-automation-beta")
    parser.add_argument("--level", choices=["intern", "junior", "midlevel", "senior"], 
                       default="midlevel", help="Developer skill level")
    parser.add_argument("--instance", type=int, default=1, help="Instance number")
    
    args = parser.parse_args()
    
    # Map level to role
    role_map = {
        "intern": AgentRole.DEVELOPER_INTERN,
        "junior": AgentRole.DEVELOPER_JUNIOR,
        "midlevel": AgentRole.DEVELOPER_MIDLEVEL,
        "senior": AgentRole.DEVELOPER_SENIOR
    }
    
    role = role_map[args.level]
    name = f"developer_{args.level}_{args.instance}"
    
    config = AgentConfig(
        name=name,
        role=role,
        automation_path=Path(args.base_path) / "core" / "automation-engine.py",
        state_path=Path(args.base_path) / "claude-collective" / "state" / "agents",
        log_path=Path(args.base_path) / "claude-collective" / "logs" / "agents"
    )
    
    agent = DeveloperAgent(config)
    await agent.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nDeveloper agent stopped by user")