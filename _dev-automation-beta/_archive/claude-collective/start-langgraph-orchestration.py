#!/usr/bin/env python3
"""
Start script for LangGraph-based orchestration
Integrates with existing claude-collective infrastructure
"""

import asyncio
import os
import sys
from pathlib import Path
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from claude_collective.core.langgraph_orchestrator import ClaudeLangGraphOrchestrator
from claude_collective.core.git_workflow import GitWorkflowManager
from claude_collective.core.logger_config import setup_logger

logger, _, _ = setup_logger('langgraph_starter')

async def main():
    """Main entry point for LangGraph orchestration"""
    
    # Get Claude API key
    claude_api_key = os.getenv("ANTHROPIC_API_KEY")
    if not claude_api_key:
        logger.error("ANTHROPIC_API_KEY environment variable not set")
        return
    
    # Initialize components
    base_path = Path(__file__).parent.parent
    cc_root = base_path.parent
    
    # Initialize git workflow (for night branch)
    git_manager = GitWorkflowManager(cc_root)
    branch_name = await git_manager.init_session()
    logger.info(f"Working on branch: {branch_name}")
    
    # Create orchestrator
    orchestrator = ClaudeLangGraphOrchestrator(base_path, claude_api_key)
    
    # Check for existing checkpoints to resume
    checkpoints = list(orchestrator.state_path.glob("*.db"))
    
    if checkpoints:
        logger.info(f"Found {len(checkpoints)} existing checkpoints")
        # Could implement logic to select and resume from checkpoint
        
    # Define initial tasks based on current system state
    initial_tasks = [
        {
            "id": "analyze_system",
            "type": "research",
            "description": "Analyze the current orchestration system and identify improvements",
            "priority": "high"
        },
        {
            "id": "design_integration",
            "type": "design",
            "description": "Design integration between LangGraph and existing claude-flow MCP tools",
            "priority": "high"
        },
        {
            "id": "implement_persistence",
            "type": "implement",
            "description": "Implement robust persistence layer for overnight operations",
            "priority": "medium"
        },
        {
            "id": "test_nudge_system",
            "type": "test",
            "description": "Test the nudge system for keeping agents active",
            "priority": "medium"
        }
    ]
    
    # Start orchestration
    logger.info("🚀 Starting LangGraph-based orchestration")
    logger.info(f"Initial tasks: {len(initial_tasks)}")
    
    try:
        await orchestrator.start_orchestration(initial_tasks)
    except KeyboardInterrupt:
        logger.info("Orchestration interrupted by user")
    except Exception as e:
        logger.error(f"Orchestration failed: {e}")
        raise
    
    # Commit any changes
    if await git_manager.has_changes():
        logger.info("Committing orchestration changes...")
        await git_manager.commit_changes(
            f"feat: LangGraph orchestration session {datetime.now().isoformat()}"
        )
    
    logger.info("✅ Orchestration session complete")

if __name__ == "__main__":
    asyncio.run(main())