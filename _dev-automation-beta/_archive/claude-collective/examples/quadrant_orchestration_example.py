#!/usr/bin/env python3
"""
Example: Quadrant-aware orchestration for CC-V1 project
Shows how agents coordinate based on code quadrants
"""

import asyncio
from pathlib import Path
from claude_collective.core.langgraph_orchestrator import ClaudeLangGraphOrchestrator
from claude_collective.core.quadrant_memory import Quadrant

async def main():
    """Demonstrate quadrant-aware agent orchestration"""
    
    # Initialize orchestrator
    orchestrator = ClaudeLangGraphOrchestrator(
        base_path=Path(__file__).parent.parent.parent,
        claude_api_key="your-api-key"
    )
    
    # Example: Multi-quadrant feature implementation
    feature_tasks = [
        {
            "id": "analyze_operator_ui",
            "type": "research",
            "quadrant": Quadrant.FRONTEND.value,
            "description": "Analyze the current operator UI implementation in app/operator",
            "priority": "high"
        },
        {
            "id": "design_websocket_layer",
            "type": "design", 
            "quadrant": Quadrant.BACKEND.value,
            "description": "Design WebSocket communication layer for real-time operator updates",
            "priority": "high",
            "depends_on": ["analyze_operator_ui"]
        },
        {
            "id": "implement_frontend_hooks",
            "type": "implement",
            "quadrant": Quadrant.FRONTEND.value,
            "description": "Implement React hooks for WebSocket connection in operator dashboard",
            "priority": "medium",
            "depends_on": ["design_websocket_layer"]
        },
        {
            "id": "implement_backend_handlers",
            "type": "implement",
            "quadrant": Quadrant.BACKEND.value,
            "description": "Implement Fastify WebSocket handlers for operator events",
            "priority": "medium",
            "depends_on": ["design_websocket_layer"]
        },
        {
            "id": "update_deployment_config",
            "type": "implement",
            "quadrant": Quadrant.INFRASTRUCTURE.value,
            "description": "Update Docker and K8s configs for WebSocket support",
            "priority": "medium",
            "depends_on": ["implement_backend_handlers"]
        },
        {
            "id": "document_integration",
            "type": "implement",
            "quadrant": Quadrant.DOCUMENTATION.value,
            "description": "Document the new WebSocket integration for operators",
            "priority": "low",
            "depends_on": ["implement_frontend_hooks", "implement_backend_handlers"]
        },
        {
            "id": "test_cross_quadrant",
            "type": "test",
            "quadrant": Quadrant.SHARED.value,
            "description": "Test end-to-end WebSocket communication across all components",
            "priority": "high",
            "depends_on": ["implement_frontend_hooks", "implement_backend_handlers", "update_deployment_config"]
        }
    ]
    
    # Pre-populate some quadrant context
    memory = orchestrator.quadrant_memory
    
    # Frontend context
    memory.store_memory(
        content="Operator UI uses Next.js 15 App Router with real-time requirements",
        quadrant=Quadrant.FRONTEND,
        file_path="app/operator/page.tsx",
        metadata={"component": "OperatorDashboard", "tech": "Next.js"}
    )
    
    # Backend context  
    memory.store_memory(
        content="Fastify server with tRPC routers, needs WebSocket upgrade path",
        quadrant=Quadrant.BACKEND,
        file_path="server/api/index.ts",
        metadata={"framework": "Fastify", "protocol": "tRPC"}
    )
    
    # Infrastructure context
    memory.store_memory(
        content="K8s deployment uses nginx ingress, needs WebSocket proxy config",
        quadrant=Quadrant.INFRASTRUCTURE,
        file_path="k8s/ingress.yaml",
        metadata={"platform": "Kubernetes", "ingress": "nginx"}
    )
    
    # Start orchestration
    print("🚀 Starting quadrant-aware orchestration")
    print(f"📋 Tasks across {len(set(t['quadrant'] for t in feature_tasks))} quadrants")
    
    await orchestrator.start_orchestration(feature_tasks)
    
    # After orchestration, show quadrant insights
    insights = memory.get_cross_quadrant_insights()
    
    print("\n📊 Quadrant Activity Report:")
    for quadrant, activity in insights["quadrant_activity"].items():
        print(f"  {quadrant}: {activity['total_memories']} memories, {activity['active_files']} active files")
        
    print("\n🔗 Cross-Quadrant Dependencies:")
    for ref, count in insights["cross_references"].items():
        print(f"  {ref}: {count} references")
        
    print("\n🔥 Hot Spots (Most accessed memories):")
    for hot_spot in insights["hot_spots"][:5]:
        print(f"  [{hot_spot['quadrant']}] {hot_spot['summary']} (accessed {hot_spot['access_count']} times)")

if __name__ == "__main__":
    asyncio.run(main())