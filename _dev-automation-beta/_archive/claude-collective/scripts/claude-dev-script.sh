#!/bin/bash
cd /home/<USER>/github/cc-v1
export CLAUDE_ROLE="DEVELOPER"
export STATE_FILE="_dev-automation-beta/claude-collective/state/dev/state.json"
export LOG_FILE="_dev-automation-beta/claude-collective/logs/dev/$(date +%Y%m%d_%H%M%S).log"

echo '{"role":"DEV","started":"'$(date)'","tasks_completed":0}' > $STATE_FILE

while true; do
    if read -t 300 task < _dev-automation-beta/claude-collective/pipes/claude-pm-to-dev; then
        echo "=== DEV RECEIVED TASK: $task ===" | tee -a $LOG_FILE
        
        # Execute task using automation framework
        result=$(python3 _dev-automation-beta/core/automation-engine.py \
            --template "Analyze and Fix Bug" \
            --context "DEV: Execute this task: $task" 2>&1)
        
        echo "DEV COMPLETED: $result" | tee -a $LOG_FILE
        
        # Send to QA
        echo "TASK: $task | RESULT: $result" > _dev-automation-beta/claude-collective/pipes/claude-dev-to-qa
        
        # Update state
        tasks=$(cat $STATE_FILE | grep -o '"tasks_completed":[0-9]*' | cut -d: -f2)
        tasks=$((tasks + 1))
        echo "{\"role\":\"DEV\",\"started\":\"$(date)\",\"tasks_completed\":$tasks,\"last_task_completed\":\"$(date)\"}" > $STATE_FILE
    fi
    sleep 30
done