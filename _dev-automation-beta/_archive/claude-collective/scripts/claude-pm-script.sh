#!/bin/bash
cd /home/<USER>/github/cc-v1
export CLAUDE_ROLE="PROJECT_MANAGER"
export STATE_FILE="_dev-automation-beta/claude-collective/state/pm/state.json"
export LOG_FILE="_dev-automation-beta/claude-collective/logs/pm/$(date +%Y%m%d_%H%M%S).log"

# Initialize state
echo '{"role":"PM","started":"'$(date)'","cycle":0}' > $STATE_FILE

while true; do
    echo "=== PM CYCLE $(date) ===" | tee -a $LOG_FILE
    
    # Use automation framework
    task=$(python3 _dev-automation-beta/core/automation-engine.py \
        --template "Analyze Current State" \
        --context "PM: Generate specific actionable task for developer" 2>&1)
    
    echo "GENERATED TASK: $task" | tee -a $LOG_FILE
    echo "$task" > _dev-automation-beta/claude-collective/pipes/claude-pm-to-dev
    
    # Wait for QA feedback with timeout
    if read -t 300 qa_report < _dev-automation-beta/claude-collective/pipes/claude-qa-to-pm; then
        echo "QA REPORT RECEIVED: $qa_report" | tee -a $LOG_FILE
        
        # Process feedback and plan next cycle
        python3 _dev-automation-beta/core/automation-engine.py \
            --template "Complete Frontend Integration" \
            --context "PM: QA reported: $qa_report. Plan next development sprint." >> $LOG_FILE 2>&1
    fi
    
    # Update cycle counter
    cycle=$(jq '.cycle + 1' $STATE_FILE 2>/dev/null || echo 1)
    echo "{\"role\":\"PM\",\"started\":\"$(date)\",\"cycle\":$cycle,\"last_update\":\"$(date)\"}" > $STATE_FILE
    
    sleep 120  # 2 minute cycles
done