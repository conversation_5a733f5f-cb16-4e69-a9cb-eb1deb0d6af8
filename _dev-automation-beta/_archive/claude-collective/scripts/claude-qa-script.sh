#!/bin/bash
cd /home/<USER>/github/cc-v1
export CLAUDE_ROLE="QA"
export STATE_FILE="_dev-automation-beta/claude-collective/state/qa/state.json"
export LOG_FILE="_dev-automation-beta/claude-collective/logs/qa/$(date +%Y%m%d_%H%M%S).log"

echo '{"role":"QA","started":"'$(date)'","tests_run":0}' > $STATE_FILE

while true; do
    if read -t 300 dev_work < _dev-automation-beta/claude-collective/pipes/claude-dev-to-qa; then
        echo "=== QA TESTING: $dev_work ===" | tee -a $LOG_FILE
        
        # Test using automation framework
        qa_result=$(python3 _dev-automation-beta/core/automation-engine.py \
            --template "Run Testing Suite" \
            --context "QA: Test this work: $dev_work" 2>&1)
        
        echo "QA RESULT: $qa_result" | tee -a $LOG_FILE
        
        # Report back to PM
        echo "$qa_result" > _dev-automation-beta/claude-collective/pipes/claude-qa-to-pm
        
        # Update state
        tests=$(cat $STATE_FILE | grep -o '"tests_run":[0-9]*' | cut -d: -f2 2>/dev/null || echo 0)
        tests=$((tests + 1))
        echo "{\"role\":\"QA\",\"started\":\"$(date)\",\"tests_run\":$tests,\"last_test_completed\":\"$(date)\"}" > $STATE_FILE
    fi
    sleep 45
done