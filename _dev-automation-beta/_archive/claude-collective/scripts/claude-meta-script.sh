#!/bin/bash
cd /home/<USER>/github/cc-v1
export CLAUDE_ROLE="META_ORCHESTRATOR"
export STATE_FILE="_dev-automation-beta/claude-collective/state/meta/state.json"
export LOG_FILE="_dev-automation-beta/claude-collective/logs/meta/$(date +%Y%m%d_%H%M%S).log"

echo '{"role":"META","started":"'$(date)'","restarts":0,"optimizations":0}' > $STATE_FILE

while true; do
    echo "=== META ORCHESTRATOR CYCLE $(date) ===" | tee -a $LOG_FILE
    
    # Monitor instance health
    for role in pm dev qa; do
        pane_id=$((role == "pm" ? 0 : role == "dev" ? 1 : 2))
        if ! tmux capture-pane -t claude-collective:0.$pane_id -p 2>/dev/null | grep -q "CYCLE\|RECEIVED\|TESTING"; then
            echo "RESTARTING $role instance" | tee -a $LOG_FILE
            tmux respawn-pane -t claude-collective:0.$pane_id "_dev-automation-beta/claude-collective/scripts/claude-${role}-script.sh" 2>/dev/null || echo "Failed to restart $role"
            
            # Update restart counter
            restarts=$(cat $STATE_FILE | grep -o '"restarts":[0-9]*' | cut -d: -f2 2>/dev/null || echo 0)
            restarts=$((restarts + 1))
            echo "{\"role\":\"META\",\"started\":\"$(date)\",\"restarts\":$restarts,\"optimizations\":0,\"last_restart\":\"$(date)\"}" > $STATE_FILE
        fi
    done
    
    # Self-optimize based on logs
    python3 _dev-automation-beta/core/automation-engine.py \
        --template "Analyze Current State" \
        --context "META: Optimize collective performance based on recent logs" >> $LOG_FILE 2>&1
    
    # Check for completion signals
    if grep -q "PROJECT_COMPLETE" CLAUDE.md 2>/dev/null; then
        echo "PROJECT COMPLETION DETECTED - SCALING DOWN" | tee -a $LOG_FILE
        break
    fi
    
    sleep 300  # 5 minute meta-cycles
done