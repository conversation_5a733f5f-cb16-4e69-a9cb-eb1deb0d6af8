# Claude Collective v1 Deployment Guide

## Overview

This guide covers the deployment of the Claude Collective v1 orchestration system, which includes:
- LangGraph-based agent orchestration
- Quadrant memory system for context awareness
- Persistent agent sessions with checkpointing
- Overnight automation capabilities

## Prerequisites

1. **Python 3.8+** installed
2. **Anthropic API Key** for Claude agents
3. **Node.js 18+** for the frontend integration
4. **Docker** (optional, for containerized deployment)

## Quick Start

### 1. <PERSON><PERSON> and Navigate

```bash
cd /path/to/cc-v1
cd _dev-automation-beta/claude-collective
```

### 2. Set Up Environment

```bash
# Copy environment template
cp ../../.env.example ../../.env

# Edit .env and add your API keys:
# - ANTHROPIC_API_KEY=sk-ant-your-key-here
# - OPENAI_API_KEY=sk-your-key-here (for embeddings)
```

### 3. Run Deployment Script

```bash
# Basic deployment
./deploy-orchestration.sh

# With systemd service for overnight automation
./deploy-orchestration.sh --with-service
```

## Manual Deployment Steps

### 1. Install Dependencies

```bash
pip install -r requirements-langgraph.txt
```

### 2. Initialize Quadrant Memory

```bash
python3 - <<EOF
from core.quadrant_memory import QuadrantMemorySystem
from pathlib import Path

memory = QuadrantMemorySystem(Path.cwd().parent.parent)
print("Quadrant memory initialized")
EOF
```

### 3. Start Orchestration

```bash
python3 start-langgraph-orchestration.py
```

### 4. Launch Monitoring Dashboard

```bash
# In a separate terminal
./start-monitoring.sh
# Access at http://localhost:8200
```

## Frontend Integration

The live demo components are already integrated into the operator dashboard:

1. **Main Integration**: `/operator` page includes live-demo tab
2. **Standalone Route**: `/operator/live-demo` for direct access
3. **Components Location**: `frontend/components/features/operator/live-demo/`

### Accessing Live Demo Features

1. Navigate to the operator dashboard
2. Click on "Live Demo" tab
3. Features available:
   - Outbound call interface
   - Incoming call handling
   - Provider health monitoring
   - Audio testing tools

## Configuration

### Environment Variables

Key variables in `.env`:

```bash
# LangGraph Orchestration
ENABLE_LANGGRAPH_ORCHESTRATION=true
LANGGRAPH_MAX_AGENTS=10
LANGGRAPH_HEARTBEAT_INTERVAL=60

# Claude Models
CLAUDE_MODEL_ORCHESTRATOR=claude-3-opus-20240229
CLAUDE_MODEL_WORKER=claude-3-sonnet-20240229

# Quadrant Memory
ENABLE_QUADRANT_MEMORY=true
QUADRANT_MEMORY_RETENTION_DAYS=90

# Overnight Automation
ENABLE_OVERNIGHT_AUTOMATION=true
OVERNIGHT_START_HOUR=22
OVERNIGHT_END_HOUR=6
```

### Agent Configuration

Agents are configured in `core/langgraph_orchestrator.py`:
- **Orchestrator**: Main coordinator (Opus model)
- **NightWatch**: Overnight monitor (Sonnet model)
- **Researcher**: Information gathering (Opus model)
- **Developer**: Implementation tasks (Opus model)
- **QA**: Testing and validation (Sonnet model)

## Monitoring

### Dashboard Access

```bash
# Start monitoring server
cd dashboard
python3 -m http.server 8200
```

Access at: http://localhost:8200

### Quadrant Visualization

```bash
# Generate quadrant report
python3 tools/quadrant_visualizer.py
```

Reports saved to: `reports/quadrant_report_*.json`

### Logs

All logs are stored in `logs/` directory:
- `orchestrator.log` - Main orchestration logs
- `swarm_mode.log` - Swarm coordination logs
- `quadrant_memory.log` - Memory system logs

## Production Deployment

### Using Docker

```dockerfile
FROM python:3.10-slim

WORKDIR /app

COPY requirements-langgraph.txt .
RUN pip install -r requirements-langgraph.txt

COPY . .

CMD ["python3", "start-langgraph-orchestration.py"]
```

### Using Kubernetes

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: claude-orchestrator
spec:
  replicas: 1
  template:
    spec:
      containers:
      - name: orchestrator
        image: claude-collective:v1
        env:
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: anthropic
```

### Using PM2

```bash
pm2 start start-langgraph-orchestration.py --name claude-orchestrator
pm2 save
pm2 startup
```

## Troubleshooting

### Common Issues

1. **No API Key Error**
   - Ensure `ANTHROPIC_API_KEY` is set in `.env`
   - Check environment variable is loaded: `echo $ANTHROPIC_API_KEY`

2. **Import Errors**
   - Install dependencies: `pip install -r requirements-langgraph.txt`
   - Check Python path: `python3 -c "import sys; print(sys.path)"`

3. **Dashboard Not Loading**
   - Check port 8200 is not in use
   - Ensure dashboard files exist in `dashboard/` directory

### Debug Mode

Enable debug logging:

```bash
export DEBUG=true
python3 start-langgraph-orchestration.py
```

## Maintenance

### Backup Checkpoints

```bash
# Backup LangGraph checkpoints
tar -czf backups/checkpoints-$(date +%Y%m%d).tar.gz data/langgraph/

# Backup quadrant memories
tar -czf backups/quadrants-$(date +%Y%m%d).tar.gz data/quadrants/
```

### Clean Old Data

```bash
# Remove old checkpoints (older than 30 days)
find data/langgraph -name "*.db" -mtime +30 -delete

# Clean old logs
find logs -name "*.log" -mtime +7 -delete
```

## Security Considerations

1. **API Keys**: Never commit API keys to version control
2. **Network**: Use HTTPS for all external communications
3. **Access Control**: Restrict dashboard access in production
4. **Audit Logs**: Enable comprehensive logging for compliance

## Support

For issues or questions:
1. Check logs in `logs/` directory
2. Review quadrant reports for memory issues
3. Monitor agent status in dashboard
4. Check systemd service status: `sudo systemctl status claude-orchestrator`

## Version History

- **v1.0** - Initial release with LangGraph and quadrant memory
- **v0.1** - Python subprocess prototype (deprecated)