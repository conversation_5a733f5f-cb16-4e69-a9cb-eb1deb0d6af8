#!/bin/bash
# Deploy Claude Collective v1 Orchestration System

set -e

echo "🚀 Deploying Claude Collective v1 Orchestration"
echo "============================================="

# Check for required environment variables
if [ -z "$ANTHROPIC_API_KEY" ]; then
    echo "❌ Error: ANTHROPIC_API_KEY environment variable is not set"
    echo "Please set it in your .env file or export it"
    exit 1
fi

# Base paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
COLLECTIVE_DIR="$SCRIPT_DIR"

# Create required directories
echo "📁 Creating required directories..."
mkdir -p "$PROJECT_ROOT/data/langgraph"
mkdir -p "$PROJECT_ROOT/data/quadrants"
mkdir -p "$COLLECTIVE_DIR/state/orchestrator"
mkdir -p "$COLLECTIVE_DIR/logs"
mkdir -p "$COLLECTIVE_DIR/reports"

# Install Python dependencies
echo "📦 Installing Python dependencies..."
cd "$COLLECTIVE_DIR"
pip install -r requirements-langgraph.txt --quiet

# Copy environment configuration
echo "🔧 Setting up environment configuration..."
if [ ! -f "$PROJECT_ROOT/.env" ]; then
    cp "$PROJECT_ROOT/.env.example" "$PROJECT_ROOT/.env"
    echo "⚠️  Created .env file from .env.example"
    echo "   Please update with your actual API keys"
fi

# Create systemd service for overnight automation (optional)
if [ "$1" == "--with-service" ]; then
    echo "🔧 Creating systemd service..."
    sudo tee /etc/systemd/system/claude-orchestrator.service > /dev/null <<EOF
[Unit]
Description=Claude Collective Orchestrator
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$COLLECTIVE_DIR
Environment="PATH=/usr/bin:/usr/local/bin"
EnvironmentFile=$PROJECT_ROOT/.env
ExecStart=/usr/bin/python3 $COLLECTIVE_DIR/start-langgraph-orchestration.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    sudo systemctl daemon-reload
    sudo systemctl enable claude-orchestrator.service
    echo "✅ Systemd service created and enabled"
fi

# Initialize the orchestration system
echo "🎯 Initializing orchestration system..."
cd "$COLLECTIVE_DIR"
python3 - <<EOF
import sys
sys.path.append('$COLLECTIVE_DIR')

from core.quadrant_memory import QuadrantMemorySystem, Quadrant
from pathlib import Path

# Initialize quadrant memory
print("Initializing quadrant memory system...")
memory = QuadrantMemorySystem(Path('$PROJECT_ROOT'))

# Pre-populate with project structure knowledge
quadrant_mappings = {
    Quadrant.FRONTEND: [
        "Next.js 15 App Router architecture",
        "Operator dashboard at /app/[locale]/(protected)/operator",
        "Live demo components in /components/features/operator/live-demo"
    ],
    Quadrant.BACKEND: [
        "Fastify server with tRPC routers",
        "WebSocket support for real-time communication",
        "AI orchestration service integration"
    ],
    Quadrant.INFRASTRUCTURE: [
        "Docker containerization",
        "Kubernetes deployment configs",
        "Nginx reverse proxy"
    ],
    Quadrant.DOCUMENTATION: [
        "CLAUDE.md for AI development rules",
        "API documentation in /docs",
        "Test specifications"
    ]
}

for quadrant, knowledge_items in quadrant_mappings.items():
    for item in knowledge_items:
        memory.store_memory(
            content=item,
            quadrant=quadrant,
            metadata={"source": "deployment_init", "type": "architecture"}
        )

print("✅ Quadrant memory initialized with project knowledge")
EOF

# Create monitoring dashboard startup script
echo "📊 Creating monitoring dashboard script..."
cat > "$COLLECTIVE_DIR/start-monitoring.sh" <<'EOF'
#!/bin/bash
cd "$(dirname "$0")"
echo "🖥️  Starting Orchestration Monitoring Dashboard"
echo "Access at: http://localhost:8200"
python3 -m http.server 8200 --directory ./reports
EOF
chmod +x "$COLLECTIVE_DIR/start-monitoring.sh"

# Generate initial status report
echo "📈 Generating initial status report..."
cd "$COLLECTIVE_DIR"
python3 tools/quadrant_visualizer.py

echo ""
echo "✅ Deployment Complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Update .env file with your ANTHROPIC_API_KEY"
echo "2. Start orchestration: python3 $COLLECTIVE_DIR/start-langgraph-orchestration.py"
echo "3. Monitor dashboard: $COLLECTIVE_DIR/start-monitoring.sh"
echo "4. View logs: tail -f $COLLECTIVE_DIR/logs/*.log"
echo ""
echo "🌙 For overnight automation:"
echo "   sudo systemctl start claude-orchestrator.service"
echo ""
echo "📊 View quadrant relationships:"
echo "   python3 $COLLECTIVE_DIR/tools/quadrant_visualizer.py"