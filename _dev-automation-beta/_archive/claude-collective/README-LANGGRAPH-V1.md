# Claude Collective v1 - LangGraph Architecture

## Overview

Version 1 of Claude Collective moves from a Python subprocess-based system to a proper multi-agent orchestration using **LangGraph** - a proven framework for building stateful, persistent agent systems.

## Why LangGraph?

After analyzing multiple frameworks (AutoGen, CrewAI, Agency Swarm, etc.), LangGraph emerged as the best choice because:

1. **True Persistence**: Built-in checkpointing allows agents to survive failures and resume exactly where they left off
2. **Stateful Agents**: Agents maintain memory and context across sessions
3. **Production Ready**: Unlike experimental frameworks, LangGraph has proven enterprise deployments
4. **Claude Compatible**: LLM-agnostic design works seamlessly with Claude API
5. **Overnight Operations**: Durable execution specifically designed for long-running workflows

## Architecture Changes (v0 → v1)

### Version 0 (Initial Sketch)
- Python subprocess workers simulating agents
- No real Claude integration
- Basic task queue system
- No persistence between sessions
- Manual nudging required

### Version 1 (LangGraph + Quadrant Memory)
- Real Claude agents via Anthropic API
- Graph-based agent coordination with LangGraph
- Quadrant-based memory system (inspired by Roo Code)
- Persistent state with SQLite checkpointing
- Automatic nudge system for inactive agents
- NightWatch agent for overnight monitoring
- Recovery from failures with state preservation
- Context-aware agent routing based on code quadrants

## Agent Roles

1. **Orchestrator** - Main coordinator, assigns tasks
2. **NightWatch** - Overnight monitor, keeps system healthy
3. **Researcher** - Information gathering and analysis
4. **Architect** - System design and planning
5. **Developer** - Implementation tasks
6. **QA** - Testing and validation
7. **Reviewer** - Code review and quality checks

## Key Features

### 1. Persistent Sessions
```python
# Agents can be resumed from checkpoints
await orchestrator.resume_from_checkpoint(checkpoint_id)
```

### 2. Automatic Nudging
- Agents inactive for >5 minutes get nudged
- NightWatch monitors overnight operations
- Self-healing system that prevents stalls

### 3. Inter-Agent Communication
- Shared state allows agents to collaborate
- Message passing through graph edges
- Knowledge preserved in agent memory

### 4. Task Routing
- Intelligent routing based on task type
- Dynamic agent selection
- Priority-based task queue

### 5. Quadrant-Based Memory System

#### Code Quadrants:
- **FRONTEND**: UI components, React/Next.js code
- **BACKEND**: APIs, services, server-side logic
- **INFRASTRUCTURE**: DevOps, configs, deployment
- **DOCUMENTATION**: Docs, tests, examples
- **SHARED**: Common utilities, types, libraries
- **EXTERNAL**: Third-party integrations

#### Quadrant Benefits:
- **Local Context Awareness**: Agents understand which part of the codebase they're working in
- **Cross-Quadrant Intelligence**: Track dependencies and relationships between different areas
- **Specialized Routing**: Tasks are routed to agents based on quadrant expertise
- **Memory Efficiency**: Related memories are clustered by quadrant for faster retrieval

#### Example Usage:
```python
# Store memory with quadrant awareness
memory_id = memory_system.store_memory(
    content="WebSocket handler implementation",
    quadrant=Quadrant.BACKEND,
    file_path="server/api/websocket.ts"
)

# Search within specific quadrants
frontend_memories = memory_system.search_memories(
    query="operator dashboard components",
    quadrants=[Quadrant.FRONTEND],
    include_related=True
)

# Get cross-quadrant insights
insights = memory_system.get_cross_quadrant_insights()
# Shows which quadrants are highly coupled, hot spots, etc.
```

## Installation

```bash
# Install LangGraph dependencies
pip install -r requirements-langgraph.txt

# Set Claude API key
export ANTHROPIC_API_KEY="your-api-key"
```

## Usage

### Start New Orchestration
```bash
python start-langgraph-orchestration.py
```

### Resume from Checkpoint
```python
# The system automatically finds and offers to resume checkpoints
```

### Monitor Status
- Check logs in `claude-collective/logs/`
- View checkpoints in `claude-collective/state/langgraph/`
- Real-time status via graph streaming

## Integration with Existing System

The LangGraph orchestrator integrates with:
- Git workflow management (night branches)
- Existing logging infrastructure
- Task discovery from documentation
- claude-flow MCP tools (future integration)

## Overnight Operations

The system is designed to run autonomously overnight:

1. **NightWatch Agent** monitors system health
2. **Automatic Nudging** prevents agent stalls
3. **Checkpoint Recovery** handles failures gracefully
4. **Task Persistence** ensures no work is lost

## Next Steps

1. Complete integration with claude-flow MCP tools
2. Build web dashboard for monitoring
3. Add more sophisticated task discovery
4. Implement agent specialization training
5. Create agent communication protocols

## Benefits Over v0

- **Real AI Agents**: Actual Claude instances, not Python workers
- **True Autonomy**: Can operate overnight without intervention
- **Fault Tolerance**: Checkpointing ensures work isn't lost
- **Scalable**: Easy to add new agent types
- **Observable**: Built-in monitoring and logging
- **Context-Aware**: Quadrant system provides local code awareness
- **Intelligent Memory**: Memories are organized by code area for relevance

## Monitoring Quadrant Activity

```bash
# Generate quadrant visualization report
python tools/quadrant_visualizer.py

# Output shows:
# - Memory distribution across quadrants
# - Cross-quadrant dependencies
# - Hot spots and recommendations
# - Mermaid diagram of relationships
```

This is a proper foundation for a persistent, autonomous Claude agent network that combines LangGraph's orchestration capabilities with Roo Code-inspired quadrant memory for superior context awareness.