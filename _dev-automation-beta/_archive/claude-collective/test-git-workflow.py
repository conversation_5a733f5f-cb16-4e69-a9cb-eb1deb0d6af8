#!/usr/bin/env python3
"""
Test script for Git Workflow integration
"""

import sys
import asyncio
from pathlib import Path

# Add the core directory to path
sys.path.insert(0, str(Path(__file__).parent / "core"))

from git_workflow import GitWorkflowManager

def test_git_workflow():
    """Test basic git workflow operations"""
    print("🧪 Testing Git Workflow Integration...")
    
    # Initialize manager
    repo_path = Path("/home/<USER>/github/cc-v1")
    manager = GitWorkflowManager(repo_path)
    
    # Check current branch
    try:
        current = manager._run_git(["branch", "--show-current"])
        print(f"✅ Current branch: {current.strip()}")
    except Exception as e:
        print(f"❌ Failed to get current branch: {e}")
        return False
        
    # Check git status
    try:
        status = manager._run_git(["status", "--porcelain"])
        if status.strip():
            print(f"⚠️  Uncommitted changes detected:\n{status}")
        else:
            print("✅ Working tree is clean")
    except Exception as e:
        print(f"❌ Failed to check status: {e}")
        return False
        
    # Test context save/load
    try:
        # Create test context
        from git_workflow import GitContext
        test_context = GitContext(
            branch_name="test_branch",
            changes_count=3
        )
        
        # Would save/load but don't want to create files during test
        print("✅ Git context management ready")
        
    except Exception as e:
        print(f"❌ Context management failed: {e}")
        return False
        
    print("\n✅ Git workflow integration test passed!")
    print("\n📝 Notes:")
    print("- System will create branches like: night_YYYY-MM-DD")
    print("- Commits will be automatic after task completion")
    print("- PRs will be created as drafts for manual review")
    print("- Never auto-merges to develop branch")
    
    return True

if __name__ == "__main__":
    success = test_git_workflow()
    sys.exit(0 if success else 1)