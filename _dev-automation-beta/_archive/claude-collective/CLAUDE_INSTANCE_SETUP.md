# Claude Instance Orchestration Setup Guide

## Overview

This guide explains how to orchestrate multiple Claude instances (<PERSON><PERSON>, <PERSON>, VS Code) that use your existing Claude subscription instead of the API.

## Architecture Options

### Option 1: Browser-Based Orchestration (Claude.ai)

Uses Playwright to automate multiple Claude.ai browser tabs.

**Pros:**
- Works with any Claude Pro/Team subscription
- Visual monitoring of each agent
- Easy to debug and intervene

**Cons:**
- Requires browser automation
- Limited by browser resources
- Manual login required initially

### Option 2: MCP-Based Orchestration (<PERSON>)

Uses Model Context Protocol with Claude Desktop app.

**Pros:**
- Native integration with Claude Desktop
- File system access through MCP
- Better performance than browser automation

**Cons:**
- Requires Claude Desktop app
- MCP setup complexity

### Option 3: VS Code Extension Orchestration

Coordinates multiple VS Code windows with Claude extension.

**Pros:**
- Direct code editing capability
- Integrated development environment
- Good for code-heavy tasks

**Cons:**
- Requires VS Code + Claude extension
- Resource intensive

## Quick Start: Browser-Based Setup

### 1. Install Dependencies

```bash
pip install playwright
playwright install chromium
```

### 2. Login to <PERSON>.ai

```bash
# Start the orchestrator in setup mode
python3 start-browser-orchestration.py --setup
```

This will:
1. Open a browser window
2. Navigate to claude.ai
3. Wait for you to login manually
4. Save the session for reuse

### 3. Configure Agent Roles

Create `agents.json`:

```json
{
  "agents": [
    {
      "name": "Orchestrator",
      "role": "coordinator",
      "prompt": "You coordinate tasks between other agents"
    },
    {
      "name": "Researcher", 
      "role": "analyst",
      "prompt": "You research and analyze requirements"
    },
    {
      "name": "Developer-1",
      "role": "builder",
      "prompt": "You implement solutions"
    },
    {
      "name": "Developer-2",
      "role": "builder", 
      "prompt": "You implement solutions"
    },
    {
      "name": "Reviewer",
      "role": "validator",
      "prompt": "You review and validate work"
    }
  ]
}
```

### 4. Start Orchestration

```bash
python3 start-browser-orchestration.py
```

This will:
- Open 5 browser tabs (one per agent)
- Initialize each with their role
- Start the coordination system

## MCP Setup for Claude Desktop

### 1. Install MCP Servers

```bash
npm install -g @modelcontextprotocol/server-filesystem
npm install -g @modelcontextprotocol/server-git
```

### 2. Configure Claude Desktop

Edit Claude Desktop config:

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%/Claude/claude_desktop_config.json`
**Linux**: `~/.config/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/cc-v1"]
    },
    "git": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-git", "--repository", "/path/to/cc-v1"]
    }
  },
  "orchestration": {
    "enabled": true,
    "role": "coordinator",
    "collectiveId": "cc-v1-collective"
  }
}
```

### 3. Open Multiple Claude Desktop Windows

1. Open Claude Desktop (Window 1) - Coordinator
2. Cmd/Ctrl + N for new window - Analyst
3. Cmd/Ctrl + N for new window - Builder
4. Cmd/Ctrl + N for new window - Validator

### 4. Initialize Each Window

In each window, paste the appropriate initialization:

**Coordinator:**
```
I am the Coordinator agent in the CC-V1 collective. I will coordinate tasks between other agents using the filesystem MCP server to share state.
```

**Analyst:**
```
I am the Analyst agent in the CC-V1 collective. I will research and analyze tasks assigned to me, sharing findings through the filesystem MCP server.
```

**Builder:**
```
I am the Builder agent in the CC-V1 collective. I will implement solutions using the filesystem and git MCP servers.
```

**Validator:**
```
I am the Validator agent in the CC-V1 collective. I will review and validate work using the filesystem and git MCP servers.
```

## Task Coordination Protocol

### 1. Shared Task Queue

Tasks are stored in: `_dev-automation-beta/claude-collective/tasks/`

Each task is a JSON file:
```json
{
  "id": "task-001",
  "description": "Implement user authentication",
  "type": "feature",
  "assigned_to": null,
  "status": "pending",
  "created_at": "2024-01-15T10:00:00Z"
}
```

### 2. Agent Communication

Agents communicate through files in: `_dev-automation-beta/claude-collective/messages/`

Message format:
```json
{
  "from": "coordinator",
  "to": "builder-1",
  "subject": "Task Assignment",
  "body": "Please implement the authentication feature",
  "task_id": "task-001",
  "timestamp": "2024-01-15T10:05:00Z"
}
```

### 3. Workflow Example

1. **Human → Coordinator**: "Build a user authentication system"

2. **Coordinator** creates task files:
   - `tasks/task-001-research.json` → Analyst
   - `tasks/task-002-implement.json` → Builder
   - `tasks/task-003-validate.json` → Validator

3. **Agents** check their task queues (via MCP filesystem)

4. **Analyst** researches and writes findings to:
   - `findings/auth-research.md`

5. **Builder** reads findings and implements:
   - Uses git MCP to create feature branch
   - Implements code
   - Updates task status

6. **Validator** reviews implementation:
   - Checks code quality
   - Runs tests
   - Approves or requests changes

## Monitoring

### Browser-Based Monitoring

```bash
# View agent activity
python3 monitor-agents.py

# Shows:
# - Active agents and their current tasks
# - Message queue status
# - Task completion progress
```

### File-Based Monitoring

```bash
# Watch task queue
watch -n 2 'ls -la tasks/ | grep pending'

# Watch messages
tail -f messages/*.json

# View agent status
cat agents/*/status.json
```

## Best Practices

1. **Start Small**: Begin with 2-3 agents before scaling up

2. **Clear Role Definition**: Each agent should have a specific purpose

3. **Task Granularity**: Break large tasks into smaller, agent-specific subtasks

4. **Regular Checkpoints**: Have agents report progress frequently

5. **Manual Oversight**: Monitor the first few runs closely

6. **Session Persistence**: Save browser sessions to avoid re-login

## Troubleshooting

### Browser Automation Issues

```bash
# Clear browser data
rm -rf browser_data/

# Restart with fresh session
python3 start-browser-orchestration.py --clean
```

### MCP Connection Issues

```bash
# Test MCP servers
npx @modelcontextprotocol/server-filesystem /path/to/project

# Check Claude Desktop logs
tail -f ~/Library/Logs/Claude/claude.log  # macOS
```

### Agent Coordination Issues

- Ensure all agents have the same collective ID
- Check file permissions in shared directories
- Verify MCP server configurations match

## Advanced: Hybrid Approach

Combine multiple approaches for maximum effectiveness:

1. **Coordinator**: Claude Desktop with MCP (for file access)
2. **Analysts**: Claude.ai browser tabs (for research)
3. **Builders**: VS Code with Claude extension (for coding)
4. **Validator**: Claude Desktop with testing tools

This leverages the strengths of each platform while using your Claude subscription for all instances.