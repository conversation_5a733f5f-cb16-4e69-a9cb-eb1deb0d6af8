#!/usr/bin/env python3
"""
Start Terminal Claude Orchestration
Manages claude-cli and claude-flow instances with overnight persistence
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime
import argparse

sys.path.insert(0, str(Path(__file__).parent))

from core.terminal_claude_orchestrator import TerminalClaudeOrchestrator
from core.logger_config import setup_logger

logger, _, _ = setup_logger('terminal_start')

async def monitor_loop(orchestrator: TerminalClaudeOrchestrator):
    """Monitor loop that shows status periodically"""
    while True:
        try:
            status = await orchestrator.get_status()
            
            print("\n" + "="*60)
            print(f"📊 Terminal Claude Status - {status['timestamp']}")
            print("="*60)
            
            for instance_id, info in status['instances'].items():
                status_icon = "🟢" if info['alive'] else "🔴"
                idle_icon = "💤" if info['idle_minutes'] > 5 else "⚡"
                
                print(f"{status_icon} {instance_id}")
                print(f"   Role: {info['role']} | Type: {info['type']}")
                print(f"   {idle_icon} Idle: {info['idle_minutes']} min | Nudges: {info['nudge_count']}")
                
                if info['current_task']:
                    print(f"   📋 Task: {info['current_task']}")
                print()
                
            # Show vector store status if available
            if orchestrator.vector_store:
                print("🗃️ Vector Store: Active")
            else:
                print("🗃️ Vector Store: Disabled")
                
            print("\n💡 Tips:")
            print("- Add task files to terminal_state/tasks/ to submit work")
            print("- Instances are nudged automatically to stay alive")
            print("- NightWatch agent provides extra monitoring overnight")
            print("- Press Ctrl+C to shutdown gracefully")
            
            await asyncio.sleep(60)  # Update every minute
            
        except Exception as e:
            logger.error(f"Monitor error: {e}")
            await asyncio.sleep(60)

async def submit_demo_tasks(orchestrator: TerminalClaudeOrchestrator):
    """Submit some demo tasks to get started"""
    demo_tasks = [
        {
            "id": "demo_analyze_1",
            "type": "analyze", 
            "description": "Analyze the current orchestration system architecture and suggest improvements",
            "priority": "medium"
        },
        {
            "id": "demo_implement_1",
            "type": "implement",
            "description": "Create a simple health check endpoint for the orchestration system",
            "priority": "low"
        },
        {
            "id": "demo_research_1",
            "type": "research",
            "description": "Research best practices for keeping terminal processes alive overnight",
            "priority": "high"
        }
    ]
    
    for task in demo_tasks:
        task_id = await orchestrator.submit_task(task)
        logger.info(f"📋 Submitted demo task: {task_id}")
        
async def main():
    parser = argparse.ArgumentParser(description="Terminal Claude Orchestration")
    parser.add_argument("--no-vector", action="store_true", help="Disable vector store")
    parser.add_argument("--demo-tasks", action="store_true", help="Submit demo tasks on startup")
    parser.add_argument("--instances", type=int, default=None, help="Override number of instances")
    args = parser.parse_args()
    
    base_path = Path(__file__).parent.parent.parent
    
    # Create orchestrator
    orchestrator = TerminalClaudeOrchestrator(
        base_path=base_path,
        use_vector_store=not args.no_vector
    )
    
    # Override instance config if requested
    if args.instances:
        logger.info(f"Overriding to spawn {args.instances} instances")
        # Simplified config for testing
        from core.terminal_claude_orchestrator import TerminalAgentRole, TerminalClaudeType
        orchestrator.instance_config = {
            TerminalAgentRole.ORCHESTRATOR: (1, TerminalClaudeType.CLAUDE_FLOW),
            TerminalAgentRole.CODER: (max(1, args.instances - 2), TerminalClaudeType.CLAUDE_FLOW),
            TerminalAgentRole.NIGHTWATCH: (1, TerminalClaudeType.CLAUDE_CLI)
        }
    
    try:
        # Start orchestrator
        await orchestrator.start()
        
        # Submit demo tasks if requested
        if args.demo_tasks:
            await submit_demo_tasks(orchestrator)
            
        # Start monitoring
        monitor_task = asyncio.create_task(monitor_loop(orchestrator))
        
        # Keep running until interrupted
        await monitor_task
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ Shutdown requested...")
    finally:
        await orchestrator.shutdown()
        logger.info("✅ Clean shutdown complete")

if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════╗
║         Terminal Claude Orchestration System v1          ║
║                                                          ║
║  Orchestrating claude-cli and claude-flow instances      ║
║  with overnight persistence and vector memory            ║
╚══════════════════════════════════════════════════════════╝
""")
    
    asyncio.run(main())