#!/usr/bin/env python3
"""
Claude Instance Orchestrator - Orchestrates multiple Claude.ai instances
Uses browser automation to coordinate actual Claude sessions
"""

import asyncio
import json
import time
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext

from .logger_config import setup_logger
from .quadrant_memory import QuadrantMemorySystem, Quadrant

logger, _, _ = setup_logger('claude_instances')

class ClaudeInstanceType(Enum):
    """Types of Claude instances"""
    WEB_BROWSER = "web_browser"      # Claude.ai in browser
    DESKTOP_APP = "desktop_app"      # Claude desktop application
    VSCODE_EXTENSION = "vscode"      # Claude in VS Code
    CURSOR_IDE = "cursor"            # Cursor IDE with Claude

class InstanceRole(Enum):
    """Roles for Claude instances"""
    ORCHESTRATOR = "orchestrator"
    RESEARCHER = "researcher"
    DEVELOPER = "developer"
    REVIEWER = "reviewer"
    TESTER = "tester"
    DOCUMENTER = "documenter"

@dataclass
class ClaudeInstance:
    """Represents an actual Claude instance (browser tab, app window, etc)"""
    id: str
    role: InstanceRole
    type: ClaudeInstanceType
    session_url: Optional[str] = None
    browser_context: Optional[BrowserContext] = None
    page: Optional[Page] = None
    last_activity: float = field(default_factory=time.time)
    current_task: Optional[str] = None
    conversation_id: Optional[str] = None
    
    def is_active(self) -> bool:
        """Check if instance is still active"""
        return time.time() - self.last_activity < 300  # 5 minutes

@dataclass
class InterInstanceMessage:
    """Message passed between Claude instances"""
    from_instance: str
    to_instance: str
    content: str
    task_context: Optional[Dict] = None
    timestamp: float = field(default_factory=time.time)

class ClaudeInstanceOrchestrator:
    """Orchestrates multiple Claude.ai instances using browser automation"""
    
    def __init__(self, base_path: Path):
        self.base_path = base_path
        self.instances: Dict[str, ClaudeInstance] = {}
        self.browser: Optional[Browser] = None
        self.playwright = None
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.quadrant_memory = QuadrantMemorySystem(base_path)
        
        # Configuration
        self.claude_url = "https://claude.ai"
        self.max_instances = 5  # Reasonable limit for browser tabs
        self.instance_config = {
            InstanceRole.ORCHESTRATOR: 1,
            InstanceRole.RESEARCHER: 1,
            InstanceRole.DEVELOPER: 2,
            InstanceRole.REVIEWER: 1
        }
        
    async def start(self):
        """Initialize the orchestrator and spawn instances"""
        logger.info("🚀 Starting Claude Instance Orchestrator")
        
        # Start Playwright
        self.playwright = await async_playwright().start()
        
        # Launch browser with persistent context
        self.browser = await self.playwright.chromium.launch_persistent_context(
            user_data_dir=str(self.base_path / "browser_data"),
            headless=False,  # Need visible browser for Claude.ai
            viewport={"width": 1280, "height": 800},
            locale="en-US"
        )
        
        # Start core services
        asyncio.create_task(self._message_processor())
        asyncio.create_task(self._health_monitor())
        
        # Spawn initial instances
        await self._spawn_instances()
        
        logger.info("✅ Instance orchestrator initialized")
        
    async def _spawn_instances(self):
        """Spawn Claude instances based on configuration"""
        for role, count in self.instance_config.items():
            for i in range(count):
                instance = await self._create_instance(role)
                if instance:
                    self.instances[instance.id] = instance
                    
    async def _create_instance(self, role: InstanceRole) -> Optional[ClaudeInstance]:
        """Create a new Claude instance in a browser tab"""
        try:
            instance_id = f"{role.value}_{uuid.uuid4().hex[:8]}"
            
            # Open new tab
            page = await self.browser.new_page()
            
            # Navigate to Claude
            await page.goto(self.claude_url)
            
            # Wait for Claude to load
            await page.wait_for_selector('textarea[placeholder*="Message Claude"]', timeout=30000)
            
            instance = ClaudeInstance(
                id=instance_id,
                role=role,
                type=ClaudeInstanceType.WEB_BROWSER,
                session_url=page.url,
                page=page
            )
            
            # Initialize the instance with its role
            await self._initialize_instance_role(instance)
            
            logger.info(f"✨ Created {role.value} instance: {instance_id}")
            return instance
            
        except Exception as e:
            logger.error(f"Failed to create instance: {e}")
            return None
            
    async def _initialize_instance_role(self, instance: ClaudeInstance):
        """Initialize a Claude instance with its specific role"""
        role_prompts = {
            InstanceRole.ORCHESTRATOR: """You are the Orchestrator in a multi-Claude system. 
Your role is to:
1. Coordinate tasks between other Claude instances
2. Monitor overall progress
3. Make decisions about task routing
4. Maintain system coherence

You will receive task descriptions and route them to appropriate specialists.""",
            
            InstanceRole.RESEARCHER: """You are the Researcher in a multi-Claude system.
Your role is to:
1. Gather information and analyze requirements
2. Research best practices and solutions
3. Provide detailed context for implementation
4. Document findings for other instances

Focus on thorough analysis and information gathering.""",
            
            InstanceRole.DEVELOPER: """You are the Developer in a multi-Claude system.
Your role is to:
1. Implement solutions based on research
2. Write clean, well-documented code
3. Follow established patterns and conventions
4. Collaborate with other instances

Focus on practical implementation.""",
            
            InstanceRole.REVIEWER: """You are the Reviewer in a multi-Claude system.
Your role is to:
1. Review code and implementations
2. Check for best practices and patterns
3. Suggest improvements
4. Ensure quality standards

Focus on constructive feedback and quality assurance."""
        }
        
        prompt = role_prompts.get(instance.role, "You are a Claude instance in a multi-Claude system.")
        
        # Send initialization message
        await self._send_message_to_instance(instance, prompt)
        
    async def _send_message_to_instance(self, instance: ClaudeInstance, message: str):
        """Send a message to a specific Claude instance"""
        if not instance.page:
            return
            
        try:
            # Find the message input
            textarea = await instance.page.wait_for_selector('textarea[placeholder*="Message Claude"]')
            
            # Clear and type message
            await textarea.click()
            await textarea.fill(message)
            
            # Send message (Enter key)
            await textarea.press("Enter")
            
            # Update activity
            instance.last_activity = time.time()
            
            # Wait a bit for response to start
            await asyncio.sleep(2)
            
        except Exception as e:
            logger.error(f"Error sending message to {instance.id}: {e}")
            
    async def _get_latest_response(self, instance: ClaudeInstance) -> Optional[str]:
        """Get the latest response from a Claude instance"""
        if not instance.page:
            return None
            
        try:
            # Wait for response to complete (look for response container)
            await instance.page.wait_for_selector('[data-testid="message-content"]', timeout=30000)
            
            # Get all messages
            messages = await instance.page.query_selector_all('[data-testid="message-content"]')
            
            if messages:
                # Get the last message (latest response)
                last_message = messages[-1]
                response_text = await last_message.inner_text()
                return response_text
                
        except Exception as e:
            logger.error(f"Error getting response from {instance.id}: {e}")
            return None
            
    async def route_task(self, task: Dict[str, Any]) -> str:
        """Route a task to the appropriate Claude instance"""
        # First, send to orchestrator for analysis
        orchestrator = next(
            (inst for inst in self.instances.values() 
             if inst.role == InstanceRole.ORCHESTRATOR),
            None
        )
        
        if not orchestrator:
            logger.error("No orchestrator instance available")
            return ""
            
        # Ask orchestrator to analyze and route
        routing_prompt = f"""Please analyze this task and determine which specialist should handle it:

Task: {task.get('description', '')}
Type: {task.get('type', 'general')}
Context: {json.dumps(task.get('context', {}), indent=2)}

Available specialists:
- Researcher: For information gathering and analysis
- Developer: For implementation and coding
- Reviewer: For code review and quality checks

Respond with just the role name (researcher/developer/reviewer)."""
        
        await self._send_message_to_instance(orchestrator, routing_prompt)
        await asyncio.sleep(5)  # Wait for response
        
        response = await self._get_latest_response(orchestrator)
        
        # Parse response to get target role
        target_role = self._parse_role_from_response(response)
        
        # Find available instance with that role
        target_instance = self._find_available_instance(target_role)
        
        if target_instance:
            # Send task to target instance
            task_prompt = f"""New task assigned to you:

{json.dumps(task, indent=2)}

Please work on this task and provide your response."""
            
            await self._send_message_to_instance(target_instance, task_prompt)
            target_instance.current_task = task.get('id', 'unknown')
            
            # Store in quadrant memory
            quadrant = Quadrant(task.get('quadrant', 'shared'))
            self.quadrant_memory.store_memory(
                content=f"Task {task['id']} assigned to {target_instance.id}",
                quadrant=quadrant,
                metadata={
                    "task": task,
                    "instance": target_instance.id,
                    "role": target_instance.role.value
                }
            )
            
            return target_instance.id
            
        return ""
        
    def _parse_role_from_response(self, response: Optional[str]) -> InstanceRole:
        """Parse the role from orchestrator's response"""
        if not response:
            return InstanceRole.DEVELOPER
            
        response_lower = response.lower()
        
        if "researcher" in response_lower:
            return InstanceRole.RESEARCHER
        elif "reviewer" in response_lower:
            return InstanceRole.REVIEWER
        else:
            return InstanceRole.DEVELOPER
            
    def _find_available_instance(self, role: InstanceRole) -> Optional[ClaudeInstance]:
        """Find an available instance with the specified role"""
        for instance in self.instances.values():
            if instance.role == role and instance.is_active() and not instance.current_task:
                return instance
        
        # If none available, return any instance with that role
        for instance in self.instances.values():
            if instance.role == role and instance.is_active():
                return instance
                
        return None
        
    async def _message_processor(self):
        """Process inter-instance messages"""
        while True:
            try:
                message = await self.message_queue.get()
                
                # Route message to target instance
                if message.to_instance in self.instances:
                    target = self.instances[message.to_instance]
                    
                    # Format message with context
                    formatted_message = f"""Message from {message.from_instance}:

{message.content}

Context: {json.dumps(message.task_context, indent=2) if message.task_context else 'None'}"""
                    
                    await self._send_message_to_instance(target, formatted_message)
                    
            except Exception as e:
                logger.error(f"Message processing error: {e}")
                
    async def _health_monitor(self):
        """Monitor instance health and restart if needed"""
        while True:
            try:
                for instance_id, instance in list(self.instances.items()):
                    if not instance.is_active():
                        logger.warning(f"Instance {instance_id} is inactive")
                        
                        # Try to revive it
                        if instance.page:
                            try:
                                await instance.page.reload()
                                instance.last_activity = time.time()
                            except:
                                # Remove dead instance
                                del self.instances[instance_id]
                                
                                # Create replacement
                                new_instance = await self._create_instance(instance.role)
                                if new_instance:
                                    self.instances[new_instance.id] = new_instance
                                    
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Health monitor error: {e}")
                await asyncio.sleep(30)
                
    async def create_instance_handoff(self, from_id: str, to_id: str, context: str):
        """Create a handoff between two Claude instances"""
        message = InterInstanceMessage(
            from_instance=from_id,
            to_instance=to_id,
            content=context
        )
        
        await self.message_queue.put(message)
        
    async def shutdown(self):
        """Clean shutdown of all instances"""
        logger.info("Shutting down instance orchestrator...")
        
        # Close all pages
        for instance in self.instances.values():
            if instance.page:
                await instance.page.close()
                
        # Close browser
        if self.browser:
            await self.browser.close()
            
        # Stop playwright
        if self.playwright:
            await self.playwright.stop()
            
        logger.info("✅ Shutdown complete")