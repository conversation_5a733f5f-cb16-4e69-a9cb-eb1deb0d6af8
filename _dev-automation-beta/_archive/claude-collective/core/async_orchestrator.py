#!/usr/bin/env python3
"""
Async Orchestrator for Claude Collective
Inspired by Cline and Roo Code's multi-agent architecture
"""

import asyncio
import json
import logging
import multiprocessing
import os
import re
import sys
import time
from concurrent.futures import Process<PERSON>oolExecutor
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from pathlib import Path
from queue import Queue
from typing import Dict, List, Optional, Any, Callable
import subprocess

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import from same directory
from .git_workflow import GitWorkflowManager
from .swarm_mode import SwarmOrchestrator, SwarmConfig
from .autodiscovery_engine import AutodiscoveryEngine

# Import enhanced logging
from .logger_config import setup_logger, log_task_event

# Setup enhanced logging
logger, log_file, json_log = setup_logger('orchestrator')

class AgentRole(Enum):
    """Agent roles inspired by Roo Code"""
    ORCHESTRATOR = "orchestrator"
    ARCHITECT = "architect"
    DEVELOPER_INTERN = "developer_intern"
    DEVELOPER_JUNIOR = "developer_junior"
    DEVELOPER_MIDLEVEL = "developer_midlevel"
    DEVELOPER_SENIOR = "developer_senior"
    QA = "qa"
    RESEARCHER = "researcher"

@dataclass
class Task:
    """Task definition for agents"""
    id: str
    type: str
    role: AgentRole
    context: str
    priority: int = 1
    status: str = "pending"
    result: Optional[str] = None
    created_at: float = 0
    completed_at: Optional[float] = None
    
    def __post_init__(self):
        if self.created_at == 0:
            self.created_at = time.time()

class AsyncOrchestrator:
    """Main orchestrator that manages agent pool and task delegation"""
    
    def __init__(self, base_path: Path):
        self.base_path = base_path
        self.automation_path = base_path / "core" / "automation-engine.py"
        self.state_path = base_path / "claude-collective" / "state"
        self.log_path = base_path / "claude-collective" / "logs"
        self.repo_path = base_path.parent  # cc-v1 root
        
        # Task queues
        self.task_queue = asyncio.Queue()
        self.result_queue = asyncio.Queue()
        
        # Agent pool
        self.agents: Dict[AgentRole, List[asyncio.Task]] = {}
        self.agent_status: Dict[str, Dict] = {}
        
        # Performance metrics
        self.metrics = {
            "tasks_created": 0,
            "tasks_completed": 0,
            "avg_completion_time": 0,
            "errors": 0
        }
        
        # Git workflow manager
        self.git_manager = GitWorkflowManager(self.repo_path)
        self.git_initialized = False
        
        # Swarm mode orchestrator
        self.swarm_orchestrator = SwarmOrchestrator(self.repo_path)
        self.swarm_mode_enabled = False  # Can be toggled
        
        # Autodiscovery engine
        self.autodiscovery = AutodiscoveryEngine(self.repo_path, orchestrator=self)
        self.autodiscovery_enabled = True  # Enabled by default
        
    async def start(self):
        """Start the orchestrator and agent pool"""
        logger.info("Starting Async Orchestrator...")
        
        # Initialize git workflow
        await self._initialize_git_workflow()
        
        # Create agent pool
        await self._create_agent_pool()
        
        # Start monitoring
        asyncio.create_task(self._monitor_agents())
        
        # Start task distributor
        asyncio.create_task(self._distribute_tasks())
        
        # Start result processor
        asyncio.create_task(self._process_results())
        
        # Start autodiscovery if enabled
        if self.autodiscovery_enabled:
            await self.autodiscovery.start()
            logger.info("🔍 Autodiscovery engine started - continuously scanning for tasks")
        
        logger.info("Orchestrator started successfully")
        
    async def _create_agent_pool(self):
        """Create pool of agents based on workload"""
        agent_config = {
            AgentRole.ARCHITECT: 1,
            AgentRole.DEVELOPER_INTERN: 2,
            AgentRole.DEVELOPER_JUNIOR: 2,
            AgentRole.DEVELOPER_MIDLEVEL: 3,
            AgentRole.DEVELOPER_SENIOR: 1,
            AgentRole.QA: 2,
            AgentRole.RESEARCHER: 1
        }
        
        for role, count in agent_config.items():
            self.agents[role] = []
            for i in range(count):
                agent_id = f"{role.value}_{i}"
                agent_task = asyncio.create_task(
                    self._agent_worker(agent_id, role)
                )
                self.agents[role].append(agent_task)
                self.agent_status[agent_id] = {
                    "role": role.value,
                    "status": "idle",
                    "tasks_completed": 0
                }
                
    async def _agent_worker(self, agent_id: str, role: AgentRole):
        """Agent worker that processes tasks"""
        logger.info(f"Agent {agent_id} started")
        
        while True:
            try:
                # Non-blocking wait for tasks
                task = await asyncio.wait_for(
                    self._get_task_for_role(role), 
                    timeout=1.0
                )
                
                if task:
                    self.agent_status[agent_id]["status"] = "working"
                    log_task_event(logger, "start", task.id, agent_id=agent_id, task_type=task.type)
                    
                    # Process task
                    start_time = time.time()
                    result = await self._process_task(task, role)
                    duration = time.time() - start_time
                    
                    # Update task
                    task.status = "completed"
                    task.result = result
                    task.completed_at = time.time()
                    
                    # Send result
                    await self.result_queue.put(task)
                    
                    # Update metrics
                    self.agent_status[agent_id]["tasks_completed"] += 1
                    self.agent_status[agent_id]["status"] = "idle"
                    
                    log_task_event(logger, "complete", task.id, 
                                 agent_id=agent_id, 
                                 duration=duration,
                                 result_size=len(result) if result else 0)
                    
            except asyncio.TimeoutError:
                # No task available, continue
                await asyncio.sleep(0.1)
            except Exception as e:
                logger.error(f"Agent {agent_id} error: {e}")
                self.metrics["errors"] += 1
                
    async def _get_task_for_role(self, role: AgentRole) -> Optional[Task]:
        """Get next task for specific role"""
        # This would be more sophisticated in production
        # For now, simple queue check
        if not self.task_queue.empty():
            task = await self.task_queue.get()
            if task.role == role:
                return task
            else:
                # Put it back if not for this role
                await self.task_queue.put(task)
        return None
        
    async def _process_task(self, task: Task, role: AgentRole) -> str:
        """Process task using swarm mode or automation framework"""
        
        # Check if swarm mode is enabled and suitable for this task
        use_swarm = (
            self.swarm_mode_enabled and 
            self.swarm_orchestrator.swarm_enabled and
            task.type in ["implement", "integrate", "fix_bug", "analyze"]
        )
        
        if use_swarm:
            logger.info(f"Processing task {task.id} with SWARM MODE (10x+ performance)")
            
            # Use swarm intelligence
            success, result = await self.swarm_orchestrator.process_with_swarm(
                task_type=task.type,
                context=f"{role.value.upper()}: {task.context}",
                force_simple=False
            )
            
            if success:
                return result
            else:
                logger.warning(f"Swarm failed for task {task.id}, falling back to simple mode")
        
        # Fall back to simple mode or use it directly
        logger.info(f"Processing task {task.id} with simple mode")
        
        # Map task to automation template
        template_map = {
            "analyze": "Analyze Current State",
            "fix_bug": "Analyze and Fix Bug",
            "test": "Run Testing Suite",
            "integrate": "Complete Frontend Integration",
            "deploy": "Prepare Deployment"
        }
        
        template = template_map.get(task.type, "Analyze Current State")
        
        # Add role context to task
        context = f"{role.value.upper()}: {task.context}"
        
        # Run automation engine
        cmd = [
            sys.executable,
            str(self.automation_path),
            "--template", template,
            "--context", context
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            return result.stdout if result.returncode == 0 else f"Error: {result.stderr}"
        except subprocess.TimeoutExpired:
            return "Error: Task timeout"
        except Exception as e:
            return f"Error: {str(e)}"
            
    async def _distribute_tasks(self):
        """Distribute tasks to agents"""
        while True:
            await asyncio.sleep(1)
            # This would contain logic to create and distribute tasks
            
    async def _process_results(self):
        """Process completed tasks"""
        while True:
            try:
                result = await asyncio.wait_for(
                    self.result_queue.get(),
                    timeout=1.0
                )
                
                # Update metrics
                self.metrics["tasks_completed"] += 1
                if result.completed_at and result.created_at:
                    completion_time = result.completed_at - result.created_at
                    # Running average
                    self.metrics["avg_completion_time"] = (
                        (self.metrics["avg_completion_time"] * (self.metrics["tasks_completed"] - 1) + 
                         completion_time) / self.metrics["tasks_completed"]
                    )
                
                # Log result
                logger.info(f"Task {result.id} completed in {completion_time:.2f}s")
                
                # Save to state
                await self._save_task_state(result)
                
                # Handle git operations
                await self._handle_task_completion(result)
                
            except asyncio.TimeoutError:
                continue
                
    async def _monitor_agents(self):
        """Monitor agent health and performance"""
        while True:
            await asyncio.sleep(10)
            
            # Check agent health
            for role, agents in self.agents.items():
                active = sum(1 for a in agents if not a.done())
                logger.info(f"{role.value}: {active}/{len(agents)} agents active")
                
            # Log metrics
            logger.info(f"Metrics: {self.metrics}")
            
            # Save system state for dashboard
            await self._save_system_state()
            
    async def _save_task_state(self, task: Task):
        """Save task state to disk"""
        state_file = self.state_path / "orchestrator" / f"task_{task.id}.json"
        state_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(state_file, 'w') as f:
            json.dump(asdict(task), f, indent=2)
            
    async def submit_task(self, task_type: str, context: str, role: AgentRole = AgentRole.DEVELOPER_MIDLEVEL):
        """Submit a new task to the system"""
        # Add small random component to avoid ID collisions
        import random
        task_id = f"task_{int(time.time() * 1000)}_{random.randint(1000, 9999)}"
        task = Task(
            id=task_id,
            type=task_type,
            role=role,
            context=context
        )
        
        await self.task_queue.put(task)
        self.metrics["tasks_created"] += 1
        logger.info(f"Task {task.id} submitted")
        
        return task.id
        
    def enable_swarm_mode(self):
        """Enable swarm mode for 10x+ performance"""
        if self.swarm_orchestrator.swarm_enabled:
            self.swarm_mode_enabled = True
            logger.info("🐝 SWARM MODE ENABLED - Expect 10x+ performance boost!")
            return True
        else:
            logger.warning("Claude-flow not available - cannot enable swarm mode")
            return False
            
    def disable_swarm_mode(self):
        """Disable swarm mode, use simple processing"""
        self.swarm_mode_enabled = False
        logger.info("Swarm mode disabled - using simple processing")

    def get_status(self) -> Dict[str, Any]:
        """Get current system status"""
        autodiscovery_stats = self.autodiscovery.get_discovery_stats() if self.autodiscovery_enabled else {}
        
        return {
            "agents": self.agent_status,
            "metrics": self.metrics,
            "queue_size": self.task_queue.qsize(),
            "timestamp": datetime.now().isoformat(),
            "swarm_mode": self.swarm_mode_enabled,
            "swarm_available": self.swarm_orchestrator.swarm_enabled,
            "autodiscovery": autodiscovery_stats
        }
        
    async def _initialize_git_workflow(self):
        """Initialize git workflow for the night session"""
        try:
            # Check for existing context
            existing_context = self.git_manager.load_context()
            
            if existing_context and not existing_context.pr_ready:
                logger.info(f"Resuming git session on branch: {existing_context.branch_name}")
            else:
                # Start new session
                context = self.git_manager.initialize_night_session()
                logger.info(f"Started new git session on branch: {context.branch_name}")
                
            self.git_initialized = True
            
        except Exception as e:
            logger.error(f"Failed to initialize git workflow: {e}")
            logger.warning("Continuing without git integration")
            self.git_initialized = False
            
    async def _handle_task_completion(self, task: Task):
        """Handle git operations when a task completes"""
        if not self.git_initialized or not task.result:
            return
            
        try:
            # Extract files that were modified from the task result
            # This is a simple pattern matching - could be enhanced
            modified_files = self._extract_modified_files(task.result)
            
            if modified_files:
                # Create meaningful commit message
                commit_msg = f"{task.type}: {task.context[:50]}..."
                
                # Stage and commit
                success = self.git_manager.stage_and_commit(
                    files=modified_files,
                    message=commit_msg,
                    task_id=task.id
                )
                
                if success:
                    logger.info(f"Committed changes for task {task.id}")
                    
                # Check if we should create a new PR branch
                if self.git_manager.should_create_new_pr():
                    logger.info("Starting new PR branch for continued work")
                    self.git_manager.start_new_pr_branch()
                    
        except Exception as e:
            logger.error(f"Git operations failed for task {task.id}: {e}")
            
    def _extract_modified_files(self, result: str) -> List[str]:
        """Extract list of modified files from task result"""
        # Simple pattern matching for file paths
        # This could be enhanced with better parsing
        files = []
        
        # Look for common patterns indicating file modifications
        patterns = [
            r"(?:modified|created|updated|wrote to):\s*([^\s]+\.(?:ts|tsx|js|jsx|py|json|md))",
            r"File (?:created|updated) at:\s*([^\s]+)",
            r"(?:backend|frontend)/[^\s]+\.(?:ts|tsx|js|jsx|py)"
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, result, re.IGNORECASE)
            files.extend(matches)
            
        # Remove duplicates and filter valid paths
        unique_files = []
        for f in set(files):
            if Path(f).exists():
                unique_files.append(f)
                
        return unique_files
    
    async def _save_system_state(self):
        """Save system state for monitoring"""
        state_file = self.state_path / "orchestrator" / "system_state.json"
        state_file.parent.mkdir(parents=True, exist_ok=True)
        
        state = self.get_status()
        
        with open(state_file, 'w') as f:
            json.dump(state, f, indent=2)

# CLI interface
async def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Claude Collective Async Orchestrator")
    parser.add_argument("--base-path", default="/home/<USER>/github/cc-v1/_dev-automation-beta")
    parser.add_argument("--submit-task", nargs=2, metavar=("TYPE", "CONTEXT"))
    parser.add_argument("--status", action="store_true")
    
    args = parser.parse_args()
    
    orchestrator = AsyncOrchestrator(Path(args.base_path))
    
    if args.submit_task:
        await orchestrator.start()
        task_id = await orchestrator.submit_task(args.submit_task[0], args.submit_task[1])
        print(f"Task submitted: {task_id}")
    elif args.status:
        print(json.dumps(orchestrator.get_status(), indent=2))
    else:
        # Run orchestrator
        await orchestrator.start()
        # Keep running
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Shutting down...")

if __name__ == "__main__":
    asyncio.run(main())