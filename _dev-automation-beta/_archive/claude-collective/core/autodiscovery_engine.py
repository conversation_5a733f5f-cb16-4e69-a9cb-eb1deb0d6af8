#!/usr/bin/env python3
"""
Autodiscovery Engine for Claude Collective
Continuously scans documentation and project state to discover and queue tasks
"""

import asyncio
import json
import logging
import re
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Set, Optional, Tuple, Any
from dataclasses import dataclass, field
import hashlib

from .logger_config import setup_logger
logger, _, _ = setup_logger('autodiscovery')

@dataclass
class DiscoveredIntent:
    """Represents an intent discovered from documentation"""
    source: str
    intent_type: str  # feature, bug, improvement, integration, etc.
    description: str
    priority: int = 5  # 1-10 scale
    confidence: float = 0.5  # 0-1 confidence in interpretation
    context: Dict = field(default_factory=dict)
    hash: str = ""
    
    def __post_init__(self):
        # Create unique hash for deduplication
        # Normalize description for better deduplication
        normalized = self.description.lower().strip()
        # Remove common variations
        normalized = re.sub(r'\s+', ' ', normalized)  # Normalize whitespace
        normalized = re.sub(r'[^a-z0-9 ]', '', normalized)  # Remove punctuation
        # Keep first 100 chars to avoid minor variations
        normalized = normalized[:100]
        content = f"{self.intent_type}:{normalized}"
        self.hash = hashlib.md5(content.encode()).hexdigest()[:8]

class AutodiscoveryEngine:
    """Continuously discovers tasks from documentation and project state"""
    
    def __init__(self, cc_root: Path, orchestrator=None):
        self.cc_root = cc_root
        self.orchestrator = orchestrator
        self.dev_automation_path = cc_root / "_dev-automation-beta"
        self.discovered_intents: Set[str] = set()  # Track by hash to avoid duplicates
        self.scan_interval = 60  # Scan every minute
        self.running = False
        
        # Intent patterns for different document types - more selective
        self.intent_patterns = {
            # Direct task indicators
            "task": [
                r"(?:TODO|TASK|FIXME):\s*([A-Z][^.]+)",
                r"(?:Need to|Should|Must)\s+(implement|create|fix|update|add|integrate)\s+(.+)",
            ],
            # Feature requests
            "feature": [
                r"(?:Feature|Enhancement):\s*([A-Z][^.]+)",
                r"(?:Missing feature|New feature):\s*(.+)",
            ],
            # Integration needs
            "integration": [
                r"(?:Integration needed):\s*(.+)",
                r"(?:Connect|Integrate)\s+(\w+)\s+(?:to|with)\s+(\w+)",
            ],
            # Bug indicators
            "bug": [
                r"(?:BUG|ISSUE):\s*([A-Z][^.]+)",
                r"(?:Fix needed):\s*(.+)",
            ],
            # Priority indicators
            "priority": [
                r"(?:URGENT|CRITICAL|HIGH PRIORITY):\s*([A-Z][^.]+)",
            ]
        }
        
    async def start(self):
        """Start the autodiscovery engine"""
        self.running = True
        logger.info("🔍 Starting Autodiscovery Engine...")
        
        # Run initial scan
        await self.scan_all_sources()
        
        # Start continuous scanning
        asyncio.create_task(self._continuous_scan())
        
    async def stop(self):
        """Stop the autodiscovery engine"""
        self.running = False
        logger.info("Stopping Autodiscovery Engine...")
        
    async def _continuous_scan(self):
        """Continuously scan for new intents"""
        while self.running:
            try:
                await asyncio.sleep(self.scan_interval)
                await self.scan_all_sources()
            except Exception as e:
                logger.error(f"Error in continuous scan: {e}")
                await asyncio.sleep(5)  # Brief pause on error
                
    async def scan_all_sources(self):
        """Scan all documentation sources for intents"""
        logger.info("🔍 Scanning documentation for intents...")
        
        intents = []
        
        # 1. Scan main documentation files
        intents.extend(await self._scan_main_docs())
        
        # 2. Scan feature documentation
        intents.extend(await self._scan_feature_docs())
        
        # 3. Scan code comments for TODOs
        intents.extend(await self._scan_code_todos())
        
        # 4. Scan git commits for fix indicators
        intents.extend(await self._scan_recent_commits())
        
        # 5. Scan workflow states - disabled due to database schema issues
        # intents.extend(await self._scan_workflow_states())
        
        # 6. Scan test failures
        intents.extend(await self._scan_test_results())
        
        # Process and queue discovered intents
        await self._process_intents(intents)
        
    async def _scan_main_docs(self) -> List[DiscoveredIntent]:
        """Scan main documentation files"""
        intents = []
        
        # Key documentation files
        doc_files = [
            "CLAUDE.md",
            "AGENTS.md", 
            "README.md",
            "ROOT_CLAUDE.md",
            "_docs/README.md",
            "_dev-automation-beta/docs/README.md"
        ]
        
        for doc_file in doc_files:
            file_path = self.cc_root / doc_file
            if file_path.exists():
                try:
                    content = file_path.read_text()
                    
                    # Extract priority tasks from CLAUDE.md style
                    if "Priority Tasks:" in content:
                        for line in content.split('\n'):
                            if "Priority Tasks:" in line:
                                tasks_str = line.split(":", 1)[1].strip()
                                for task in tasks_str.split(","):
                                    task = task.strip()
                                    # Skip if too short or looks like a fragment
                                    if task and len(task) > 10 and not task.endswith("..."):
                                        intents.append(DiscoveredIntent(
                                            source=doc_file,
                                            intent_type="task",
                                            description=task,
                                            priority=8,
                                            confidence=0.9
                                        ))
                                        
                    # Extract Next Steps sections
                    if "Next Steps:" in content or "## Next" in content:
                        in_next_section = False
                        for line in content.split('\n'):
                            if "Next Steps:" in line or "## Next" in line:
                                in_next_section = True
                                continue
                            elif line.startswith("#") and in_next_section:
                                break
                            elif in_next_section and line.strip().startswith(("-", "*", "•")):
                                task = line.strip().lstrip("-*• ").strip()
                                if task:
                                    intents.append(DiscoveredIntent(
                                        source=doc_file,
                                        intent_type="task",
                                        description=task,
                                        priority=7,
                                        confidence=0.8
                                    ))
                                    
                    # Scan for intent patterns
                    for intent_type, patterns in self.intent_patterns.items():
                        for pattern in patterns:
                            matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
                            for match in matches:
                                description = match.group(1) if match.groups() else match.group(0)
                                description = description.strip()
                                # Skip fragments and very short descriptions
                                if len(description) > 15 and not description.endswith("...") and description.count(" ") > 2:
                                    intents.append(DiscoveredIntent(
                                        source=doc_file,
                                        intent_type=intent_type,
                                        description=description,
                                        priority=9 if intent_type == "priority" else 6,
                                        confidence=0.7
                                    ))
                                
                except Exception as e:
                    logger.error(f"Error scanning {doc_file}: {e}")
                    
        return intents
        
    async def _scan_feature_docs(self) -> List[DiscoveredIntent]:
        """Scan feature-specific documentation"""
        intents = []
        
        # Look for feature docs
        feature_patterns = ["**/FEATURE*.md", "**/features/*.md", "**/docs/*.md"]
        
        for pattern in feature_patterns:
            for doc_path in self.cc_root.glob(pattern):
                try:
                    content = doc_path.read_text()
                    
                    # Look for implementation status
                    if "Status:" in content:
                        for line in content.split('\n'):
                            if "Status:" in line and any(word in line.lower() for word in ["todo", "pending", "planned"]):
                                # Extract feature name from file or previous line
                                feature_name = doc_path.stem.replace("_", " ").title()
                                intents.append(DiscoveredIntent(
                                    source=str(doc_path.relative_to(self.cc_root)),
                                    intent_type="feature",
                                    description=f"Implement {feature_name}",
                                    priority=6,
                                    confidence=0.8
                                ))
                                
                except Exception as e:
                    logger.error(f"Error scanning {doc_path}: {e}")
                    
        return intents
        
    async def _scan_code_todos(self) -> List[DiscoveredIntent]:
        """Scan code files for TODO comments"""
        intents = []
        
        # Scan common code file types
        code_patterns = ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "**/*.py"]
        
        # Limit scanning to avoid performance issues
        max_files = 100
        file_count = 0
        
        for pattern in code_patterns:
            for code_path in self.cc_root.glob(pattern):
                if file_count >= max_files:
                    break
                    
                # Skip node_modules, dist, build directories
                if any(skip in str(code_path) for skip in ["node_modules", "dist", "build", ".next"]):
                    continue
                    
                try:
                    content = code_path.read_text()
                    
                    # Look for TODO/FIXME comments
                    todo_pattern = r"(?://|#)\s*(TODO|FIXME|HACK|BUG|XXX):\s*(.+)"
                    matches = re.finditer(todo_pattern, content, re.IGNORECASE)
                    
                    for match in matches:
                        todo_type = match.group(1).upper()
                        description = match.group(2).strip()
                        
                        intents.append(DiscoveredIntent(
                            source=str(code_path.relative_to(self.cc_root)),
                            intent_type="bug" if todo_type in ["BUG", "FIXME"] else "task",
                            description=f"{description} (in {code_path.name})",
                            priority=5,
                            confidence=0.9
                        ))
                        
                    file_count += 1
                    
                except Exception as e:
                    # Skip files that can't be read
                    pass
                    
        return intents
        
    async def _scan_recent_commits(self) -> List[DiscoveredIntent]:
        """Scan recent git commits for indicators"""
        intents = []
        
        try:
            import subprocess
            
            # Get recent commits
            result = subprocess.run(
                ["git", "log", "--oneline", "-20", "--grep=WIP\\|TODO\\|FIXME"],
                cwd=self.cc_root,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if line:
                        # Extract commit message
                        parts = line.split(' ', 1)
                        if len(parts) > 1:
                            commit_msg = parts[1]
                            
                            # Look for WIP or TODO indicators
                            if any(indicator in commit_msg.upper() for indicator in ["WIP", "TODO", "FIXME"]):
                                intents.append(DiscoveredIntent(
                                    source="git history",
                                    intent_type="task",
                                    description=f"Complete: {commit_msg}",
                                    priority=7,
                                    confidence=0.7
                                ))
                                
        except Exception as e:
            logger.error(f"Error scanning git commits: {e}")
            
        return intents
        
    async def _scan_workflow_states(self) -> List[DiscoveredIntent]:
        """Scan workflow state files"""
        intents = []
        
        # Check automation database
        db_path = self.dev_automation_path / "memory" / "automation.db"
        if db_path.exists():
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Check if table exists and has expected columns
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='template_executions'")
                if cursor.fetchone():
                    # Look for failed templates that need retry
                    cursor.execute("""
                        SELECT DISTINCT template_name, context
                        FROM template_executions
                        WHERE status = 'failed'
                        AND timestamp > datetime('now', '-7 days')
                        ORDER BY timestamp DESC
                        LIMIT 10
                    """)
                else:
                    self.logger.warning("template_executions table not found in automation.db")
                    return intents
                
                for row in cursor.fetchall():
                    template, context = row
                    intents.append(DiscoveredIntent(
                        source="automation memory",
                        intent_type="bug",
                        description=f"Fix and retry {template}: {context[:50]}...",
                        priority=8,
                        confidence=0.9
                    ))
                    
                conn.close()
                
            except Exception as e:
                logger.error(f"Error scanning automation database: {e}")
                
        return intents
        
    async def _scan_test_results(self) -> List[DiscoveredIntent]:
        """Scan for test failures"""
        intents = []
        
        # Look for test result files
        test_patterns = ["**/test-results.json", "**/.test-results/*", "**/coverage/lcov-report/index.html"]
        
        for pattern in test_patterns:
            for test_path in self.cc_root.glob(pattern):
                try:
                    if test_path.suffix == ".json":
                        with open(test_path) as f:
                            data = json.load(f)
                            
                        # Look for failures
                        if "failures" in data and data["failures"] > 0:
                            intents.append(DiscoveredIntent(
                                source=str(test_path.relative_to(self.cc_root)),
                                intent_type="bug",
                                description=f"Fix {data['failures']} failing tests",
                                priority=9,
                                confidence=1.0
                            ))
                            
                except Exception as e:
                    logger.error(f"Error scanning test results {test_path}: {e}")
                    
        return intents
        
    async def _process_intents(self, intents: List[DiscoveredIntent]):
        """Process discovered intents and submit new ones to orchestrator"""
        new_count = 0
        
        for intent in intents:
            # Skip if already discovered
            if intent.hash in self.discovered_intents:
                continue
                
            # Skip low confidence intents
            if intent.confidence < 0.5:
                continue
                
            # Add to discovered set
            self.discovered_intents.add(intent.hash)
            
            # Submit to orchestrator if available
            if self.orchestrator:
                await self._submit_intent_as_task(intent)
                new_count += 1
                
        if new_count > 0:
            logger.info(f"🎯 Discovered and submitted {new_count} new intents")
            
    async def _submit_intent_as_task(self, intent: DiscoveredIntent):
        """Convert intent to task and submit to orchestrator"""
        # Map intent type to task type
        task_type_map = {
            "feature": "implement",
            "bug": "fix_bug",
            "integration": "integrate",
            "task": "implement",
            "priority": "implement"
        }
        
        task_type = task_type_map.get(intent.intent_type, "analyze")
        
        # Determine role based on intent
        if intent.priority >= 8 or "critical" in intent.description.lower():
            role = "DEVELOPER_SENIOR"
        elif intent.intent_type == "bug":
            role = "QA"
        elif "research" in intent.description.lower():
            role = "RESEARCHER"
        elif "design" in intent.description.lower() or "architect" in intent.description.lower():
            role = "ARCHITECT"
        else:
            role = "DEVELOPER_MIDLEVEL"
            
        # Map role string to enum
        from core.async_orchestrator import AgentRole
        role_map = {
            "ARCHITECT": AgentRole.ARCHITECT,
            "DEVELOPER_SENIOR": AgentRole.DEVELOPER_SENIOR,
            "DEVELOPER_MIDLEVEL": AgentRole.DEVELOPER_MIDLEVEL,
            "QA": AgentRole.QA,
            "RESEARCHER": AgentRole.RESEARCHER
        }
        
        agent_role = role_map.get(role, AgentRole.DEVELOPER_MIDLEVEL)
        
        # Submit task
        task_id = await self.orchestrator.submit_task(
            task_type=task_type,
            context=f"[Autodiscovered from {intent.source}] {intent.description}",
            role=agent_role
        )
        
        logger.info(f"📋 Submitted autodiscovered task {task_id}: {intent.description[:60]}...")
        
    def get_discovery_stats(self) -> Dict[str, Any]:
        """Get statistics about discovered intents"""
        return {
            "total_discovered": len(self.discovered_intents),
            "scan_interval": self.scan_interval,
            "running": self.running,
            "sources_monitored": [
                "Documentation files (*.md)",
                "Code TODOs",
                "Git commits", 
                "Workflow states",
                "Test results"
            ]
        }

# CLI testing
if __name__ == "__main__":
    async def test_discovery():
        engine = AutodiscoveryEngine(Path("/home/<USER>/github/cc-v1"))
        await engine.scan_all_sources()
        print(f"Discovery stats: {engine.get_discovery_stats()}")
        
    asyncio.run(test_discovery())