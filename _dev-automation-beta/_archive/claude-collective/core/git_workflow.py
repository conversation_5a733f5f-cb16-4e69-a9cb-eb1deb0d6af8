#!/usr/bin/env python3
"""
Git Workflow Manager for Claude Collective
Handles branch creation, commits, and PR preparation
"""

import subprocess
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class GitContext:
    """Current git context for the collective"""
    branch_name: str
    base_branch: str = "develop"
    changes_count: int = 0
    last_commit_time: Optional[float] = None
    pr_ready: bool = False

class GitWorkflowManager:
    """Manages git operations for overnight automation"""
    
    def __init__(self, repo_path: Path = Path("/home/<USER>/github/cc-v1")):
        self.repo_path = repo_path
        self.context: Optional[GitContext] = None
        self.commit_threshold = 5  # Create PR after 5 meaningful changes
        
    def initialize_night_session(self) -> GitContext:
        """Initialize a new night session with its own branch"""
        # Create branch name with date
        date_str = datetime.now().strftime("%Y-%m-%d")
        branch_name = f"night_{date_str}"
        
        logger.info(f"Initializing night session on branch: {branch_name}")
        
        # Check current branch
        current_branch = self._run_git(["rev-parse", "--abbrev-ref", "HEAD"]).strip()
        
        if current_branch == branch_name:
            logger.info(f"Already on {branch_name}, continuing...")
            self.context = GitContext(branch_name=branch_name)
            self._save_context()
            return self.context
        
        # Ensure we're on develop
        self._run_git(["checkout", "develop"])
        
        # Try to pull, but don't fail if no remote
        try:
            self._run_git(["pull", "origin", "develop"])
        except subprocess.CalledProcessError:
            logger.warning("Could not pull from origin/develop - continuing with local develop")
        
        # Create and checkout new branch
        try:
            self._run_git(["checkout", "-b", branch_name])
        except subprocess.CalledProcessError:
            # Branch might already exist from earlier run
            self._run_git(["checkout", branch_name])
            
        self.context = GitContext(branch_name=branch_name)
        self._save_context()
        
        return self.context
        
    def stage_and_commit(self, files: List[str], message: str, task_id: str) -> bool:
        """Stage files and create a commit with structured message"""
        if not self.context:
            logger.error("No git context initialized")
            return False
            
        try:
            # Stage specific files
            for file in files:
                self._run_git(["add", file])
                
            # Create structured commit message
            commit_msg = f"""feat: {message}

Task ID: {task_id}
Night Session: {self.context.branch_name}
Automated by Claude Collective

Changes:
{self._get_staged_changes_summary()}
"""
            
            # Commit
            self._run_git(["commit", "-m", commit_msg])
            
            # Update context
            self.context.changes_count += 1
            self.context.last_commit_time = datetime.now().timestamp()
            self._save_context()
            
            # Check if we should prepare a PR
            if self.context.changes_count >= self.commit_threshold:
                self.prepare_pr()
                
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Git commit failed: {e}")
            return False
            
    def prepare_pr(self) -> Optional[str]:
        """Prepare a PR (push branch and create PR description)"""
        if not self.context or self.context.pr_ready:
            return None
            
        try:
            # Push branch
            self._run_git(["push", "-u", "origin", self.context.branch_name])
            
            # Get commit summary
            commits = self._get_branch_commits()
            
            # Create PR description
            pr_description = f"""## 🌙 Night Session Summary - {self.context.branch_name}

### Overview
Automated development session with {len(commits)} commits containing {self.context.changes_count} meaningful changes.

### Commits
{self._format_commits_for_pr(commits)}

### Changes Summary
{self._get_changes_summary()}

### Testing Status
- [ ] Unit tests passed
- [ ] Integration tests passed
- [ ] Type checking passed
- [ ] Linting passed

### Review Checklist
- [ ] Code follows project standards
- [ ] No hardcoded values
- [ ] Proper error handling
- [ ] Documentation updated

---
*This PR was prepared by Claude Collective overnight automation.*
*Please review carefully before merging to develop.*
"""
            
            # Save PR description
            pr_file = self.repo_path / f".claude-collective-pr-{self.context.branch_name}.md"
            pr_file.write_text(pr_description)
            
            # Mark as PR ready
            self.context.pr_ready = True
            self._save_context()
            
            logger.info(f"PR prepared. Description saved to: {pr_file}")
            
            # Create GitHub PR using gh CLI if available
            try:
                result = subprocess.run(
                    ["gh", "pr", "create",
                     "--title", f"🌙 Night Session: {self.context.branch_name}",
                     "--body-file", str(pr_file),
                     "--base", self.context.base_branch,
                     "--draft"],  # Always create as draft for manual review
                    cwd=self.repo_path,
                    capture_output=True,
                    text=True,
                    check=True
                ).stdout
                pr_url = result.strip()
                logger.info(f"Draft PR created: {pr_url}")
                return pr_url
            except:
                logger.info("GitHub CLI not available. Push completed, create PR manually.")
                return None
                
        except subprocess.CalledProcessError as e:
            logger.error(f"PR preparation failed: {e}")
            return None
            
    def should_create_new_pr(self) -> bool:
        """Check if we should create a new PR"""
        if not self.context:
            return False
            
        # Create new PR if:
        # 1. Current PR is ready and we have new changes
        # 2. We've accumulated enough changes
        # 3. It's been too long since last PR
        
        if self.context.pr_ready and self.context.changes_count > 0:
            return True
            
        if self.context.changes_count >= self.commit_threshold:
            return True
            
        return False
        
    def start_new_pr_branch(self) -> GitContext:
        """Start a new branch for the next PR in the same session"""
        if not self.context:
            return self.initialize_night_session()
            
        # Create incremented branch name
        base_name = self.context.branch_name
        counter = 2
        
        while True:
            new_branch = f"{base_name}_pr{counter}"
            try:
                self._run_git(["checkout", "-b", new_branch])
                break
            except:
                counter += 1
                
        self.context = GitContext(
            branch_name=new_branch,
            base_branch=self.context.base_branch
        )
        self._save_context()
        
        return self.context
        
    def _run_git(self, args: List[str]) -> str:
        """Run a git command"""
        cmd = ["git"] + args
        result = subprocess.run(
            cmd,
            cwd=self.repo_path,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout
        
    def _get_staged_changes_summary(self) -> str:
        """Get summary of staged changes"""
        try:
            diff = self._run_git(["diff", "--cached", "--stat"])
            return diff
        except:
            return "Unable to get changes summary"
            
    def _get_branch_commits(self) -> List[Dict[str, str]]:
        """Get commits on current branch"""
        try:
            log = self._run_git([
                "log",
                f"{self.context.base_branch}..HEAD",
                "--pretty=format:%H|%s|%an|%ai"
            ])
            
            commits = []
            for line in log.strip().split('\n'):
                if line:
                    parts = line.split('|')
                    commits.append({
                        "hash": parts[0][:8],
                        "subject": parts[1],
                        "author": parts[2],
                        "date": parts[3]
                    })
            return commits
        except:
            return []
            
    def _format_commits_for_pr(self, commits: List[Dict[str, str]]) -> str:
        """Format commits for PR description"""
        lines = []
        for commit in commits:
            lines.append(f"- `{commit['hash']}` {commit['subject']}")
        return '\n'.join(lines)
        
    def _get_changes_summary(self) -> str:
        """Get summary of all changes in branch"""
        try:
            stats = self._run_git([
                "diff",
                f"{self.context.base_branch}...HEAD",
                "--stat"
            ])
            return f"```\n{stats}\n```"
        except:
            return "Unable to generate changes summary"
            
    def _save_context(self):
        """Save git context to file"""
        context_file = self.repo_path / ".claude-collective-git-context.json"
        with open(context_file, 'w') as f:
            json.dump({
                "branch_name": self.context.branch_name,
                "base_branch": self.context.base_branch,
                "changes_count": self.context.changes_count,
                "last_commit_time": self.context.last_commit_time,
                "pr_ready": self.context.pr_ready
            }, f, indent=2)
            
    def load_context(self) -> Optional[GitContext]:
        """Load existing git context"""
        context_file = self.repo_path / ".claude-collective-git-context.json"
        if context_file.exists():
            with open(context_file, 'r') as f:
                data = json.load(f)
                self.context = GitContext(**data)
                return self.context
        return None