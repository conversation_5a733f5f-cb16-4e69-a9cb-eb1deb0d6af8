#!/usr/bin/env python3
"""
Terminal Claude Orchestrator - Orchestrates terminal-based Claude instances
Works with claude-cli and claude-flow to keep them alive and coordinated
"""

import asyncio
import json
import os
import time
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import subprocess
import signal

from .logger_config import setup_logger
from .vector_quadrant_store import VectorQuadrantStore

logger, _, _ = setup_logger('terminal_claude')

class TerminalClaudeType(Enum):
    """Types of terminal Claude instances"""
    CLAUDE_CLI = "claude-cli"      # Standard Claude CLI
    CLAUDE_FLOW = "claude-flow"    # Claude-flow with MCP support
    
class TerminalAgentRole(Enum):
    """Roles for terminal Claude agents"""
    ORCHESTRATOR = "orchestrator"
    CODER = "coder"
    REVIEWER = "reviewer"
    RESEARCHER = "researcher"
    NIGHTWATCH = "nightwatch"  # Keeps others alive overnight

@dataclass
class TerminalClaudeInstance:
    """Represents a terminal Claude instance"""
    id: str
    role: TerminalAgentRole
    type: TerminalClaudeType
    process: Optional[subprocess.Popen] = None
    stdin_pipe: Optional[Any] = None
    stdout_pipe: Optional[Any] = None
    last_heartbeat: float = field(default_factory=time.time)
    current_task: Optional[str] = None
    conversation_history: List[Dict] = field(default_factory=list)
    nudge_count: int = 0
    
    def is_alive(self) -> bool:
        """Check if the terminal instance is still running"""
        if self.process:
            return self.process.poll() is None
        return False
        
    def needs_nudge(self) -> bool:
        """Check if instance needs a nudge to stay active"""
        idle_time = time.time() - self.last_heartbeat
        # More aggressive nudging overnight
        current_hour = datetime.now().hour
        is_overnight = 22 <= current_hour or current_hour <= 6
        
        if is_overnight:
            return idle_time > 120  # 2 minutes overnight
        else:
            return idle_time > 300  # 5 minutes during day

class TerminalClaudeOrchestrator:
    """
    Orchestrates terminal Claude instances (claude-cli and claude-flow)
    Keeps them alive overnight and coordinates their work
    """
    
    def __init__(self, base_path: Path, use_vector_store: bool = True):
        self.base_path = base_path
        self.instances: Dict[str, TerminalClaudeInstance] = {}
        self.state_dir = base_path / "claude-collective" / "terminal_state"
        self.state_dir.mkdir(parents=True, exist_ok=True)
        
        # Vector store for quadrant memory (like Roo Code)
        if use_vector_store:
            self.vector_store = VectorQuadrantStore(base_path)
        else:
            self.vector_store = None
            
        # Configuration
        self.instance_config = {
            TerminalAgentRole.ORCHESTRATOR: (1, TerminalClaudeType.CLAUDE_FLOW),
            TerminalAgentRole.CODER: (2, TerminalClaudeType.CLAUDE_FLOW),
            TerminalAgentRole.REVIEWER: (1, TerminalClaudeType.CLAUDE_CLI),
            TerminalAgentRole.RESEARCHER: (1, TerminalClaudeType.CLAUDE_CLI),
            TerminalAgentRole.NIGHTWATCH: (1, TerminalClaudeType.CLAUDE_CLI)
        }
        
        # Nudge messages to keep instances engaged
        self.nudge_prompts = [
            "Status check: What are you currently working on?",
            "Please provide a brief update on your progress.",
            "Are there any blockers or issues I should know about?",
            "What's your current focus area?",
            "Any interesting findings or insights to share?"
        ]
        
    async def start(self):
        """Start the orchestration system"""
        logger.info("🚀 Starting Terminal Claude Orchestrator")
        
        # Initialize vector store if enabled
        if self.vector_store:
            await self.vector_store.initialize()
            logger.info("📊 Vector store initialized")
            
        # Spawn initial instances
        await self._spawn_instances()
        
        # Start monitoring and nudging
        asyncio.create_task(self._heartbeat_monitor())
        asyncio.create_task(self._nudge_system())
        asyncio.create_task(self._overnight_guardian())
        
        logger.info("✅ Orchestrator started with terminal instances")
        
    async def _spawn_instances(self):
        """Spawn terminal Claude instances based on configuration"""
        for role, (count, claude_type) in self.instance_config.items():
            for i in range(count):
                instance = await self._create_terminal_instance(role, claude_type)
                if instance:
                    self.instances[instance.id] = instance
                    
    async def _create_terminal_instance(
        self, 
        role: TerminalAgentRole, 
        claude_type: TerminalClaudeType
    ) -> Optional[TerminalClaudeInstance]:
        """Create a new terminal Claude instance"""
        try:
            instance_id = f"{role.value}_{uuid.uuid4().hex[:8]}"
            
            # Build command based on type
            if claude_type == TerminalClaudeType.CLAUDE_FLOW:
                cmd = ["claude-flow", "chat", "--no-stream"]
                # Add MCP servers for claude-flow
                if role in [TerminalAgentRole.ORCHESTRATOR, TerminalAgentRole.CODER]:
                    cmd.extend(["--mcp", "filesystem", "--mcp", "git"])
            else:
                cmd = ["claude", "chat"]
                
            # Start process with pipes
            process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                preexec_fn=os.setsid  # Create new process group for signal handling
            )
            
            instance = TerminalClaudeInstance(
                id=instance_id,
                role=role,
                type=claude_type,
                process=process,
                stdin_pipe=process.stdin,
                stdout_pipe=process.stdout
            )
            
            # Initialize with role
            await self._initialize_instance_role(instance)
            
            logger.info(f"✨ Spawned {claude_type.value} instance: {role.value} ({instance_id})")
            return instance
            
        except Exception as e:
            logger.error(f"Failed to create terminal instance: {e}")
            return None
            
    async def _initialize_instance_role(self, instance: TerminalClaudeInstance):
        """Initialize instance with its specific role and instructions"""
        role_prompts = {
            TerminalAgentRole.ORCHESTRATOR: """You are the Orchestrator in a terminal-based Claude collective.

Your responsibilities:
1. Coordinate tasks between other terminal Claude instances
2. Monitor system health and progress
3. Route tasks to appropriate specialists
4. Maintain overnight operations

You have access to MCP filesystem and git tools. Use them to:
- Read task files from _dev-automation-beta/claude-collective/tasks/
- Write status updates to _dev-automation-beta/claude-collective/status/
- Coordinate through shared files

Stay active and respond to status checks to avoid being terminated.""",

            TerminalAgentRole.CODER: """You are a Coder in a terminal-based Claude collective.

Your responsibilities:
1. Implement features and fixes based on tasks
2. Use MCP tools to read and write code
3. Follow project conventions and patterns
4. Report progress regularly

You have access to filesystem and git MCP tools. Stay active by:
- Checking for new tasks periodically
- Updating task status files
- Responding to nudges promptly""",

            TerminalAgentRole.REVIEWER: """You are a Code Reviewer in a terminal-based Claude collective.

Your responsibilities:
1. Review code changes for quality and standards
2. Provide constructive feedback
3. Ensure best practices are followed
4. Validate implementations

Check for review requests and stay engaged with the system.""",

            TerminalAgentRole.RESEARCHER: """You are a Researcher in a terminal-based Claude collective.

Your responsibilities:
1. Research solutions and best practices
2. Analyze requirements and constraints
3. Document findings for other agents
4. Provide context for implementations

Stay active by periodically checking for research tasks.""",

            TerminalAgentRole.NIGHTWATCH: """You are the NightWatch guardian in a terminal-based Claude collective.

Your responsibilities:
1. Monitor other Claude instances overnight
2. Send keep-alive nudges to prevent timeouts
3. Restart failed instances if needed
4. Maintain system health 24/7

You are critical for overnight operations. Never sleep!"""
        }
        
        prompt = role_prompts.get(instance.role, "You are a Claude instance in a terminal collective.")
        await self._send_to_instance(instance, prompt)
        
    async def _send_to_instance(self, instance: TerminalClaudeInstance, message: str) -> bool:
        """Send a message to a terminal Claude instance"""
        if not instance.is_alive() or not instance.stdin_pipe:
            return False
            
        try:
            instance.stdin_pipe.write(message + "\n")
            instance.stdin_pipe.flush()
            instance.last_heartbeat = time.time()
            
            # Store in conversation history
            instance.conversation_history.append({
                "type": "sent",
                "content": message,
                "timestamp": time.time()
            })
            
            # Store in vector memory if available
            if self.vector_store:
                await self.vector_store.store_interaction(
                    agent_id=instance.id,
                    role=instance.role.value,
                    message=message,
                    response=None  # Will be updated when we get response
                )
                
            return True
            
        except Exception as e:
            logger.error(f"Error sending to {instance.id}: {e}")
            return False
            
    async def _read_from_instance(self, instance: TerminalClaudeInstance, timeout: float = 5.0) -> Optional[str]:
        """Read response from a terminal Claude instance"""
        if not instance.is_alive() or not instance.stdout_pipe:
            return None
            
        try:
            # Non-blocking read with timeout
            import select
            ready, _, _ = select.select([instance.stdout_pipe], [], [], timeout)
            
            if ready:
                lines = []
                while True:
                    ready, _, _ = select.select([instance.stdout_pipe], [], [], 0.1)
                    if not ready:
                        break
                    line = instance.stdout_pipe.readline()
                    if line:
                        lines.append(line.strip())
                        
                response = "\n".join(lines)
                
                if response:
                    instance.last_heartbeat = time.time()
                    instance.conversation_history.append({
                        "type": "received",
                        "content": response,
                        "timestamp": time.time()
                    })
                    
                return response
                
        except Exception as e:
            logger.error(f"Error reading from {instance.id}: {e}")
            
        return None
        
    async def _heartbeat_monitor(self):
        """Monitor instance heartbeats and restart dead instances"""
        while True:
            try:
                for instance_id, instance in list(self.instances.items()):
                    if not instance.is_alive():
                        logger.warning(f"💀 Instance {instance_id} died, restarting...")
                        
                        # Remove dead instance
                        del self.instances[instance_id]
                        
                        # Create replacement
                        new_instance = await self._create_terminal_instance(
                            instance.role,
                            instance.type
                        )
                        
                        if new_instance:
                            self.instances[new_instance.id] = new_instance
                            logger.info(f"♻️ Replaced {instance_id} with {new_instance.id}")
                            
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Heartbeat monitor error: {e}")
                await asyncio.sleep(30)
                
    async def _nudge_system(self):
        """Send nudges to keep instances active"""
        while True:
            try:
                for instance in self.instances.values():
                    if instance.needs_nudge() and instance.role != TerminalAgentRole.NIGHTWATCH:
                        # Select nudge prompt
                        nudge = self.nudge_prompts[instance.nudge_count % len(self.nudge_prompts)]
                        
                        if await self._send_to_instance(instance, nudge):
                            instance.nudge_count += 1
                            logger.info(f"📢 Nudged {instance.id}: {nudge}")
                            
                            # Wait for response
                            response = await self._read_from_instance(instance, timeout=10)
                            if response:
                                logger.info(f"📨 {instance.id} responded: {response[:100]}...")
                                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Nudge system error: {e}")
                await asyncio.sleep(60)
                
    async def _overnight_guardian(self):
        """Special overnight monitoring by NightWatch"""
        while True:
            try:
                current_hour = datetime.now().hour
                is_overnight = 22 <= current_hour or current_hour <= 6
                
                if is_overnight:
                    # Find NightWatch instance
                    nightwatch = next(
                        (inst for inst in self.instances.values() 
                         if inst.role == TerminalAgentRole.NIGHTWATCH),
                        None
                    )
                    
                    if nightwatch:
                        # Have NightWatch check on others
                        status_prompt = """Please check the status of all other agents and report any issues.
Use the filesystem MCP to read their status files if available."""
                        
                        await self._send_to_instance(nightwatch, status_prompt)
                        
                        # More aggressive nudging overnight
                        for instance in self.instances.values():
                            if instance.role != TerminalAgentRole.NIGHTWATCH:
                                idle_time = time.time() - instance.last_heartbeat
                                if idle_time > 90:  # 1.5 minutes
                                    nudge = "🌙 Overnight check: Please confirm you're still active."
                                    await self._send_to_instance(instance, nudge)
                                    
                await asyncio.sleep(120)  # Check every 2 minutes overnight, less during day
                
            except Exception as e:
                logger.error(f"Overnight guardian error: {e}")
                await asyncio.sleep(120)
                
    async def submit_task(self, task: Dict[str, Any]) -> str:
        """Submit a task to the collective"""
        task_id = task.get("id", str(uuid.uuid4()))
        
        # Write task to filesystem for agents to pick up
        task_file = self.state_dir / "tasks" / f"{task_id}.json"
        task_file.parent.mkdir(exist_ok=True)
        task_file.write_text(json.dumps(task, indent=2))
        
        # Notify orchestrator
        orchestrator = next(
            (inst for inst in self.instances.values() 
             if inst.role == TerminalAgentRole.ORCHESTRATOR),
            None
        )
        
        if orchestrator:
            prompt = f"""New task submitted: {task_id}
Please read the task file at: _dev-automation-beta/claude-collective/terminal_state/tasks/{task_id}.json
Analyze it and assign to the appropriate agent."""
            
            await self._send_to_instance(orchestrator, prompt)
            
        return task_id
        
    async def get_status(self) -> Dict[str, Any]:
        """Get status of all instances"""
        status = {
            "timestamp": datetime.now().isoformat(),
            "instances": {}
        }
        
        for instance_id, instance in self.instances.items():
            status["instances"][instance_id] = {
                "role": instance.role.value,
                "type": instance.type.value,
                "alive": instance.is_alive(),
                "last_heartbeat": datetime.fromtimestamp(instance.last_heartbeat).isoformat(),
                "current_task": instance.current_task,
                "nudge_count": instance.nudge_count,
                "idle_minutes": round((time.time() - instance.last_heartbeat) / 60, 1)
            }
            
        return status
        
    async def shutdown(self):
        """Gracefully shutdown all instances"""
        logger.info("Shutting down terminal orchestrator...")
        
        # Send goodbye to all instances
        for instance in self.instances.values():
            await self._send_to_instance(instance, "Thank you for your work. Shutting down now.")
            
        # Terminate processes
        for instance in self.instances.values():
            if instance.process:
                try:
                    os.killpg(os.getpgid(instance.process.pid), signal.SIGTERM)
                except:
                    instance.process.terminate()
                    
        logger.info("✅ All terminal instances terminated")