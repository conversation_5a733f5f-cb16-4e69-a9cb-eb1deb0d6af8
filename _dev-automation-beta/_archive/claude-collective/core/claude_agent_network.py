#!/usr/bin/env python3
"""
Claude Agent Network v1 - Persistent Agent Orchestration
Manages a network of actual Claude agents that stay alive and communicate
"""

import asyncio
import json
import time
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Set, Any

from .logger_config import setup_logger
logger, _, _ = setup_logger('claude_network')

class AgentState(Enum):
    """States of Claude agents in the network"""
    SPAWNING = "spawning"
    IDLE = "idle"
    WORKING = "working"
    WAITING_USER = "waiting_user"
    SLEEPING = "sleeping"
    STALLED = "stalled"
    TERMINATED = "terminated"

class CommunicationType(Enum):
    """Types of inter-agent communication"""
    HEARTBEAT = "heartbeat"          # Keep-alive signal
    NUDGE = "nudge"                  # Wake up sleeping agent
    TASK_HANDOFF = "task_handoff"    # Pass task to another agent
    STATUS_CHECK = "status_check"    # Check agent health
    KNOWLEDGE_SHARE = "knowledge_share"  # Share discoveries
    USER_FOLLOWUP = "user_followup"  # Follow up on user tasks

@dataclass
class ClaudeAgent:
    """Represents a live Claude agent instance"""
    id: str
    name: str
    type: str  # coordinator, researcher, coder, architect, etc.
    session_id: Optional[str] = None
    state: AgentState = AgentState.SPAWNING
    last_heartbeat: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)
    current_task: Optional[str] = None
    memory: Dict[str, Any] = field(default_factory=dict)
    conversation_history: List[Dict] = field(default_factory=list)
    
    def is_alive(self) -> bool:
        """Check if agent is still responsive"""
        return time.time() - self.last_heartbeat < 300  # 5 minute timeout
    
    def needs_nudge(self) -> bool:
        """Check if agent needs a nudge"""
        idle_time = time.time() - self.last_activity
        return (self.state == AgentState.IDLE and idle_time > 180 or  # 3 min idle
                self.state == AgentState.SLEEPING or
                self.state == AgentState.WAITING_USER and idle_time > 600)  # 10 min waiting

@dataclass
class AgentMessage:
    """Message between agents"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    from_agent: str = ""
    to_agent: str = ""  # Can be "all" for broadcast
    type: CommunicationType = CommunicationType.HEARTBEAT
    content: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)

class ClaudeAgentNetwork:
    """Orchestrates a network of persistent Claude agents"""
    
    def __init__(self, base_path: Path):
        self.base_path = base_path
        self.agents: Dict[str, ClaudeAgent] = {}
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.agent_sessions: Dict[str, Any] = {}  # Maps to claude-flow sessions
        
        # Network configuration
        self.min_agents = 3  # Minimum agents to keep alive
        self.max_agents = 10  # Maximum concurrent agents
        self.heartbeat_interval = 60  # seconds
        self.nudge_interval = 180  # seconds
        
        # Persistence
        self.state_file = base_path / "claude-collective" / "state" / "agent_network.json"
        self.state_file.parent.mkdir(parents=True, exist_ok=True)
        
    async def start(self):
        """Start the agent network"""
        logger.info("🌐 Starting Claude Agent Network v1")
        
        # Load persisted state
        self._load_state()
        
        # Start core services
        asyncio.create_task(self._heartbeat_loop())
        asyncio.create_task(self._nudge_loop())
        asyncio.create_task(self._message_processor())
        asyncio.create_task(self._health_monitor())
        
        # Spawn initial agents
        await self._spawn_core_agents()
        
        logger.info("✅ Agent network initialized")
        
    async def _spawn_core_agents(self):
        """Spawn the core set of agents"""
        core_agents = [
            ("Orchestrator", "coordinator"),
            ("NightWatch", "monitor"),  # Overnight operations
            ("Researcher", "researcher"),
            ("Developer", "coder"),
            ("Architect", "architect")
        ]
        
        for name, agent_type in core_agents:
            if len(self.agents) >= self.max_agents:
                break
                
            agent = await self._spawn_agent(name, agent_type)
            if agent:
                self.agents[agent.id] = agent
                logger.info(f"✨ Spawned {agent_type} agent: {name}")
                
    async def _spawn_agent(self, name: str, agent_type: str) -> Optional[ClaudeAgent]:
        """Spawn a new Claude agent using claude-flow"""
        agent_id = f"{agent_type}_{uuid.uuid4().hex[:8]}"
        agent = ClaudeAgent(
            id=agent_id,
            name=name,
            type=agent_type
        )
        
        try:
            # Use claude-flow MCP to spawn agent
            # This would integrate with the actual MCP tools
            spawn_cmd = {
                "tool": "mcp__claude-flow__agent_spawn",
                "parameters": {
                    "type": agent_type,
                    "name": name,
                    "persistent": True,
                    "memory_enabled": True
                }
            }
            
            # In real implementation, this would call the MCP tool
            # For now, we simulate the spawn
            agent.session_id = f"session_{agent_id}"
            agent.state = AgentState.IDLE
            
            # Store in persistent memory
            await self._broadcast_message(AgentMessage(
                from_agent=agent_id,
                to_agent="all",
                type=CommunicationType.HEARTBEAT,
                content={"status": "online", "capabilities": [agent_type]}
            ))
            
            return agent
            
        except Exception as e:
            logger.error(f"Failed to spawn agent {name}: {e}")
            return None
            
    async def _heartbeat_loop(self):
        """Send heartbeats between agents to keep them alive"""
        while True:
            try:
                active_agents = [a for a in self.agents.values() if a.is_alive()]
                
                # Each agent sends heartbeat to neighbors
                for i, agent in enumerate(active_agents):
                    # Send to next agent in circle (creates a ring topology)
                    next_agent = active_agents[(i + 1) % len(active_agents)]
                    
                    message = AgentMessage(
                        from_agent=agent.id,
                        to_agent=next_agent.id,
                        type=CommunicationType.HEARTBEAT,
                        content={
                            "state": agent.state.value,
                            "current_task": agent.current_task,
                            "memory_size": len(agent.memory)
                        }
                    )
                    
                    await self.message_queue.put(message)
                    
                await asyncio.sleep(self.heartbeat_interval)
                
            except Exception as e:
                logger.error(f"Heartbeat error: {e}")
                await asyncio.sleep(10)
                
    async def _nudge_loop(self):
        """Nudge sleeping or stalled agents"""
        while True:
            try:
                for agent in self.agents.values():
                    if agent.needs_nudge():
                        # Find an active agent to send the nudge
                        nudger = self._find_active_agent(exclude=agent.id)
                        if nudger:
                            await self._send_nudge(nudger, agent)
                            
                await asyncio.sleep(self.nudge_interval)
                
            except Exception as e:
                logger.error(f"Nudge loop error: {e}")
                await asyncio.sleep(30)
                
    async def _send_nudge(self, from_agent: ClaudeAgent, to_agent: ClaudeAgent):
        """Send a nudge from one agent to another"""
        nudge_content = {
            "reason": "inactivity",
            "last_activity": to_agent.last_activity,
            "suggestion": self._generate_nudge_suggestion(to_agent)
        }
        
        message = AgentMessage(
            from_agent=from_agent.id,
            to_agent=to_agent.id,
            type=CommunicationType.NUDGE,
            content=nudge_content
        )
        
        await self.message_queue.put(message)
        logger.info(f"📢 {from_agent.name} nudged {to_agent.name}")
        
    def _generate_nudge_suggestion(self, agent: ClaudeAgent) -> str:
        """Generate contextual nudge suggestion"""
        if agent.state == AgentState.WAITING_USER:
            return "Check if the user needs help or follow up on previous tasks"
        elif agent.state == AgentState.SLEEPING:
            return "Wake up and check for new tasks in the queue"
        elif agent.current_task:
            return f"Continue working on: {agent.current_task}"
        else:
            return "Check the task queue or help other agents"
            
    async def _message_processor(self):
        """Process inter-agent messages"""
        while True:
            try:
                message = await self.message_queue.get()
                
                # Route message to recipient(s)
                if message.to_agent == "all":
                    await self._broadcast_message(message)
                else:
                    await self._deliver_message(message)
                    
                # Update agent states based on message
                if message.from_agent in self.agents:
                    sender = self.agents[message.from_agent]
                    sender.last_activity = time.time()
                    
                    if message.type == CommunicationType.HEARTBEAT:
                        sender.last_heartbeat = time.time()
                        
            except Exception as e:
                logger.error(f"Message processing error: {e}")
                
    async def _broadcast_message(self, message: AgentMessage):
        """Broadcast message to all agents"""
        for agent_id, agent in self.agents.items():
            if agent_id != message.from_agent and agent.is_alive():
                # In real implementation, this would use MCP tools
                # to communicate with the actual Claude agent
                agent.conversation_history.append({
                    "from": message.from_agent,
                    "type": message.type.value,
                    "content": message.content,
                    "timestamp": message.timestamp
                })
                
    async def _deliver_message(self, message: AgentMessage):
        """Deliver message to specific agent"""
        if message.to_agent in self.agents:
            recipient = self.agents[message.to_agent]
            
            # Use claude-flow MCP for actual delivery
            comm_cmd = {
                "tool": "mcp__claude-flow__agent_communicate",
                "parameters": {
                    "to": message.to_agent,
                    "message": json.dumps(message.content),
                    "type": message.type.value
                }
            }
            
            # Update local state
            recipient.conversation_history.append({
                "from": message.from_agent,
                "type": message.type.value,
                "content": message.content,
                "timestamp": message.timestamp
            })
            
            # Handle special message types
            if message.type == CommunicationType.NUDGE:
                recipient.state = AgentState.WORKING
                recipient.last_activity = time.time()
                
    def _find_active_agent(self, exclude: Optional[str] = None) -> Optional[ClaudeAgent]:
        """Find an active agent"""
        active_agents = [
            a for a in self.agents.values() 
            if a.is_alive() and 
               a.state in [AgentState.IDLE, AgentState.WORKING] and
               a.id != exclude
        ]
        return active_agents[0] if active_agents else None
        
    async def _health_monitor(self):
        """Monitor agent health and respawn if needed"""
        while True:
            try:
                # Check agent health
                dead_agents = []
                for agent_id, agent in self.agents.items():
                    if not agent.is_alive():
                        dead_agents.append(agent_id)
                        logger.warning(f"💀 Agent {agent.name} is unresponsive")
                        
                # Remove dead agents
                for agent_id in dead_agents:
                    del self.agents[agent_id]
                    
                # Maintain minimum agents
                if len(self.agents) < self.min_agents:
                    await self._spawn_core_agents()
                    
                # Save state
                self._save_state()
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Health monitor error: {e}")
                await asyncio.sleep(30)
                
    def _save_state(self):
        """Save network state to disk"""
        state = {
            "agents": {
                agent_id: {
                    "id": agent.id,
                    "name": agent.name,
                    "type": agent.type,
                    "state": agent.state.value,
                    "last_heartbeat": agent.last_heartbeat,
                    "current_task": agent.current_task,
                    "memory_keys": list(agent.memory.keys())
                }
                for agent_id, agent in self.agents.items()
            },
            "metrics": {
                "total_agents": len(self.agents),
                "active_agents": len([a for a in self.agents.values() if a.is_alive()]),
                "last_save": time.time()
            }
        }
        
        self.state_file.write_text(json.dumps(state, indent=2))
        
    def _load_state(self):
        """Load network state from disk"""
        if self.state_file.exists():
            try:
                state = json.loads(self.state_file.read_text())
                logger.info(f"📂 Loaded state with {len(state['agents'])} agents")
                # In real implementation, would reconnect to existing agents
            except Exception as e:
                logger.error(f"Failed to load state: {e}")
                
    async def submit_task_to_network(self, task: Dict[str, Any]) -> str:
        """Submit a task to the agent network"""
        # Find best agent for the task
        agent = self._select_agent_for_task(task)
        
        if agent:
            task_id = f"task_{uuid.uuid4().hex[:8]}"
            
            # Send task to agent
            message = AgentMessage(
                from_agent="orchestrator",
                to_agent=agent.id,
                type=CommunicationType.TASK_HANDOFF,
                content={
                    "task_id": task_id,
                    "task": task
                }
            )
            
            await self.message_queue.put(message)
            
            # Update agent state
            agent.state = AgentState.WORKING
            agent.current_task = task.get("description", task_id)
            
            logger.info(f"📋 Assigned task {task_id} to {agent.name}")
            return task_id
            
        else:
            logger.warning("No available agents for task")
            return ""
            
    def _select_agent_for_task(self, task: Dict[str, Any]) -> Optional[ClaudeAgent]:
        """Select best agent for a task based on type and availability"""
        task_type = task.get("type", "general")
        
        # Priority order for different task types
        agent_priority = {
            "research": ["researcher", "architect", "coordinator"],
            "coding": ["coder", "architect", "coordinator"],
            "design": ["architect", "coder", "coordinator"],
            "testing": ["coder", "researcher", "coordinator"],
            "general": ["coordinator", "researcher", "coder", "architect"]
        }
        
        preferred_types = agent_priority.get(task_type, agent_priority["general"])
        
        # Find available agent by preference
        for pref_type in preferred_types:
            for agent in self.agents.values():
                if (agent.type == pref_type and 
                    agent.is_alive() and 
                    agent.state in [AgentState.IDLE, AgentState.WORKING]):
                    return agent
                    
        # Return any available agent
        return self._find_active_agent()