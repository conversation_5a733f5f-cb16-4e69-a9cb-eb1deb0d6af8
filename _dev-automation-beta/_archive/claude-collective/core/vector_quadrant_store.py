#!/usr/bin/env python3
"""
Vector Quadrant Store - Similar to Roo Code's implementation
Uses Qdrant vector database for semantic code search and quadrant organization
"""

import asyncio
import hashlib
import json
import time
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
from datetime import datetime

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
    QDRANT_AVAILABLE = True
except ImportError:
    QDRANT_AVAILABLE = False
    
try:
    import chromadb
    CHROMA_AVAILABLE = True
except ImportError:
    CHROMA_AVAILABLE = False

from .logger_config import setup_logger

logger, _, _ = setup_logger('vector_store')

@dataclass
class CodeChunk:
    """Represents a chunk of code for vectorization"""
    id: str
    content: str
    file_path: str
    start_line: int
    end_line: int
    quadrant: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    embedding: Optional[np.ndarray] = None
    
@dataclass
class SearchResult:
    """Search result from vector store"""
    chunk: CodeChunk
    score: float
    
class VectorQuadrantStore:
    """
    Vector store for code quadrants, inspired by Roo Code
    Supports multiple backends: Qdrant (primary), ChromaDB (fallback)
    """
    
    def __init__(self, base_path: Path, backend: str = "auto"):
        self.base_path = base_path
        self.data_dir = base_path / "claude-collective" / "vector_store"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Quadrant definitions (different from Roo's single workspace approach)
        self.quadrants = {
            "frontend": {
                "patterns": ["**/*.tsx", "**/*.jsx", "**/app/**", "**/components/**"],
                "description": "UI components and frontend logic"
            },
            "backend": {
                "patterns": ["**/api/**", "**/server/**", "**/*.py", "**/backend/**"],
                "description": "Server-side logic and APIs"
            },
            "infrastructure": {
                "patterns": ["**/*.yml", "**/*.yaml", "**/docker/**", "**/.github/**"],
                "description": "DevOps and configuration"
            },
            "shared": {
                "patterns": ["**/shared/**", "**/common/**", "**/utils/**"],
                "description": "Shared utilities and types"
            }
        }
        
        # Vector store configuration
        self.vector_dim = 768  # Using same as Roo's nomic-embed-text
        self.collection_name = "code_quadrants"
        self.backend = self._select_backend(backend)
        self.client = None
        
        # Chunk configuration (similar to Roo)
        self.min_chunk_size = 100
        self.max_chunk_size = 1000
        
    def _select_backend(self, backend: str) -> str:
        """Select vector store backend"""
        if backend == "auto":
            if QDRANT_AVAILABLE:
                return "qdrant"
            elif CHROMA_AVAILABLE:
                return "chroma"
            else:
                return "numpy"  # Fallback to simple numpy
        return backend
        
    async def initialize(self):
        """Initialize the vector store"""
        logger.info(f"🚀 Initializing vector store with backend: {self.backend}")
        
        if self.backend == "qdrant":
            await self._init_qdrant()
        elif self.backend == "chroma":
            await self._init_chroma()
        else:
            await self._init_numpy()
            
    async def _init_qdrant(self):
        """Initialize Qdrant vector store"""
        if not QDRANT_AVAILABLE:
            raise ImportError("Qdrant not installed. Run: pip install qdrant-client")
            
        # Try to connect to running Qdrant instance
        try:
            self.client = QdrantClient(host="localhost", port=6333)
            
            # Create collection if it doesn't exist
            collections = await self.client.get_collections()
            if self.collection_name not in [c.name for c in collections.collections]:
                await self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(size=self.vector_dim, distance=Distance.COSINE)
                )
                logger.info(f"✅ Created Qdrant collection: {self.collection_name}")
            else:
                logger.info(f"📊 Using existing Qdrant collection: {self.collection_name}")
                
        except Exception as e:
            logger.warning(f"Qdrant connection failed, using persistent disk storage: {e}")
            # Use disk-based Qdrant
            qdrant_path = self.data_dir / "qdrant"
            self.client = QdrantClient(path=str(qdrant_path))
            
            try:
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(size=self.vector_dim, distance=Distance.COSINE)
                )
            except:
                pass  # Collection already exists
                
    async def _init_chroma(self):
        """Initialize ChromaDB vector store"""
        if not CHROMA_AVAILABLE:
            raise ImportError("ChromaDB not installed. Run: pip install chromadb")
            
        persist_dir = str(self.data_dir / "chroma")
        self.client = chromadb.PersistentClient(path=persist_dir)
        
        # Get or create collection
        self.collection = self.client.get_or_create_collection(
            name=self.collection_name,
            metadata={"description": "Code quadrants vector store"}
        )
        logger.info(f"✅ ChromaDB initialized at: {persist_dir}")
        
    async def _init_numpy(self):
        """Initialize simple numpy-based vector store"""
        self.vectors_file = self.data_dir / "vectors.npz"
        self.metadata_file = self.data_dir / "metadata.json"
        
        if self.vectors_file.exists():
            data = np.load(self.vectors_file)
            self.vectors = data["vectors"]
            self.ids = list(data["ids"])
        else:
            self.vectors = np.empty((0, self.vector_dim))
            self.ids = []
            
        if self.metadata_file.exists():
            self.metadata = json.loads(self.metadata_file.read_text())
        else:
            self.metadata = {}
            
        logger.info("✅ Numpy vector store initialized")
        
    async def index_file(self, file_path: Path) -> List[str]:
        """Index a file and store its chunks"""
        # Determine quadrant
        quadrant = self._determine_quadrant(file_path)
        
        # Read file content
        try:
            content = file_path.read_text(encoding='utf-8')
        except:
            return []
            
        # Skip large files (like Roo)
        if len(content) > 1_000_000:  # 1MB limit
            logger.warning(f"Skipping large file: {file_path}")
            return []
            
        # Create chunks
        chunks = self._create_chunks(content, file_path, quadrant)
        
        # Generate embeddings
        for chunk in chunks:
            chunk.embedding = await self._generate_embedding(chunk.content)
            
        # Store in vector database
        chunk_ids = await self._store_chunks(chunks)
        
        logger.info(f"📝 Indexed {len(chunks)} chunks from {file_path.name} in {quadrant} quadrant")
        return chunk_ids
        
    def _determine_quadrant(self, file_path: Path) -> str:
        """Determine which quadrant a file belongs to"""
        for quadrant, config in self.quadrants.items():
            for pattern in config["patterns"]:
                if file_path.match(pattern):
                    return quadrant
        return "shared"  # Default quadrant
        
    def _create_chunks(self, content: str, file_path: Path, quadrant: str) -> List[CodeChunk]:
        """Create chunks from file content (simplified version of Roo's approach)"""
        chunks = []
        lines = content.split('\n')
        
        # Simple line-based chunking for now
        # TODO: Add tree-sitter support for semantic chunking
        current_chunk = []
        start_line = 0
        
        for i, line in enumerate(lines):
            current_chunk.append(line)
            chunk_text = '\n'.join(current_chunk)
            
            # Check if we should create a chunk
            should_chunk = (
                len(chunk_text) >= self.min_chunk_size and (
                    len(chunk_text) >= self.max_chunk_size or
                    self._is_chunk_boundary(line, lines, i)
                )
            )
            
            if should_chunk:
                chunk_id = hashlib.md5(
                    f"{file_path}:{start_line}:{i}".encode()
                ).hexdigest()
                
                chunks.append(CodeChunk(
                    id=chunk_id,
                    content=chunk_text,
                    file_path=str(file_path),
                    start_line=start_line,
                    end_line=i,
                    quadrant=quadrant,
                    metadata={
                        "language": file_path.suffix,
                        "indexed_at": datetime.now().isoformat()
                    }
                ))
                
                current_chunk = []
                start_line = i + 1
                
        # Don't forget the last chunk
        if current_chunk and len('\n'.join(current_chunk)) >= self.min_chunk_size:
            chunk_id = hashlib.md5(
                f"{file_path}:{start_line}:{len(lines)}".encode()
            ).hexdigest()
            
            chunks.append(CodeChunk(
                id=chunk_id,
                content='\n'.join(current_chunk),
                file_path=str(file_path),
                start_line=start_line,
                end_line=len(lines),
                quadrant=quadrant,
                metadata={
                    "language": file_path.suffix,
                    "indexed_at": datetime.now().isoformat()
                }
            ))
            
        return chunks
        
    def _is_chunk_boundary(self, line: str, lines: List[str], index: int) -> bool:
        """Determine if this is a good place to split chunks"""
        # Simple heuristics for now
        if not line.strip():  # Empty line
            return True
        if line.strip().startswith(('def ', 'class ', 'function ', 'const ', 'export ')):
            return True
        if index < len(lines) - 1 and lines[index + 1].strip().startswith('#'):  # Comment
            return True
        return False
        
    async def _generate_embedding(self, text: str) -> np.ndarray:
        """Generate embedding for text"""
        # For demo, using random embeddings
        # In production, use actual embedding model like Roo does
        # Options: OpenAI, Ollama with nomic-embed-text, etc.
        
        # Simple hash-based pseudo-embedding for consistency
        text_hash = hashlib.sha256(text.encode()).digest()
        embedding = np.frombuffer(text_hash * 24, dtype=np.float32)[:self.vector_dim]
        embedding = embedding / np.linalg.norm(embedding)  # Normalize
        
        return embedding
        
    async def _store_chunks(self, chunks: List[CodeChunk]) -> List[str]:
        """Store chunks in vector database"""
        if self.backend == "qdrant":
            return await self._store_qdrant(chunks)
        elif self.backend == "chroma":
            return await self._store_chroma(chunks)
        else:
            return await self._store_numpy(chunks)
            
    async def _store_qdrant(self, chunks: List[CodeChunk]) -> List[str]:
        """Store in Qdrant"""
        points = []
        
        for chunk in chunks:
            points.append(PointStruct(
                id=chunk.id,
                vector=chunk.embedding.tolist(),
                payload={
                    "content": chunk.content,
                    "file_path": chunk.file_path,
                    "start_line": chunk.start_line,
                    "end_line": chunk.end_line,
                    "quadrant": chunk.quadrant,
                    **chunk.metadata
                }
            ))
            
        self.client.upsert(
            collection_name=self.collection_name,
            points=points
        )
        
        return [chunk.id for chunk in chunks]
        
    async def search(
        self, 
        query: str, 
        quadrants: Optional[List[str]] = None,
        limit: int = 10,
        threshold: float = 0.3
    ) -> List[SearchResult]:
        """Search for similar code chunks"""
        # Generate query embedding
        query_embedding = await self._generate_embedding(query)
        
        if self.backend == "qdrant":
            return await self._search_qdrant(query_embedding, quadrants, limit, threshold)
        elif self.backend == "chroma":
            return await self._search_chroma(query_embedding, quadrants, limit, threshold)
        else:
            return await self._search_numpy(query_embedding, quadrants, limit, threshold)
            
    async def _search_qdrant(
        self, 
        query_embedding: np.ndarray, 
        quadrants: Optional[List[str]],
        limit: int,
        threshold: float
    ) -> List[SearchResult]:
        """Search in Qdrant"""
        # Build filter for quadrants
        filter_conditions = None
        if quadrants:
            filter_conditions = Filter(
                should=[
                    FieldCondition(
                        key="quadrant",
                        match=MatchValue(value=q)
                    )
                    for q in quadrants
                ]
            )
            
        results = self.client.search(
            collection_name=self.collection_name,
            query_vector=query_embedding.tolist(),
            limit=limit,
            query_filter=filter_conditions,
            score_threshold=threshold
        )
        
        search_results = []
        for result in results:
            chunk = CodeChunk(
                id=result.id,
                content=result.payload["content"],
                file_path=result.payload["file_path"],
                start_line=result.payload["start_line"],
                end_line=result.payload["end_line"],
                quadrant=result.payload["quadrant"],
                metadata={k: v for k, v in result.payload.items() 
                         if k not in ["content", "file_path", "start_line", "end_line", "quadrant"]}
            )
            
            search_results.append(SearchResult(chunk=chunk, score=result.score))
            
        return search_results
        
    async def store_interaction(
        self, 
        agent_id: str, 
        role: str, 
        message: str, 
        response: Optional[str] = None
    ):
        """Store agent interaction in vector store for future reference"""
        # Create a special chunk for the interaction
        interaction_text = f"Agent: {role}\nMessage: {message}"
        if response:
            interaction_text += f"\nResponse: {response}"
            
        chunk = CodeChunk(
            id=f"interaction_{agent_id}_{int(time.time())}",
            content=interaction_text,
            file_path=f"interactions/{agent_id}",
            start_line=0,
            end_line=0,
            quadrant="shared",
            metadata={
                "type": "interaction",
                "agent_id": agent_id,
                "role": role,
                "timestamp": datetime.now().isoformat()
            }
        )
        
        chunk.embedding = await self._generate_embedding(chunk.content)
        await self._store_chunks([chunk])
        
    async def get_agent_context(self, agent_id: str, role: str) -> str:
        """Get relevant context for an agent based on their role and history"""
        # Search for relevant past interactions
        results = await self.search(
            query=f"{role} agent tasks and context",
            quadrants=["shared"],
            limit=5
        )
        
        context_parts = []
        for result in results:
            if result.chunk.metadata.get("agent_id") == agent_id:
                context_parts.append(result.chunk.content)
                
        return "\n\n".join(context_parts) if context_parts else "No previous context found."