#!/usr/bin/env python3
"""
Intelligent Task Seeder for Claude Collective
Reads from CLAUDE.md, AGENTS.md, and automation memory to determine what needs to be done
"""

import json
import logging
import sqlite3
from pathlib import Path
from typing import List, Dict, Tuple, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class PendingTask:
    """Represents a task that needs to be done"""
    type: str
    context: str
    priority: str
    source: str
    role: str = "DEVELOPER_MIDLEVEL"

class IntelligentTaskSeeder:
    """Seeds tasks based on documentation and memory"""
    
    def __init__(self, cc_root: Path):
        self.cc_root = cc_root
        self.dev_automation_path = cc_root / "_dev-automation-beta"
        self.memory_db = self.dev_automation_path / "memory" / "automation.db"
        
    def gather_pending_tasks(self) -> List[PendingTask]:
        """Gather all pending tasks from various sources"""
        tasks = []
        
        # 1. Read from CLAUDE.md priority tasks
        tasks.extend(self._extract_claude_md_tasks())
        
        # 2. Read from automation memory database
        tasks.extend(self._extract_memory_tasks())
        
        # 3. Read from workflow state
        tasks.extend(self._extract_workflow_state_tasks())
        
        # 4. Read from TODO files
        tasks.extend(self._extract_todo_files())
        
        # 5. Check for uncommitted changes that need handling
        tasks.extend(self._check_git_status())
        
        return tasks
        
    def _extract_claude_md_tasks(self) -> List[PendingTask]:
        """Extract priority tasks from CLAUDE.md"""
        tasks = []
        
        try:
            claude_md = self.cc_root / "CLAUDE.md"
            if claude_md.exists():
                content = claude_md.read_text()
                
                # Extract priority tasks line
                for line in content.split('\n'):
                    if line.startswith("**Priority Tasks**:"):
                        task_list = line.split(":", 1)[1].strip()
                        # Split by comma and create tasks
                        for task_desc in task_list.split(","):
                            task_desc = task_desc.strip()
                            if task_desc:
                                # Determine task type based on keywords
                                task_type = self._determine_task_type(task_desc)
                                tasks.append(PendingTask(
                                    type=task_type,
                                    context=task_desc,
                                    priority="high",
                                    source="CLAUDE.md",
                                    role=self._determine_role(task_desc)
                                ))
                        break
                        
                # Also look for active features that need work
                in_features = False
                for line in content.split('\n'):
                    if "Current Active Features" in line:
                        in_features = True
                    elif in_features and "Next Steps:" in line:
                        next_steps = line.split(":", 1)[1].strip()
                        feature_name = None
                        # Look back for feature name
                        for prev_line in content.split('\n'):
                            if "**" in prev_line and "(active)" in prev_line:
                                feature_name = prev_line.split("**")[1].strip()
                                break
                        if feature_name and next_steps:
                            task_type = self._determine_task_type(next_steps)
                            tasks.append(PendingTask(
                                type=task_type,
                                context=f"{feature_name}: {next_steps}",
                                priority="medium",
                                source="CLAUDE.md features",
                                role=self._determine_role(next_steps)
                            ))
                            
        except Exception as e:
            logger.error(f"Error reading CLAUDE.md: {e}")
            
        return tasks
        
    def _extract_memory_tasks(self) -> List[PendingTask]:
        """Extract pending tasks from automation memory database"""
        tasks = []
        
        try:
            if self.memory_db.exists():
                conn = sqlite3.connect(self.memory_db)
                cursor = conn.cursor()
                
                # Try to get pending workflow tasks
                try:
                    cursor.execute("""
                        SELECT task_name, status, priority, context
                        FROM workflow_state 
                        WHERE status IN ('pending', 'in_progress')
                        ORDER BY priority DESC
                        LIMIT 10
                    """)
                    
                    for row in cursor.fetchall():
                        task_name, status, priority, context = row
                        task_type = self._determine_task_type(task_name)
                        tasks.append(PendingTask(
                            type=task_type,
                            context=context or task_name,
                            priority=priority or "medium",
                            source="automation memory",
                            role=self._determine_role(task_name)
                        ))
                except:
                    pass
                    
                # Try to get recent failures that need retry
                try:
                    cursor.execute("""
                        SELECT template_name, context, error_message
                        FROM template_executions
                        WHERE status = 'failed'
                        AND timestamp > datetime('now', '-1 day')
                        LIMIT 5
                    """)
                    
                    for row in cursor.fetchall():
                        template, context, error = row
                        tasks.append(PendingTask(
                            type="fix_bug",
                            context=f"Retry failed {template}: {context} (Error: {error[:50]}...)",
                            priority="high",
                            source="failed executions",
                            role="DEVELOPER_SENIOR"
                        ))
                except:
                    pass
                    
                conn.close()
                
        except Exception as e:
            logger.error(f"Error reading automation memory: {e}")
            
        return tasks
        
    def _extract_workflow_state_tasks(self) -> List[PendingTask]:
        """Extract tasks from workflow state files"""
        tasks = []
        
        try:
            # Check for state.json files
            state_files = list(self.dev_automation_path.rglob("state.json"))
            
            for state_file in state_files:
                try:
                    with open(state_file) as f:
                        state = json.load(f)
                        
                    # Look for active tasks
                    if "active_tasks" in state:
                        for task in state["active_tasks"]:
                            if isinstance(task, str):
                                task_type = self._determine_task_type(task)
                                tasks.append(PendingTask(
                                    type=task_type,
                                    context=task,
                                    priority="high",
                                    source=f"workflow state: {state_file.parent.name}",
                                    role=self._determine_role(task)
                                ))
                                
                except Exception as e:
                    logger.error(f"Error reading state file {state_file}: {e}")
                    
        except Exception as e:
            logger.error(f"Error scanning workflow states: {e}")
            
        return tasks
        
    def _extract_todo_files(self) -> List[PendingTask]:
        """Extract tasks from TODO files"""
        tasks = []
        
        try:
            # Common TODO file patterns
            todo_patterns = ["TODO.md", "TODO.txt", "TASKS.md", "ROADMAP.md"]
            
            for pattern in todo_patterns:
                todo_files = list(self.cc_root.rglob(pattern))
                
                for todo_file in todo_files:
                    try:
                        content = todo_file.read_text()
                        
                        # Look for task patterns
                        for line in content.split('\n'):
                            if any(marker in line for marker in ["- [ ]", "TODO:", "TASK:", "FIXME:"]):
                                task_desc = line.strip()
                                # Clean up markers
                                for marker in ["- [ ]", "TODO:", "TASK:", "FIXME:"]:
                                    task_desc = task_desc.replace(marker, "").strip()
                                    
                                if task_desc:
                                    task_type = self._determine_task_type(task_desc)
                                    tasks.append(PendingTask(
                                        type=task_type,
                                        context=task_desc,
                                        priority="medium",
                                        source=f"TODO: {todo_file.name}",
                                        role=self._determine_role(task_desc)
                                    ))
                                    
                    except Exception as e:
                        logger.error(f"Error reading TODO file {todo_file}: {e}")
                        
        except Exception as e:
            logger.error(f"Error scanning TODO files: {e}")
            
        return tasks[:5]  # Limit to top 5 TODO items
        
    def _check_git_status(self) -> List[PendingTask]:
        """Check for uncommitted changes that might need handling"""
        tasks = []
        
        try:
            import subprocess
            
            # Check for uncommitted changes
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                cwd=self.cc_root,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0 and result.stdout.strip():
                modified_files = []
                for line in result.stdout.strip().split('\n'):
                    if line.startswith(" M "):
                        modified_files.append(line[3:])
                        
                if modified_files:
                    tasks.append(PendingTask(
                        type="analyze",
                        context=f"Review and test uncommitted changes in: {', '.join(modified_files[:3])}{'...' if len(modified_files) > 3 else ''}",
                        priority="high",
                        source="git status",
                        role="QA"
                    ))
                    
        except Exception as e:
            logger.error(f"Error checking git status: {e}")
            
        return tasks
        
    def _determine_task_type(self, description: str) -> str:
        """Determine task type from description"""
        desc_lower = description.lower()
        
        if any(word in desc_lower for word in ["fix", "bug", "error", "issue"]):
            return "fix_bug"
        elif any(word in desc_lower for word in ["test", "verify", "check"]):
            return "test"
        elif any(word in desc_lower for word in ["implement", "create", "build", "add"]):
            return "implement"
        elif any(word in desc_lower for word in ["integrate", "connect", "merge"]):
            return "integrate"
        elif any(word in desc_lower for word in ["deploy", "release", "publish"]):
            return "deploy"
        elif any(word in desc_lower for word in ["analyze", "review", "assess"]):
            return "analyze"
        else:
            return "analyze"  # Default to analysis
            
    def _determine_role(self, description: str) -> str:
        """Determine best role for a task"""
        desc_lower = description.lower()
        
        if any(word in desc_lower for word in ["architect", "design", "plan", "structure"]):
            return "ARCHITECT"
        elif any(word in desc_lower for word in ["test", "qa", "verify", "validation"]):
            return "QA"
        elif any(word in desc_lower for word in ["research", "investigate", "explore"]):
            return "RESEARCHER"
        elif any(word in desc_lower for word in ["complex", "critical", "advanced", "senior"]):
            return "DEVELOPER_SENIOR"
        elif any(word in desc_lower for word in ["simple", "basic", "minor"]):
            return "DEVELOPER_JUNIOR"
        else:
            return "DEVELOPER_MIDLEVEL"
            
    def get_prioritized_tasks(self, limit: int = 10) -> List[PendingTask]:
        """Get prioritized list of tasks to work on"""
        all_tasks = self.gather_pending_tasks()
        
        # Remove duplicates based on context similarity
        unique_tasks = []
        seen_contexts = set()
        
        for task in all_tasks:
            # Simple deduplication - could be enhanced with fuzzy matching
            context_key = task.context.lower()[:50]
            if context_key not in seen_contexts:
                seen_contexts.add(context_key)
                unique_tasks.append(task)
                
        # Sort by priority
        priority_order = {"high": 0, "medium": 1, "low": 2}
        unique_tasks.sort(key=lambda t: priority_order.get(t.priority, 2))
        
        return unique_tasks[:limit]

# Example usage
if __name__ == "__main__":
    seeder = IntelligentTaskSeeder(Path("/home/<USER>/github/cc-v1"))
    tasks = seeder.get_prioritized_tasks(5)
    
    print("📋 Discovered Pending Tasks:\n")
    for i, task in enumerate(tasks, 1):
        print(f"{i}. [{task.priority}] {task.type}: {task.context}")
        print(f"   Source: {task.source} | Role: {task.role}")
        print()