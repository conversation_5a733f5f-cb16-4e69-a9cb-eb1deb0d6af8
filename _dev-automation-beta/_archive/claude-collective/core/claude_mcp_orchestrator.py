#!/usr/bin/env python3
"""
Claude MCP Orchestrator - Uses Model Context Protocol to orchestrate Claude instances
Works with <PERSON> desktop app and VS Code extensions that support MCP
"""

import asyncio
import json
import time
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Set
import websocket
import threading

from .logger_config import setup_logger
from .quadrant_memory import QuadrantMemorySystem, Quadrant

logger, _, _ = setup_logger('claude_mcp')

class MCPAgentRole(Enum):
    """Agent roles in MCP orchestration"""
    COORDINATOR = "coordinator"
    ANALYST = "analyst"
    BUILDER = "builder"
    VALIDATOR = "validator"

@dataclass
class MCPTask:
    """Task for MCP agents"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    description: str = ""
    type: str = "general"
    assigned_to: Optional[str] = None
    status: str = "pending"
    result: Optional[Any] = None
    created_at: float = field(default_factory=time.time)
    completed_at: Optional[float] = None

@dataclass
class MCPAgent:
    """MCP-connected Claude agent"""
    id: str
    role: MCPAgentRole
    connection_id: Optional[str] = None
    last_ping: float = field(default_factory=time.time)
    current_task: Optional[str] = None
    capabilities: List[str] = field(default_factory=list)
    
    def is_alive(self) -> bool:
        return time.time() - self.last_ping < 60  # 1 minute timeout

class ClaudeMCPOrchestrator:
    """
    Orchestrates Claude instances through MCP (Model Context Protocol)
    
    This works with:
    1. Claude Desktop App with MCP servers
    2. VS Code with Claude extension and MCP support
    3. Any MCP-compatible Claude interface
    """
    
    def __init__(self, base_path: Path):
        self.base_path = base_path
        self.agents: Dict[str, MCPAgent] = {}
        self.tasks: Dict[str, MCPTask] = {}
        self.quadrant_memory = QuadrantMemorySystem(base_path)
        
        # MCP server configuration
        self.mcp_servers = {
            "filesystem": {
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-filesystem", str(base_path)]
            },
            "git": {
                "command": "npx", 
                "args": ["-y", "@modelcontextprotocol/server-git", "--repository", str(base_path)]
            },
            "postgres": {
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://localhost/ccv1"]
            }
        }
        
        # Agent assignment strategy
        self.role_assignment = {
            "analyze": MCPAgentRole.ANALYST,
            "build": MCPAgentRole.BUILDER,
            "implement": MCPAgentRole.BUILDER,
            "validate": MCPAgentRole.VALIDATOR,
            "test": MCPAgentRole.VALIDATOR,
            "coordinate": MCPAgentRole.COORDINATOR
        }
        
    def create_mcp_config(self) -> Dict[str, Any]:
        """Create MCP configuration for Claude instances"""
        config = {
            "mcpServers": self.mcp_servers,
            "orchestration": {
                "enabled": True,
                "orchestratorId": str(uuid.uuid4()),
                "baseUrl": f"ws://localhost:8765",  # WebSocket for coordination
                "sharedMemory": {
                    "type": "quadrant",
                    "path": str(self.quadrant_memory.memory_path)
                }
            }
        }
        
        # Save config for Claude instances to use
        config_path = self.base_path / "claude-collective" / "mcp-config.json"
        config_path.parent.mkdir(parents=True, exist_ok=True)
        config_path.write_text(json.dumps(config, indent=2))
        
        logger.info(f"📝 MCP config saved to: {config_path}")
        return config
        
    async def start_coordination_server(self):
        """Start WebSocket server for agent coordination"""
        import websockets
        
        async def handle_agent(websocket, path):
            """Handle agent connection"""
            agent_id = None
            
            try:
                # Wait for agent registration
                message = await websocket.recv()
                data = json.loads(message)
                
                if data["type"] == "register":
                    agent_id = data["agentId"]
                    role = MCPAgentRole(data["role"])
                    
                    agent = MCPAgent(
                        id=agent_id,
                        role=role,
                        connection_id=str(id(websocket)),
                        capabilities=data.get("capabilities", [])
                    )
                    
                    self.agents[agent_id] = agent
                    logger.info(f"✅ Agent registered: {agent_id} ({role.value})")
                    
                    # Send acknowledgment
                    await websocket.send(json.dumps({
                        "type": "registered",
                        "agentId": agent_id,
                        "message": f"Welcome {role.value} agent"
                    }))
                    
                # Handle agent messages
                async for message in websocket:
                    await self._handle_agent_message(agent_id, json.loads(message))
                    
            except websockets.exceptions.ConnectionClosed:
                logger.info(f"Agent disconnected: {agent_id}")
            finally:
                if agent_id and agent_id in self.agents:
                    del self.agents[agent_id]
                    
        # Start server
        await websockets.serve(handle_agent, "localhost", 8765)
        logger.info("🌐 Coordination server started on ws://localhost:8765")
        
    async def _handle_agent_message(self, agent_id: str, message: Dict):
        """Handle message from agent"""
        msg_type = message.get("type")
        
        if msg_type == "ping":
            if agent_id in self.agents:
                self.agents[agent_id].last_ping = time.time()
                
        elif msg_type == "task_update":
            task_id = message.get("taskId")
            if task_id in self.tasks:
                task = self.tasks[task_id]
                task.status = message.get("status", task.status)
                if message.get("result"):
                    task.result = message["result"]
                if task.status == "completed":
                    task.completed_at = time.time()
                    
                # Store in memory
                quadrant = self._determine_quadrant(task.type)
                self.quadrant_memory.store_memory(
                    content=f"Task {task_id} {task.status}: {task.description}",
                    quadrant=quadrant,
                    metadata={
                        "task": task.__dict__,
                        "agent": agent_id
                    }
                )
                
        elif msg_type == "request_task":
            # Agent is requesting work
            task = self._find_task_for_agent(agent_id)
            if task:
                await self._assign_task_to_agent(task, agent_id)
                
    def _determine_quadrant(self, task_type: str) -> Quadrant:
        """Determine quadrant based on task type"""
        quadrant_map = {
            "frontend": Quadrant.FRONTEND,
            "backend": Quadrant.BACKEND,
            "infrastructure": Quadrant.INFRASTRUCTURE,
            "documentation": Quadrant.DOCUMENTATION,
            "testing": Quadrant.SHARED
        }
        return quadrant_map.get(task_type, Quadrant.SHARED)
        
    def _find_task_for_agent(self, agent_id: str) -> Optional[MCPTask]:
        """Find suitable task for agent"""
        if agent_id not in self.agents:
            return None
            
        agent = self.agents[agent_id]
        
        # Find pending tasks suitable for agent's role
        for task in self.tasks.values():
            if task.status == "pending" and not task.assigned_to:
                # Match task type to agent role
                for keyword, role in self.role_assignment.items():
                    if keyword in task.type.lower() and agent.role == role:
                        return task
                        
        return None
        
    async def _assign_task_to_agent(self, task: MCPTask, agent_id: str):
        """Assign task to agent"""
        task.assigned_to = agent_id
        task.status = "assigned"
        
        if agent_id in self.agents:
            self.agents[agent_id].current_task = task.id
            
        # Notify agent (would send through WebSocket in real implementation)
        logger.info(f"📋 Task {task.id} assigned to {agent_id}")
        
    def create_task_prompt(self, task: MCPTask) -> str:
        """Create a prompt for Claude to work on a task"""
        # Get relevant memories
        quadrant = self._determine_quadrant(task.type)
        memories = self.quadrant_memory.search_memories(
            query=task.description,
            quadrants=[quadrant],
            limit=5
        )
        
        memory_context = "\n".join([
            f"- {mem.content}" for mem in memories
        ]) if memories else "No relevant context found."
        
        prompt = f"""## Task Assignment

**Task ID**: {task.id}
**Type**: {task.type}
**Description**: {task.description}

### Relevant Context from Project Memory:
{memory_context}

### Instructions:
1. Analyze the task requirements
2. Use MCP tools to explore the codebase
3. Implement or complete the requested work
4. Report progress and results

You have access to these MCP servers:
- filesystem: Read/write files in the project
- git: Version control operations
- postgres: Database queries if needed

Please work on this task and report your progress."""
        
        return prompt
        
    def generate_setup_instructions(self) -> str:
        """Generate instructions for setting up Claude instances"""
        config = self.create_mcp_config()
        
        instructions = f"""# Claude MCP Orchestration Setup

## 1. Configure Claude Desktop App

Add this to your Claude Desktop configuration:

```json
{json.dumps(config, indent=2)}
```

Location:
- macOS: ~/Library/Application Support/Claude/claude_desktop_config.json
- Windows: %APPDATA%/Claude/claude_desktop_config.json
- Linux: ~/.config/Claude/claude_desktop_config.json

## 2. Start Coordination Server

Run in terminal:
```bash
cd {self.base_path}/claude-collective
python3 -c "
from core.claude_mcp_orchestrator import ClaudeMCPOrchestrator
import asyncio
orchestrator = ClaudeMCPOrchestrator(Path('.'))
asyncio.run(orchestrator.start_coordination_server())
"
```

## 3. Open Claude Instances

Open multiple Claude Desktop windows or VS Code instances with:
1. One COORDINATOR agent
2. One or more ANALYST agents
3. One or more BUILDER agents
4. One VALIDATOR agent

## 4. Initialize Each Agent

In each Claude instance, start with:

"I am a [ROLE] agent in the Claude Collective orchestration system. 
My agent ID is [generate a unique ID]. 
I will connect to the orchestration server and await tasks."

## 5. Agents Will Self-Organize

The agents will:
- Connect to the coordination server
- Receive task assignments based on their role
- Share context through the quadrant memory system
- Collaborate to complete complex tasks

## Example Task Submission

To submit a task to the collective:
```python
task = MCPTask(
    description="Implement user authentication with JWT",
    type="build_backend"
)
orchestrator.tasks[task.id] = task
```

The COORDINATOR will analyze and route to appropriate agents."""
        
        return instructions
        
    async def monitor_collective(self):
        """Monitor the collective's activity"""
        while True:
            try:
                active_agents = [a for a in self.agents.values() if a.is_alive()]
                pending_tasks = [t for t in self.tasks.values() if t.status == "pending"]
                active_tasks = [t for t in self.tasks.values() if t.status in ["assigned", "in_progress"]]
                
                logger.info(f"""
📊 Collective Status:
  Active Agents: {len(active_agents)}
  Pending Tasks: {len(pending_tasks)}
  Active Tasks: {len(active_tasks)}
  Quadrants with Activity: {len([q for q in Quadrant if self.quadrant_memory.memories[q]])}
""")
                
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                logger.error(f"Monitor error: {e}")
                await asyncio.sleep(30)