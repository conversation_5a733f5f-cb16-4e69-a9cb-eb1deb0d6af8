#!/usr/bin/env python3
"""
Enhanced logging configuration for Claude Collective
Provides detailed logging with file rotation and real-time inspection
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from datetime import datetime
import json

def setup_logger(name: str, log_dir: Path = None, level=logging.INFO):
    """Setup enhanced logger with both file and console output"""
    
    if log_dir is None:
        log_dir = Path("/home/<USER>/github/cc-v1/_dev-automation-beta/claude-collective/logs")
    
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Remove existing handlers
    logger.handlers = []
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # File handler for detailed logs
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_path = log_dir / f"{name}_{timestamp}.log"
    file_handler = logging.handlers.RotatingFileHandler(
        file_path,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    
    # Console handler for important messages
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    
    # Add handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    # Also create a JSON log for structured analysis
    json_path = log_dir / f"{name}_{timestamp}.json"
    json_handler = JsonFileHandler(json_path)
    json_handler.setLevel(logging.DEBUG)
    logger.addHandler(json_handler)
    
    return logger, file_path, json_path

class JsonFileHandler(logging.Handler):
    """Custom handler that writes JSON formatted logs"""
    
    def __init__(self, filename):
        super().__init__()
        self.filename = filename
        self.file = open(filename, 'a')
        
    def emit(self, record):
        """Write log record as JSON"""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'function': record.funcName,
            'line': record.lineno,
            'message': record.getMessage(),
            'module': record.module
        }
        
        # Add extra fields if present
        if hasattr(record, 'task_id'):
            log_entry['task_id'] = record.task_id
        if hasattr(record, 'agent_id'):
            log_entry['agent_id'] = record.agent_id
        if hasattr(record, 'swarm_id'):
            log_entry['swarm_id'] = record.swarm_id
            
        self.file.write(json.dumps(log_entry) + '\n')
        self.file.flush()
        
    def close(self):
        """Close the file"""
        self.file.close()
        super().close()

# Convenience function for task-specific logging
def log_task_event(logger, event_type: str, task_id: str, **kwargs):
    """Log a task-related event with structured data"""
    extra = {'task_id': task_id}
    extra.update(kwargs)
    
    if event_type == "start":
        logger.info(f"Task {task_id} started", extra=extra)
    elif event_type == "complete":
        logger.info(f"Task {task_id} completed", extra=extra)
    elif event_type == "error":
        logger.error(f"Task {task_id} failed", extra=extra)
    else:
        logger.info(f"Task {task_id} - {event_type}", extra=extra)

# Create a log viewer utility
def create_log_viewer(log_dir: Path):
    """Create a simple log viewer script"""
    viewer_script = log_dir / "view_logs.py"
    viewer_content = '''#!/usr/bin/env python3
"""Simple log viewer for Claude Collective logs"""

import sys
import json
from pathlib import Path
import argparse
from datetime import datetime

def view_logs(log_file, filter_level=None, filter_task=None, last_n=None):
    """View and filter log entries"""
    
    if log_file.suffix == '.json':
        # JSON logs
        with open(log_file, 'r') as f:
            lines = f.readlines()
            if last_n:
                lines = lines[-last_n:]
                
            for line in lines:
                try:
                    entry = json.loads(line)
                    
                    # Apply filters
                    if filter_level and entry['level'] != filter_level:
                        continue
                    if filter_task and entry.get('task_id') != filter_task:
                        continue
                        
                    # Format output
                    print(f"{entry['timestamp']} [{entry['level']}] {entry['message']}")
                    if 'task_id' in entry:
                        print(f"  Task: {entry['task_id']}")
                    if 'agent_id' in entry:
                        print(f"  Agent: {entry['agent_id']}")
                        
                except json.JSONDecodeError:
                    pass
    else:
        # Text logs
        with open(log_file, 'r') as f:
            lines = f.readlines()
            if last_n:
                lines = lines[-last_n:]
            for line in lines:
                print(line.strip())

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="View Claude Collective logs")
    parser.add_argument("log_file", help="Path to log file")
    parser.add_argument("--level", help="Filter by log level")
    parser.add_argument("--task", help="Filter by task ID")
    parser.add_argument("--last", type=int, help="Show last N entries")
    
    args = parser.parse_args()
    view_logs(Path(args.log_file), args.level, args.task, args.last)
'''
    
    viewer_script.write_text(viewer_content)
    viewer_script.chmod(0o755)
    
    # Create a summary script too
    summary_script = log_dir / "summarize_logs.py"
    summary_content = '''#!/usr/bin/env python3
"""Summarize Claude Collective log activity"""

import json
from pathlib import Path
from collections import Counter, defaultdict
from datetime import datetime

def summarize_logs(log_dir):
    """Generate summary statistics from logs"""
    
    stats = {
        'total_tasks': 0,
        'completed_tasks': 0,
        'failed_tasks': 0,
        'tasks_by_type': Counter(),
        'tasks_by_agent': Counter(),
        'errors_by_type': Counter(),
        'swarm_executions': 0,
        'avg_completion_time': []
    }
    
    # Process all JSON logs
    for json_log in log_dir.glob("*.json"):
        with open(json_log, 'r') as f:
            for line in f:
                try:
                    entry = json.loads(line)
                    
                    if 'task_id' in entry:
                        if 'started' in entry['message']:
                            stats['total_tasks'] += 1
                        elif 'completed' in entry['message']:
                            stats['completed_tasks'] += 1
                        elif 'failed' in entry['message']:
                            stats['failed_tasks'] += 1
                            
                    if 'agent_id' in entry:
                        stats['tasks_by_agent'][entry['agent_id']] += 1
                        
                    if 'swarm_id' in entry:
                        stats['swarm_executions'] += 1
                        
                    if entry['level'] == 'ERROR':
                        stats['errors_by_type'][entry.get('function', 'unknown')] += 1
                        
                except json.JSONDecodeError:
                    pass
    
    # Print summary
    print("=== Claude Collective Activity Summary ===")
    print(f"Total Tasks: {stats['total_tasks']}")
    print(f"Completed: {stats['completed_tasks']}")
    print(f"Failed: {stats['failed_tasks']}")
    print(f"Success Rate: {stats['completed_tasks']/max(stats['total_tasks'],1)*100:.1f}%")
    print(f"\\nSwarm Executions: {stats['swarm_executions']}")
    
    print("\\nTop Agents by Activity:")
    for agent, count in stats['tasks_by_agent'].most_common(5):
        print(f"  {agent}: {count} tasks")
        
    if stats['errors_by_type']:
        print("\\nErrors by Function:")
        for func, count in stats['errors_by_type'].most_common():
            print(f"  {func}: {count} errors")

if __name__ == "__main__":
    import sys
    log_dir = Path(sys.argv[1] if len(sys.argv) > 1 else ".")
    summarize_logs(log_dir)
'''
    
    summary_script.write_text(summary_content)
    summary_script.chmod(0o755)
    
    print(f"Log viewer created: {viewer_script}")
    print(f"Log summarizer created: {summary_script}")