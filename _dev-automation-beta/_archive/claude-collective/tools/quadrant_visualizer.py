#!/usr/bin/env python3
"""
Quadrant Visualizer - Shows memory distribution and relationships
"""

import json
import time
import sys
from pathlib import Path
from typing import Dict, List

sys.path.insert(0, str(Path(__file__).parent.parent))
from core.quadrant_memory import QuadrantMemorySystem, Quadrant

def generate_quadrant_report(base_path: Path) -> Dict:
    """Generate a comprehensive quadrant analysis report"""
    
    # Initialize memory system
    memory_system = QuadrantMemorySystem(base_path)
    
    report = {
        "timestamp": time.time(),
        "quadrants": {},
        "relationships": {},
        "recommendations": []
    }
    
    # Analyze each quadrant
    for quadrant in Quadrant:
        memories = memory_system.memories[quadrant]
        context = memory_system.contexts[quadrant]
        
        # Calculate metrics
        total_memories = len(memories)
        recent_memories = sum(1 for m in memories.values() 
                            if time.time() - m.timestamp < 3600)  # Last hour
        avg_access = (sum(m.access_count for m in memories.values()) / total_memories 
                     if total_memories > 0 else 0)
        
        report["quadrants"][quadrant.value] = {
            "total_memories": total_memories,
            "recent_memories": recent_memories,
            "active_files": list(context.active_files)[:10],  # Top 10
            "avg_access_count": round(avg_access, 2),
            "key_patterns": dict(sorted(
                context.key_patterns.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5])  # Top 5 patterns
        }
        
        # Find cross-quadrant relationships
        for memory in memories.values():
            for related_id in memory.related_memories:
                # Check which quadrant the related memory is in
                for other_q in Quadrant:
                    if other_q != quadrant and related_id in memory_system.memories[other_q]:
                        rel_key = f"{quadrant.value}->{other_q.value}"
                        report["relationships"][rel_key] = \
                            report["relationships"].get(rel_key, 0) + 1
    
    # Generate recommendations based on patterns
    insights = memory_system.get_cross_quadrant_insights()
    
    # High activity recommendations
    for q_name, activity in insights["quadrant_activity"].items():
        if activity["recent_memories"] > 20:
            report["recommendations"].append({
                "type": "high_activity",
                "quadrant": q_name,
                "action": f"Consider deploying specialized agent for {q_name}",
                "reason": f"{activity['recent_memories']} recent memories indicate high activity"
            })
    
    # Cross-quadrant coupling recommendations
    for rel, count in report["relationships"].items():
        if count > 10:
            q1, q2 = rel.split("->")
            report["recommendations"].append({
                "type": "high_coupling",
                "quadrants": [q1, q2],
                "action": f"Review integration between {q1} and {q2}",
                "reason": f"{count} cross-references indicate tight coupling"
            })
    
    return report

def print_quadrant_visualization(report: Dict):
    """Print a visual representation of quadrant relationships"""
    
    print("\n" + "="*60)
    print("🗺️  QUADRANT MEMORY VISUALIZATION")
    print("="*60)
    
    # Quadrant boxes
    print("\n📦 QUADRANT OVERVIEW:\n")
    
    for q_name, data in report["quadrants"].items():
        print(f"┌─ {q_name.upper()} {'─'*(20-len(q_name))}")
        print(f"│  Memories: {data['total_memories']} (recent: {data['recent_memories']})")
        print(f"│  Avg Access: {data['avg_access_count']}")
        print(f"│  Active Files: {len(data['active_files'])}")
        if data['key_patterns']:
            print(f"│  Top Pattern: {list(data['key_patterns'].keys())[0]}")
        print(f"└{'─'*25}")
        print()
    
    # Relationships
    if report["relationships"]:
        print("\n🔗 CROSS-QUADRANT FLOWS:\n")
        for rel, count in sorted(report["relationships"].items(), 
                                key=lambda x: x[1], reverse=True):
            q1, q2 = rel.split("->")
            arrow = "━" * min(count // 2, 10) + ">"
            print(f"  {q1:12} {arrow} {q2:12} ({count} refs)")
    
    # Recommendations
    if report["recommendations"]:
        print("\n💡 RECOMMENDATIONS:\n")
        for i, rec in enumerate(report["recommendations"], 1):
            print(f"{i}. [{rec['type'].upper()}] {rec['action']}")
            print(f"   Reason: {rec['reason']}")
            print()
    
    print("="*60)

def generate_mermaid_diagram(report: Dict) -> str:
    """Generate a Mermaid diagram for quadrant relationships"""
    
    lines = ["graph TD"]
    
    # Add quadrant nodes
    for q_name, data in report["quadrants"].items():
        size = "large" if data["total_memories"] > 50 else "medium"
        lines.append(f"    {q_name}[{q_name.upper()}<br/>Memories: {data['total_memories']}]")
    
    # Add relationships
    for rel, count in report["relationships"].items():
        q1, q2 = rel.split("->")
        thickness = min(count // 5, 5)  # Line thickness based on count
        lines.append(f"    {q1} ---|{count} refs| {q2}")
    
    # Style active quadrants
    for q_name, data in report["quadrants"].items():
        if data["recent_memories"] > 10:
            lines.append(f"    style {q_name} fill:#f9f,stroke:#333,stroke-width:4px")
    
    return "\n".join(lines)

if __name__ == "__main__":
    import time
    
    base_path = Path(__file__).parent.parent.parent
    report = generate_quadrant_report(base_path)
    
    # Print visualization
    print_quadrant_visualization(report)
    
    # Save report
    report_file = base_path / "claude-collective" / "reports" / f"quadrant_report_{int(time.time())}.json"
    report_file.parent.mkdir(parents=True, exist_ok=True)
    report_file.write_text(json.dumps(report, indent=2))
    
    # Generate Mermaid diagram
    mermaid = generate_mermaid_diagram(report)
    diagram_file = report_file.with_suffix(".mmd")
    diagram_file.write_text(mermaid)
    
    print(f"\n📄 Report saved to: {report_file}")
    print(f"📊 Mermaid diagram saved to: {diagram_file}")