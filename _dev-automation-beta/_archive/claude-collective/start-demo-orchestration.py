#!/usr/bin/env python3
"""
Demo orchestration - simulates LangGraph orchestration without API calls
"""

import asyncio
import json
import time
from pathlib import Path
import sys

sys.path.insert(0, str(Path(__file__).parent))

from core.quadrant_memory import QuadrantMemorySystem, Quadrant
from core.logger_config import setup_logger

logger, _, _ = setup_logger('demo_orchestration')

class DemoOrchestration:
    """Simulates the LangGraph orchestration for demonstration"""
    
    def __init__(self, base_path: Path):
        self.base_path = base_path
        self.quadrant_memory = QuadrantMemorySystem(base_path)
        self.agents = {
            "orchestrator": {"status": "idle", "tasks_completed": 0},
            "nightwatch": {"status": "idle", "tasks_completed": 0},
            "researcher": {"status": "idle", "tasks_completed": 0},
            "architect": {"status": "idle", "tasks_completed": 0},
            "developer": {"status": "idle", "tasks_completed": 0},
            "qa": {"status": "idle", "tasks_completed": 0},
            "reviewer": {"status": "idle", "tasks_completed": 0}
        }
        self.task_queue = []
        self.completed_tasks = []
        
    async def simulate_agent_work(self, agent_name: str, task: dict):
        """Simulate an agent working on a task"""
        logger.info(f"🤖 {agent_name} starting task: {task['description'][:50]}...")
        self.agents[agent_name]["status"] = "working"
        
        # Simulate work time based on task type
        work_time = {"research": 2, "design": 3, "implement": 4, "test": 2, "review": 2}.get(task["type"], 2)
        await asyncio.sleep(work_time)
        
        # Store memory in appropriate quadrant
        quadrant = Quadrant(task.get("quadrant", "shared"))
        memory_id = self.quadrant_memory.store_memory(
            content=f"{agent_name} completed: {task['description']}",
            quadrant=quadrant,
            metadata={
                "agent": agent_name,
                "task_id": task["id"],
                "task_type": task["type"],
                "duration": work_time
            }
        )
        
        # Update quadrant context
        self.quadrant_memory.update_quadrant_context(
            quadrant=quadrant,
            change={
                "type": f"{task['type']}_completed",
                "agent": agent_name,
                "task": task["description"]
            }
        )
        
        self.agents[agent_name]["status"] = "idle"
        self.agents[agent_name]["tasks_completed"] += 1
        task["status"] = "completed"
        task["completed_by"] = agent_name
        self.completed_tasks.append(task)
        
        logger.info(f"✅ {agent_name} completed task: {task['id']}")
        
    async def nudge_system(self):
        """Simulate the nudge system checking for inactive agents"""
        while True:
            await asyncio.sleep(10)  # Check every 10 seconds
            
            inactive_agents = [name for name, info in self.agents.items() 
                             if info["status"] == "idle" and info != "nightwatch"]
            
            if inactive_agents and self.task_queue:
                logger.info(f"📢 Nudging {len(inactive_agents)} inactive agents")
                # Nightwatch activates
                self.agents["nightwatch"]["status"] = "monitoring"
                await asyncio.sleep(1)
                self.agents["nightwatch"]["status"] = "idle"
                
    async def orchestrate(self, initial_tasks: list):
        """Main orchestration loop"""
        self.task_queue = initial_tasks.copy()
        
        logger.info("🚀 Starting demo orchestration")
        logger.info(f"📋 {len(self.task_queue)} tasks to process")
        
        # Start nudge system
        nudge_task = asyncio.create_task(self.nudge_system())
        
        # Process tasks
        while self.task_queue or any(a["status"] == "working" for a in self.agents.values()):
            # Assign tasks to available agents
            for task in self.task_queue[:]:
                # Find suitable agent
                agent_map = {
                    "research": "researcher",
                    "design": "architect", 
                    "implement": "developer",
                    "test": "qa",
                    "review": "reviewer"
                }
                
                agent_name = agent_map.get(task["type"], "orchestrator")
                
                if self.agents[agent_name]["status"] == "idle":
                    self.task_queue.remove(task)
                    asyncio.create_task(self.simulate_agent_work(agent_name, task))
                    
            # Show status
            working_agents = [name for name, info in self.agents.items() 
                            if info["status"] == "working"]
            if working_agents:
                logger.info(f"⚙️  Working agents: {', '.join(working_agents)}")
                
            await asyncio.sleep(1)
            
        nudge_task.cancel()
        
        logger.info("✅ All tasks completed!")
        
        # Show results
        print("\n📊 ORCHESTRATION RESULTS:")
        print(f"Tasks completed: {len(self.completed_tasks)}")
        print("\nAgent Performance:")
        for name, info in self.agents.items():
            if info["tasks_completed"] > 0:
                print(f"  {name}: {info['tasks_completed']} tasks")
                
        # Show quadrant insights
        insights = self.quadrant_memory.get_cross_quadrant_insights()
        
        print("\n🗺️  QUADRANT ACTIVITY:")
        for quadrant, activity in insights["quadrant_activity"].items():
            if activity["total_memories"] > 0:
                print(f"  {quadrant}: {activity['total_memories']} memories")
                
        if insights["cross_references"]:
            print("\n🔗 CROSS-QUADRANT REFERENCES:")
            for ref, count in insights["cross_references"].items():
                print(f"  {ref}: {count} references")

async def main():
    """Run the demo orchestration"""
    base_path = Path(__file__).parent.parent
    orchestrator = DemoOrchestration(base_path)
    
    # Create sample tasks that span multiple quadrants
    tasks = [
        {
            "id": "analyze_ui",
            "type": "research",
            "quadrant": Quadrant.FRONTEND.value,
            "description": "Analyze operator UI components for WebSocket integration points",
            "priority": "high"
        },
        {
            "id": "design_ws_arch",
            "type": "design",
            "quadrant": Quadrant.BACKEND.value,
            "description": "Design WebSocket architecture for real-time operator updates",
            "priority": "high"
        },
        {
            "id": "impl_frontend_hooks",
            "type": "implement",
            "quadrant": Quadrant.FRONTEND.value,
            "description": "Implement React hooks for WebSocket connection management",
            "priority": "medium"
        },
        {
            "id": "impl_backend_handlers",
            "type": "implement",
            "quadrant": Quadrant.BACKEND.value,
            "description": "Implement Fastify WebSocket handlers for operator events",
            "priority": "medium"
        },
        {
            "id": "update_k8s_config",
            "type": "implement",
            "quadrant": Quadrant.INFRASTRUCTURE.value,
            "description": "Update Kubernetes ingress configuration for WebSocket support",
            "priority": "medium"
        },
        {
            "id": "test_integration",
            "type": "test",
            "quadrant": Quadrant.SHARED.value,
            "description": "Test end-to-end WebSocket communication across all quadrants",
            "priority": "high"
        },
        {
            "id": "review_implementation",
            "type": "review",
            "quadrant": Quadrant.SHARED.value,
            "description": "Review complete WebSocket implementation for best practices",
            "priority": "medium"
        },
        {
            "id": "document_ws_api",
            "type": "implement",
            "quadrant": Quadrant.DOCUMENTATION.value,
            "description": "Document WebSocket API endpoints and event types",
            "priority": "low"
        }
    ]
    
    await orchestrator.orchestrate(tasks)

if __name__ == "__main__":
    print("🎭 CLAUDE COLLECTIVE V1 - DEMO ORCHESTRATION")
    print("=" * 50)
    print("This demo simulates the LangGraph orchestration")
    print("without requiring API keys.")
    print("=" * 50)
    print()
    
    asyncio.run(main())