# Git Workflow Integration for Claude Collective

## Overview

The Claude Collective now includes automatic Git workflow management to ensure safe overnight automation without accidentally breaking the `develop` branch.

## How It Works

### 1. Automatic Branch Creation
When the system starts, it automatically:
- Creates a dated branch: `night_YYYY-MM-DD`
- Ensures it's based on latest `develop`
- All work happens in this isolated branch

### 2. Smart Commit Management
As agents complete tasks:
- Changes are automatically detected from task results
- Commits are created with structured messages
- Each commit includes task ID and description

### 3. PR Creation Strategy
The system creates PRs when:
- 5 meaningful changes accumulate (configurable)
- You manually request a PR
- The session ends

### 4. Safety Features
- **Never auto-merges** to develop
- Creates **draft PRs** for review
- Groups related changes sensibly
- Clear commit messages for easy review

## Branch Naming Convention

```
night_2025-08-04          # Main night session branch
night_2025-08-04_pr2      # Second PR if session continues
night_2025-08-04_pr3      # Third PR, etc.
```

## PR Structure

Each PR includes:
- Summary of all commits
- List of files changed
- Testing checklist
- Review checklist
- Clear indication it's from automation

## Manual Controls

### Check Current Branch
```bash
cd /home/<USER>/github/cc-v1
git branch --show-current
```

### View Pending Changes
```bash
git log develop..HEAD --oneline
```

### Create PR Manually
```bash
gh pr create --draft --title "Night Session: $(date +%Y-%m-%d)" --base develop
```

### Review Before Merging
1. Check the PR on GitHub
2. Review all changes
3. Run tests
4. Merge only when confident

## Configuration

Edit `git_workflow.py` to adjust:
- `commit_threshold`: Number of changes before PR (default: 5)
- `base_branch`: Target branch (default: "develop")

## Troubleshooting

### If Git Operations Fail
The system continues without Git integration - no work is lost.

### To Resume a Session
The system automatically detects and resumes incomplete sessions.

### To Start Fresh
Delete `.claude-collective-git-context.json` in the repo root.

## Best Practices

1. **Review Every Morning**: Check the draft PRs created overnight
2. **Test Before Merging**: Run full test suite on PR branches
3. **Squash if Needed**: GitHub's squash-merge works well for these PRs
4. **Keep PR Size Reasonable**: The 5-commit threshold prevents huge PRs

## Integration with Agents

Agents don't need to know about Git - the orchestrator handles everything:
- Detects file changes from agent outputs
- Groups related changes
- Creates meaningful commit messages
- Manages branch lifecycle

This keeps the agent code simple while ensuring proper version control.