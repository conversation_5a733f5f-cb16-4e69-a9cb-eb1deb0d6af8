#!/bin/bash
# Start the async Claude Collective system with tmux

set -e

BASE_PATH="/home/<USER>/github/cc-v1/_dev-automation-beta"
SESSION_NAME="claude-collective-async"

echo "╔══════════════════════════════════════════════════════════════════╗"
echo "║        🚀 Starting Async Claude Collective System                ║"
echo "╠══════════════════════════════════════════════════════════════════╣"
echo "║                                                                  ║"
echo "║  This will start:                                                ║"
echo "║  • Async Orchestrator with agent pool                           ║"
echo "║  • Real-time monitoring dashboard                                ║"
echo "║  • High-performance task processing                              ║"
echo "║                                                                  ║"
echo "║  Dashboard: http://localhost:8200                                ║"
echo "║                                                                  ║"
echo "╚══════════════════════════════════════════════════════════════════╝"
echo ""

# Check if tmux session already exists
if tmux has-session -t $SESSION_NAME 2>/dev/null; then
    echo "⚠️  Session '$SESSION_NAME' already exists!"
    echo ""
    echo "Options:"
    echo "1. Attach to existing session: tmux attach -t $SESSION_NAME"
    echo "2. Kill existing session: tmux kill-session -t $SESSION_NAME"
    echo ""
    exit 1
fi

# Create new tmux session
echo "📦 Creating tmux session: $SESSION_NAME"
tmux new-session -d -s $SESSION_NAME -n orchestrator

# Window 1: Async Orchestrator
echo "🎯 Starting Async Orchestrator..."
tmux send-keys -t $SESSION_NAME:orchestrator "cd $BASE_PATH" C-m
tmux send-keys -t $SESSION_NAME:orchestrator "python3 claude-collective/core/async_orchestrator.py" C-m

# Window 2: Monitoring Dashboard
echo "📊 Starting Monitoring Dashboard..."
tmux new-window -t $SESSION_NAME -n dashboard
tmux send-keys -t $SESSION_NAME:dashboard "cd $BASE_PATH" C-m
tmux send-keys -t $SESSION_NAME:dashboard "python3 claude-collective/monitoring/dashboard.py" C-m

# Window 3: Logs viewer
echo "📜 Setting up logs viewer..."
tmux new-window -t $SESSION_NAME -n logs
tmux send-keys -t $SESSION_NAME:logs "cd $BASE_PATH/claude-collective/logs" C-m
tmux send-keys -t $SESSION_NAME:logs "tail -f agents/*.log orchestrator/*.log 2>/dev/null | grep -v '^$'" C-m

# Window 4: Interactive control
echo "🎮 Setting up interactive control..."
tmux new-window -t $SESSION_NAME -n control
tmux send-keys -t $SESSION_NAME:control "cd $BASE_PATH" C-m
tmux send-keys -t $SESSION_NAME:control "# Interactive control window" C-m
tmux send-keys -t $SESSION_NAME:control "# Run: python3 claude-collective/launchers/start-async-collective.py" C-m
tmux send-keys -t $SESSION_NAME:control "# to get interactive controls" C-m

echo ""
echo "✅ Async Claude Collective started successfully!"
echo ""
echo "📋 Tmux Commands:"
echo "  • Attach to session:  tmux attach -t $SESSION_NAME"
echo "  • Detach from tmux:   Ctrl+B, then D"
echo "  • Switch windows:     Ctrl+B, then window number (0-3)"
echo "  • Kill session:       tmux kill-session -t $SESSION_NAME"
echo ""
echo "🌐 Access Points:"
echo "  • Dashboard:          http://localhost:8200"
echo "  • Orchestrator API:   http://localhost:8100 (when implemented)"
echo ""
echo "💡 Tips:"
echo "  • Window 0: Orchestrator (main system)"
echo "  • Window 1: Dashboard (web monitoring)"
echo "  • Window 2: Logs (real-time log viewing)"
echo "  • Window 3: Control (interactive commands)"
echo ""

# Optionally attach to session
read -p "Would you like to attach to the session now? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    tmux attach -t $SESSION_NAME
fi