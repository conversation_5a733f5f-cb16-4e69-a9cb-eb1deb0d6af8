#!/usr/bin/env python3
"""
Async Claude Collective Launcher
Fast, asynchronous multi-agent orchestration system
"""

import asyncio
import sys
import signal
import json
import time
from pathlib import Path
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from core.async_orchestrator import AsyncOrchestrator, AgentRole

class AsyncCollectiveLauncher:
    """Launcher for the async Claude Collective system"""
    
    def __init__(self, base_path: Path):
        self.base_path = base_path
        self.orchestrator = AsyncOrchestrator(base_path)
        self.agents = []
        self.running = False
        
    async def start(self):
        """Start the collective system"""
        print("""
╔══════════════════════════════════════════════════════════════════╗
║          🚀 Claude Collective - Async Orchestration              ║
╠══════════════════════════════════════════════════════════════════╣
║                                                                  ║
║  Starting high-performance asynchronous agent system...          ║
║                                                                  ║
║  Features:                                                       ║
║  • Non-blocking async operations                                 ║
║  • Agent pool with specialized roles                             ║
║  • Real-time task distribution                                   ║
║  • Zero waiting time between tasks                               ║
║                                                                  ║
╚══════════════════════════════════════════════════════════════════╝
        """)
        
        self.running = True
        
        # Start orchestrator
        print("🎯 Starting orchestrator...")
        await self.orchestrator.start()
        
        # Start initial tasks
        await self._seed_initial_tasks()
        
        # Start monitoring
        asyncio.create_task(self._monitor_system())
        
        # Start interactive shell
        await self._interactive_shell()
        
    async def _seed_initial_tasks(self):
        """Seed some initial tasks to get the system going"""
        initial_tasks = [
            ("analyze", "Analyze the current CC-V1 project state and identify pending tasks", AgentRole.ARCHITECT),
            ("analyze", "Research best practices for async Python applications", AgentRole.RESEARCHER),
            ("test", "Run comprehensive test suite for the operator feature", AgentRole.QA),
        ]
        
        for task_type, context, role in initial_tasks:
            task_id = await self.orchestrator.submit_task(task_type, context, role)
            print(f"📋 Submitted task {task_id} to {role.value}")
            
    async def _monitor_system(self):
        """Monitor system status and display updates"""
        while self.running:
            await asyncio.sleep(10)
            
            status = self.orchestrator.get_status()
            
            # Clear screen and show status
            print("\033[H\033[J")  # Clear screen
            print(f"""
╔══════════════════════════════════════════════════════════════════╗
║              Claude Collective - System Status                   ║
║                  {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}                         ║
╠══════════════════════════════════════════════════════════════════╣
║ Metrics:                                                         ║
║   Tasks Created:   {status['metrics']['tasks_created']:>10}                            ║
║   Tasks Completed: {status['metrics']['tasks_completed']:>10}                            ║
║   Avg Time:        {status['metrics']['avg_completion_time']:>10.2f}s                        ║
║   Queue Size:      {status['queue_size']:>10}                            ║
║   Errors:          {status['metrics']['errors']:>10}                            ║
╠══════════════════════════════════════════════════════════════════╣
║ Active Agents:                                                   ║""")
            
            for agent_id, agent_info in status['agents'].items():
                print(f"║   {agent_id:<20} {agent_info['status']:>10} ({agent_info['tasks_completed']} tasks)    ║")
                
            print(f"""╠══════════════════════════════════════════════════════════════════╣
║ Swarm Mode: {'🐝 ENABLED (10x+)' if status.get('swarm_mode') else '🐌 DISABLED':<20} Available: {'✅' if status.get('swarm_available') else '❌'}      ║
╠══════════════════════════════════════════════════════════════════╣
║ Commands: (s)ubmit, (w)arm toggle, (q)uit, (r)eport             ║
╚══════════════════════════════════════════════════════════════════╝""")
            
    async def _interactive_shell(self):
        """Interactive command shell"""
        while self.running:
            try:
                # Non-blocking input
                command = await asyncio.get_event_loop().run_in_executor(
                    None, input, "\n> "
                )
                
                if command.lower() == 'q':
                    await self.stop()
                    break
                elif command.lower() == 's':
                    await self._submit_task_interactive()
                elif command.lower() == 'r':
                    await self._generate_report()
                elif command.lower() == 'w':
                    await self._toggle_swarm_mode()
                else:
                    print(f"Unknown command: {command}")
                    
            except KeyboardInterrupt:
                await self.stop()
                break
            except Exception as e:
                print(f"Error: {e}")
                
    async def _submit_task_interactive(self):
        """Interactive task submission"""
        print("\n=== Submit New Task ===")
        
        task_types = ["analyze", "fix_bug", "test", "integrate", "deploy"]
        print("Task types:", ", ".join(task_types))
        task_type = input("Task type: ").strip()
        
        if task_type not in task_types:
            print("Invalid task type")
            return
            
        context = input("Task context: ").strip()
        
        roles = [r.value for r in AgentRole if r != AgentRole.ORCHESTRATOR]
        print("Available roles:", ", ".join(roles))
        role_input = input("Target role (default: developer_midlevel): ").strip()
        
        role = AgentRole.DEVELOPER_MIDLEVEL
        for r in AgentRole:
            if r.value == role_input:
                role = r
                break
                
        task_id = await self.orchestrator.submit_task(task_type, context, role)
        print(f"✅ Task {task_id} submitted successfully!")
        
    async def _generate_report(self):
        """Generate performance report"""
        status = self.orchestrator.get_status()
        
        report_path = self.base_path / "claude-collective" / "reports" / f"report_{int(time.time())}.json"
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(status, f, indent=2)
            
        print(f"\n📊 Report saved to: {report_path}")
        
    async def _toggle_swarm_mode(self):
        """Toggle swarm mode on/off"""
        status = self.orchestrator.get_status()
        
        if not status.get('swarm_available'):
            print("\n❌ Claude-flow is not available. Cannot enable swarm mode.")
            print("Install with: npm install -g claude-flow@alpha")
            return
            
        if status.get('swarm_mode'):
            self.orchestrator.disable_swarm_mode()
            print("\n🐌 Swarm mode DISABLED - using simple processing")
        else:
            if self.orchestrator.enable_swarm_mode():
                print("\n🐝 Swarm mode ENABLED - 10x+ performance boost activated!")
                print("Tasks will now use swarm intelligence for massive speedup")
            else:
                print("\n❌ Failed to enable swarm mode")
        
    async def stop(self):
        """Stop the collective system"""
        print("\n🛑 Stopping Claude Collective...")
        self.running = False
        
        # Save final state
        await self._generate_report()
        
        print("✅ System stopped successfully")

async def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Async Claude Collective Launcher")
    parser.add_argument("--base-path", 
                       default="/home/<USER>/github/cc-v1/_dev-automation-beta",
                       help="Base path for automation framework")
    
    args = parser.parse_args()
    
    launcher = AsyncCollectiveLauncher(Path(args.base_path))
    
    # Setup signal handlers
    def signal_handler(sig, frame):
        asyncio.create_task(launcher.stop())
        
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start the system
    await launcher.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n✅ Graceful shutdown complete")