#!/usr/bin/env python3
"""
Browser-based Claude Orchestration
Orchestrates multiple Claude.ai instances in browser tabs
"""

import asyncio
import json
import sys
import time
from pathlib import Path
from datetime import datetime
import argparse

sys.path.insert(0, str(Path(__file__).parent))

from core.claude_instance_orchestrator import ClaudeInstanceOrchestrator
from core.logger_config import setup_logger

logger, _, _ = setup_logger('browser_orchestrator')

class BrowserOrchestrationManager:
    """Manages browser-based Claude orchestration"""
    
    def __init__(self, base_path: Path):
        self.base_path = base_path
        self.orchestrator = ClaudeInstanceOrchestrator(base_path)
        self.task_dir = base_path / "claude-collective" / "tasks"
        self.message_dir = base_path / "claude-collective" / "messages"
        self.agent_dir = base_path / "claude-collective" / "agents"
        
        # Create directories
        for dir_path in [self.task_dir, self.message_dir, self.agent_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
            
    async def setup_mode(self):
        """Run in setup mode to save login session"""
        logger.info("🔧 Running in setup mode")
        logger.info("Please login to Claude.ai when the browser opens")
        
        await self.orchestrator.start()
        
        logger.info("✅ Login successful! Session saved.")
        logger.info("You can now run without --setup flag")
        
        # Keep browser open for verification
        input("Press Enter to close browser...")
        
        await self.orchestrator.shutdown()
        
    async def create_sample_tasks(self):
        """Create sample tasks for demonstration"""
        sample_tasks = [
            {
                "id": f"task-demo-{int(time.time())}",
                "description": "Analyze the project structure and identify main components",
                "type": "research",
                "priority": "high",
                "status": "pending",
                "created_at": datetime.now().isoformat()
            },
            {
                "id": f"task-demo-{int(time.time())+1}",
                "description": "Review the orchestration system architecture",
                "type": "review",
                "priority": "medium", 
                "status": "pending",
                "created_at": datetime.now().isoformat()
            },
            {
                "id": f"task-demo-{int(time.time())+2}",
                "description": "Implement a simple monitoring endpoint",
                "type": "implement",
                "priority": "medium",
                "status": "pending",
                "created_at": datetime.now().isoformat()
            }
        ]
        
        for task in sample_tasks:
            task_file = self.task_dir / f"{task['id']}.json"
            task_file.write_text(json.dumps(task, indent=2))
            
        logger.info(f"📝 Created {len(sample_tasks)} sample tasks")
        
    async def monitor_task_queue(self):
        """Monitor task queue and distribute to agents"""
        while True:
            try:
                # Check for pending tasks
                pending_tasks = []
                for task_file in self.task_dir.glob("*.json"):
                    try:
                        task_data = json.loads(task_file.read_text())
                        if task_data.get("status") == "pending":
                            pending_tasks.append(task_data)
                    except:
                        continue
                        
                # Route pending tasks
                for task in pending_tasks:
                    instance_id = await self.orchestrator.route_task(task)
                    if instance_id:
                        # Update task status
                        task["status"] = "assigned"
                        task["assigned_to"] = instance_id
                        task["assigned_at"] = datetime.now().isoformat()
                        
                        task_file = self.task_dir / f"{task['id']}.json"
                        task_file.write_text(json.dumps(task, indent=2))
                        
                        logger.info(f"✅ Task {task['id']} assigned to {instance_id}")
                        
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Task monitor error: {e}")
                await asyncio.sleep(30)
                
    async def update_agent_status(self):
        """Update agent status files"""
        while True:
            try:
                for instance_id, instance in self.orchestrator.instances.items():
                    status = {
                        "id": instance_id,
                        "role": instance.role.value,
                        "active": instance.is_active(),
                        "current_task": instance.current_task,
                        "last_activity": datetime.fromtimestamp(instance.last_activity).isoformat(),
                        "url": instance.session_url
                    }
                    
                    status_file = self.agent_dir / f"{instance_id}_status.json"
                    status_file.write_text(json.dumps(status, indent=2))
                    
                await asyncio.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                logger.error(f"Status update error: {e}")
                await asyncio.sleep(30)
                
    async def run_orchestration(self):
        """Run the full orchestration system"""
        logger.info("🚀 Starting Browser-Based Claude Orchestration")
        
        # Start orchestrator
        await self.orchestrator.start()
        
        # Create sample tasks if none exist
        if not list(self.task_dir.glob("*.json")):
            await self.create_sample_tasks()
            
        # Start monitoring tasks
        monitor_task = asyncio.create_task(self.monitor_task_queue())
        status_task = asyncio.create_task(self.update_agent_status())
        
        logger.info("✅ Orchestration system running")
        logger.info(f"📁 Task directory: {self.task_dir}")
        logger.info(f"📁 Message directory: {self.message_dir}")
        logger.info(f"📁 Agent directory: {self.agent_dir}")
        logger.info("")
        logger.info("💡 Tips:")
        logger.info("- Add .json files to tasks/ directory to create new tasks")
        logger.info("- Monitor agent status in agents/*_status.json")
        logger.info("- View browser tabs to see agents working")
        logger.info("")
        logger.info("Press Ctrl+C to stop")
        
        try:
            # Keep running
            await asyncio.gather(monitor_task, status_task)
        except KeyboardInterrupt:
            logger.info("Shutting down...")
        finally:
            await self.orchestrator.shutdown()

async def main():
    parser = argparse.ArgumentParser(description="Browser-based Claude Orchestration")
    parser.add_argument("--setup", action="store_true", help="Run in setup mode to save login")
    parser.add_argument("--clean", action="store_true", help="Clean browser data and start fresh")
    args = parser.parse_args()
    
    base_path = Path(__file__).parent.parent.parent
    manager = BrowserOrchestrationManager(base_path)
    
    if args.clean:
        browser_data = base_path / "browser_data"
        if browser_data.exists():
            import shutil
            shutil.rmtree(browser_data)
            logger.info("🧹 Cleaned browser data")
            
    if args.setup:
        await manager.setup_mode()
    else:
        await manager.run_orchestration()

if __name__ == "__main__":
    asyncio.run(main())