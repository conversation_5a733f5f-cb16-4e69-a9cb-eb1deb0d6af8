{"agents": {"architect_0": {"role": "architect", "status": "idle", "tasks_completed": 0}, "developer_intern_0": {"role": "developer_intern", "status": "idle", "tasks_completed": 0}, "developer_intern_1": {"role": "developer_intern", "status": "idle", "tasks_completed": 0}, "developer_junior_0": {"role": "developer_junior", "status": "idle", "tasks_completed": 0}, "developer_junior_1": {"role": "developer_junior", "status": "idle", "tasks_completed": 0}, "developer_midlevel_0": {"role": "developer_midlevel", "status": "idle", "tasks_completed": 4}, "developer_midlevel_1": {"role": "developer_midlevel", "status": "idle", "tasks_completed": 4}, "developer_midlevel_2": {"role": "developer_midlevel", "status": "idle", "tasks_completed": 4}, "developer_senior_0": {"role": "developer_senior", "status": "idle", "tasks_completed": 1}, "qa_0": {"role": "qa", "status": "idle", "tasks_completed": 0}, "qa_1": {"role": "qa", "status": "idle", "tasks_completed": 0}, "researcher_0": {"role": "researcher", "status": "idle", "tasks_completed": 0}}, "metrics": {"tasks_created": 13, "tasks_completed": 1, "avg_completion_time": 0.21623802185058594, "errors": 0}, "queue_size": 0, "timestamp": "2025-08-04T11:37:24.807516", "swarm_mode": true, "swarm_available": true, "autodiscovery": {"total_discovered": 9, "scan_interval": 60, "running": true, "sources_monitored": ["Documentation files (*.md)", "Code TODOs", "Git commits", "Workflow states", "Test results"]}}