#!/usr/bin/env python3
"""
Real-time web dashboard for Claude Collective monitoring
Simple HTTP server with WebSocket for live updates
"""

import asyncio
import json
import time
from pathlib import Path
from datetime import datetime
from aiohttp import web
import aiohttp_cors

# HTML template for the dashboard
DASHBOARD_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>Claude Collective - Live Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #e0e0e0;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        h1 {
            color: #4a9eff;
            text-align: center;
            margin-bottom: 30px;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #4a9eff;
            margin: 10px 0;
        }
        .metric-label {
            color: #999;
            font-size: 0.9em;
        }
        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        .agent-card {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        .agent-card.working {
            border-color: #4a9eff;
            box-shadow: 0 0 10px rgba(74, 158, 255, 0.3);
        }
        .agent-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .agent-name {
            font-weight: bold;
            color: #4a9eff;
        }
        .agent-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
        }
        .status-idle {
            background: #333;
            color: #999;
        }
        .status-working {
            background: #2a4a2a;
            color: #4ade80;
        }
        .agent-stats {
            font-size: 0.9em;
            color: #999;
        }
        .live-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #4ade80;
            border-radius: 50%;
            margin-right: 5px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .timestamp {
            text-align: center;
            color: #666;
            margin-top: 20px;
        }
        .error {
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Claude Collective - Live Dashboard</h1>
        
        <div class="metrics" id="metrics">
            <div class="metric-card">
                <div class="metric-label">Tasks Created</div>
                <div class="metric-value" id="tasks-created">0</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Tasks Completed</div>
                <div class="metric-value" id="tasks-completed">0</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Avg Completion Time</div>
                <div class="metric-value" id="avg-time">0s</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Queue Size</div>
                <div class="metric-value" id="queue-size">0</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Errors</div>
                <div class="metric-value error" id="errors">0</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Status</div>
                <div class="metric-value"><span class="live-indicator"></span>Live</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Swarm Mode</div>
                <div class="metric-value" id="swarm-mode">-</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Autodiscovery</div>
                <div class="metric-value" id="autodiscovery">-</div>
            </div>
        </div>
        
        <h2>Agent Pool Status</h2>
        <div class="agents-grid" id="agents-grid">
            <!-- Agent cards will be inserted here -->
        </div>
        
        <div class="timestamp" id="timestamp">
            Waiting for connection...
        </div>
    </div>
    
    <script>
        const ws = new WebSocket('ws://localhost:8200/ws');
        
        ws.onopen = () => {
            console.log('Connected to monitoring server');
        };
        
        ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            updateDashboard(data);
        };
        
        ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
        
        ws.onclose = () => {
            console.log('Disconnected from monitoring server');
            document.getElementById('timestamp').textContent = 'Connection lost - refresh to reconnect';
        };
        
        function updateDashboard(data) {
            // Update metrics
            document.getElementById('tasks-created').textContent = data.metrics.tasks_created;
            document.getElementById('tasks-completed').textContent = data.metrics.tasks_completed;
            document.getElementById('avg-time').textContent = data.metrics.avg_completion_time.toFixed(1) + 's';
            document.getElementById('queue-size').textContent = data.queue_size;
            document.getElementById('errors').textContent = data.metrics.errors;
            
            // Update swarm mode status
            const swarmStatus = data.swarm_mode ? '🐝 10x+' : '🐌 Normal';
            document.getElementById('swarm-mode').textContent = swarmStatus;
            
            // Update autodiscovery status
            if (data.autodiscovery && data.autodiscovery.running) {
                const discoveredCount = data.autodiscovery.total_discovered || 0;
                document.getElementById('autodiscovery').textContent = `🔍 ${discoveredCount} found`;
            } else {
                document.getElementById('autodiscovery').textContent = '❌ Off';
            }
            
            // Update agents
            const agentsGrid = document.getElementById('agents-grid');
            agentsGrid.innerHTML = '';
            
            for (const [agentId, agentInfo] of Object.entries(data.agents)) {
                const card = document.createElement('div');
                card.className = 'agent-card ' + (agentInfo.status === 'working' ? 'working' : '');
                
                card.innerHTML = `
                    <div class="agent-header">
                        <span class="agent-name">${agentId}</span>
                        <span class="agent-status status-${agentInfo.status}">${agentInfo.status}</span>
                    </div>
                    <div class="agent-stats">
                        Role: ${agentInfo.role}<br>
                        Tasks Completed: ${agentInfo.tasks_completed}
                    </div>
                `;
                
                agentsGrid.appendChild(card);
            }
            
            // Update timestamp
            document.getElementById('timestamp').textContent = 
                'Last updated: ' + new Date(data.timestamp).toLocaleString();
        }
    </script>
</body>
</html>
"""

class MonitoringDashboard:
    """Web-based monitoring dashboard for Claude Collective"""
    
    def __init__(self, orchestrator_path: Path, port: int = 8200):
        self.orchestrator_path = orchestrator_path
        self.port = port
        self.app = web.Application()
        self.websockets = set()
        self.setup_routes()
        
    def setup_routes(self):
        """Setup web routes"""
        self.app.router.add_get('/', self.index_handler)
        self.app.router.add_get('/ws', self.websocket_handler)
        self.app.router.add_get('/api/status', self.status_handler)
        
        # Setup CORS
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
            )
        })
        
        for route in list(self.app.router._resources):
            cors.add(route)
            
    async def index_handler(self, request):
        """Serve the dashboard HTML"""
        return web.Response(text=DASHBOARD_HTML, content_type='text/html')
        
    async def status_handler(self, request):
        """Get current system status"""
        status = self.get_system_status()
        return web.json_response(status)
        
    async def websocket_handler(self, request):
        """Handle WebSocket connections"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        self.websockets.add(ws)
        
        try:
            # Send initial status
            status = self.get_system_status()
            await ws.send_str(json.dumps(status))
            
            # Keep connection alive
            async for msg in ws:
                if msg.type == web.WSMsgType.ERROR:
                    print(f'WebSocket error: {ws.exception()}')
                    
        finally:
            self.websockets.discard(ws)
            
        return ws
        
    def get_system_status(self):
        """Get system status from state files"""
        # Read orchestrator state
        orchestrator_state_file = self.orchestrator_path / "state" / "orchestrator" / "system_state.json"
        
        if orchestrator_state_file.exists():
            with open(orchestrator_state_file, 'r') as f:
                return json.load(f)
        
        # Return default status if no state file
        return {
            "agents": {},
            "metrics": {
                "tasks_created": 0,
                "tasks_completed": 0,
                "avg_completion_time": 0,
                "errors": 0
            },
            "queue_size": 0,
            "timestamp": datetime.now().isoformat()
        }
        
    async def broadcast_status(self):
        """Broadcast status updates to all connected clients"""
        while True:
            await asyncio.sleep(2)  # Update every 2 seconds
            
            if self.websockets:
                status = self.get_system_status()
                
                # Send to all connected clients
                disconnected = set()
                for ws in self.websockets:
                    try:
                        await ws.send_str(json.dumps(status))
                    except ConnectionResetError:
                        disconnected.add(ws)
                        
                # Remove disconnected clients
                self.websockets -= disconnected
                
    async def start(self):
        """Start the monitoring dashboard"""
        # Start broadcast task
        asyncio.create_task(self.broadcast_status())
        
        # Setup and run web server
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, 'localhost', self.port)
        
        print(f"""
╔══════════════════════════════════════════════════════════════════╗
║         Claude Collective - Monitoring Dashboard                 ║
╠══════════════════════════════════════════════════════════════════╣
║                                                                  ║
║  Dashboard running at: http://localhost:{self.port:<38}║
║                                                                  ║
║  Open in your browser to see real-time system status            ║
║                                                                  ║
╚══════════════════════════════════════════════════════════════════╝
        """)
        
        await site.start()

async def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Claude Collective Monitoring Dashboard")
    parser.add_argument("--base-path", 
                       default="/home/<USER>/github/cc-v1/_dev-automation-beta",
                       help="Base path for collective")
    parser.add_argument("--port", type=int, default=8200,
                       help="Port for web server")
    
    args = parser.parse_args()
    
    dashboard = MonitoringDashboard(
        Path(args.base_path) / "claude-collective",
        args.port
    )
    
    await dashboard.start()
    
    # Keep running
    try:
        await asyncio.Event().wait()
    except KeyboardInterrupt:
        print("\nDashboard stopped")

if __name__ == "__main__":
    asyncio.run(main())