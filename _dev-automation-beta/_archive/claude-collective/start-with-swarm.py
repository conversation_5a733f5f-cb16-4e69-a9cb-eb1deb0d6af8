#!/usr/bin/env python3
"""
Start Claude Collective with Swarm Mode enabled by default
"""

import asyncio
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))
sys.path.insert(0, str(Path(__file__).parent.parent))

# Use relative imports since we're in the claude-collective directory
from core.async_orchestrator import AsyncOrchestrator, AgentRole

async def start_with_swarm():
    """Start the orchestrator with swarm mode enabled"""
    print("""
╔══════════════════════════════════════════════════════════════════╗
║      🐝 Claude Collective - Starting with SWARM MODE 🐝          ║
╠══════════════════════════════════════════════════════════════════╣
║                                                                  ║
║  Performance Mode: SWARM INTELLIGENCE (150x)                     ║
║  • Claude-flow v2.0.0-alpha.84 integration                       ║
║  • Multi-agent parallel processing                               ║
║  • Neural optimization patterns                                  ║
║                                                                  ║
╚══════════════════════════════════════════════════════════════════╝
    """)
    
    # First, ensure we're on a nightly branch
    print("\n🔀 Setting up Git workflow...")
    from core.git_workflow import GitWorkflowManager
    
    git_manager = GitWorkflowManager(Path("/home/<USER>/github/cc-v1"))
    git_context = git_manager.initialize_night_session()
    print(f"✅ Created and switched to branch: {git_context.branch_name}")
    print("   All work will be safely isolated from develop branch\n")
    
    base_path = Path("/home/<USER>/github/cc-v1/_dev-automation-beta")
    orchestrator = AsyncOrchestrator(base_path)
    
    # Start orchestrator (it will detect the git context)
    await orchestrator.start()
    
    # Enable swarm mode
    if orchestrator.enable_swarm_mode():
        print("\n✅ SWARM MODE ACTIVATED - Expect 150x performance!")
        
        print("\n🔍 AUTODISCOVERY MODE ACTIVE")
        print("   • Continuously scanning documentation for tasks")
        print("   • Monitoring TODOs, test failures, and workflow states")
        print("   • Auto-submitting discovered intents to swarm agents")
        print("   • Scan interval: Every 60 seconds")
        print("\n   The system will automatically discover and process tasks!")
        
        # Manually seed some priority tasks from CLAUDE.md
        print("\n📋 Seeding priority tasks from CLAUDE.md...")
        priority_tasks = [
            ("integrate", "Connect live-demo components to main operator interface"),
            ("implement", "Update operator dashboard routing for demo features"),
            ("implement", "Update environment configuration for new services"),
            ("test", "Verify deployment health and performance"),
        ]
        
        for task_type, context in priority_tasks:
            task_id = await orchestrator.submit_task(task_type, context)
            print(f"   • Submitted: {context[:60]}...")
            
    else:
        print("\n⚠️  Claude-flow not available - running in standard async mode")
        
    print("\n🌐 Dashboard: http://localhost:8200")
    print("📝 Logs: tail -f logs/*/*.log")
    print("\nPress Ctrl+C to stop\n")
    
    # Keep running
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        print("\n✅ Shutting down...")

if __name__ == "__main__":
    try:
        asyncio.run(start_with_swarm())
    except KeyboardInterrupt:
        print("\n✅ Graceful shutdown complete")