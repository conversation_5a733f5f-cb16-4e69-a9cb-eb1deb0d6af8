"""
State Manager for Top-Level Orchestrator
Manages persistent state, conversation history, and exploration tracking
"""

import asyncio
import json
import sqlite3
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any


class StateManager:
    """
    Manages state persistence for the orchestrator
    Tracks exploration areas, conversation history, and system state
    """
    
    def __init__(self, repo_path: Path):
        self.repo_path = repo_path
        self.state_dir = repo_path / "_dev-automation-beta" / "memory"
        self.state_dir.mkdir(parents=True, exist_ok=True)
        
        # Database for persistent storage
        self.db_path = self.state_dir / "orchestrator_state.db"
        
        # In-memory state
        self.current_session = {
            'session_id': datetime.now().isoformat(),
            'started_at': datetime.now().isoformat(),
            'last_activity': time.time(),
            'exploration_areas': set(),
            'conversation_count': 0,
            'restart_count': 0
        }
        
    async def initialize(self):
        """Initialize the state management system"""
        await self._init_database()
        await self._load_session_state()
        
    async def _init_database(self):
        """Initialize SQLite database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Conversation history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    speaker TEXT NOT NULL,
                    message TEXT NOT NULL,
                    session_id TEXT NOT NULL
                )
            ''')
            
            # Exploration tracking table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS explorations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    area TEXT NOT NULL,
                    details TEXT,
                    session_id TEXT NOT NULL
                )
            ''')
            
            # System events table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    details TEXT,
                    session_id TEXT NOT NULL
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"⚠️ Database initialization failed: {e}")
            
    async def _load_session_state(self):
        """Load or create session state"""
        state_file = self.state_dir / "current_session.json"
        
        if state_file.exists():
            try:
                with open(state_file, 'r') as f:
                    saved_state = json.load(f)
                    
                # Check if session is recent (within 24 hours)
                started_at = datetime.fromisoformat(saved_state.get('started_at', ''))
                if datetime.now() - started_at < timedelta(hours=24):
                    self.current_session.update(saved_state)
                    self.current_session['restart_count'] += 1
                    
            except Exception as e:
                print(f"⚠️ Failed to load session state: {e}")
                
        await self._save_session_state()
        
    async def _save_session_state(self):
        """Save current session state"""
        state_file = self.state_dir / "current_session.json"
        
        # Convert set to list for JSON serialization
        state_to_save = self.current_session.copy()
        state_to_save['exploration_areas'] = list(state_to_save['exploration_areas'])
        
        try:
            with open(state_file, 'w') as f:
                json.dump(state_to_save, f, indent=2)
        except Exception as e:
            print(f"⚠️ Failed to save session state: {e}")
            
    async def log_interaction(self, speaker: str, message: str):
        """Log a conversation interaction"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO conversations (timestamp, speaker, message, session_id)
                VALUES (?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                speaker,
                message[:1000],  # Truncate long messages
                self.current_session['session_id']
            ))
            
            conn.commit()
            conn.close()
            
            self.current_session['conversation_count'] += 1
            self.current_session['last_activity'] = time.time()
            await self._save_session_state()
            
        except Exception as e:
            print(f"⚠️ Failed to log interaction: {e}")
            
    async def track_exploration(self, area: str, details: str = ""):
        """Track an exploration area"""
        try:
            self.current_session['exploration_areas'].add(area)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO explorations (timestamp, area, details, session_id)
                VALUES (?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                area,
                details,
                self.current_session['session_id']
            ))
            
            conn.commit()
            conn.close()
            
            await self._save_session_state()
            
        except Exception as e:
            print(f"⚠️ Failed to track exploration: {e}")
            
    async def get_unexplored_areas(self) -> List[str]:
        """Get areas that haven't been explored recently"""
        all_areas = [
            "frontend", "backend", "tests", "documentation", 
            "configuration", "deployment", "security", "performance",
            "database", "api", "ui", "automation", "monitoring"
        ]
        
        explored = self.current_session['exploration_areas']
        return [area for area in all_areas if area not in explored]
        
    async def get_recent_exploration(self) -> Optional[str]:
        """Get the most recent exploration area"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT area FROM explorations 
                WHERE session_id = ? 
                ORDER BY timestamp DESC 
                LIMIT 1
            ''', (self.current_session['session_id'],))
            
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result else None
            
        except Exception as e:
            print(f"⚠️ Failed to get recent exploration: {e}")
            return None
