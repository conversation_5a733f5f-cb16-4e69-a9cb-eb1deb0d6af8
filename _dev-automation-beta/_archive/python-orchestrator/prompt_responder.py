"""
Prompt Responder - Generates intelligent responses to claude-flow prompts
Encourages exploration and discovery rather than dictating tasks
"""

import random
import re
from pathlib import Path
from typing import List, Optional


class PromptResponder:
    """
    Responds to claude-flow prompts in a way that encourages
    autonomous exploration and discovery
    """
    
    def __init__(self, repo_path: Path, state_manager):
        self.repo_path = repo_path
        self.state_manager = state_manager
        
        # Response strategies for different prompt types
        self.response_strategies = {
            "continue": self._respond_to_continue,
            "what_next": self._respond_to_what_next,
            "error": self._respond_to_error,
            "choice": self._respond_to_choice,
            "details": self._respond_to_details_request,
            "idle": self._respond_to_idle,
        }
        
    async def generate_response(self, prompt: str, context: List[str]) -> Optional[str]:
        """Generate an intelligent response based on prompt and context"""
        prompt_lower = prompt.lower()
        
        # Identify prompt type
        prompt_type = self._identify_prompt_type(prompt_lower)
        
        # Get appropriate response strategy
        strategy = self.response_strategies.get(prompt_type, self._default_response)
        
        # Generate response
        return await strategy(prompt, context)
        
    def _identify_prompt_type(self, prompt: str) -> str:
        """Identify the type of prompt"""
        if re.search(r"continue\?|shall i continue|should i proceed", prompt):
            return "continue"
        elif re.search(r"what.*next|what should i|what would you like", prompt):
            return "what_next"
        elif re.search(r"error:|failed|issue|problem", prompt):
            return "error"
        elif re.search(r"choose|select|which|option", prompt):
            return "choice"
        elif re.search(r"more details|explain|elaborate", prompt):
            return "details"
        else:
            return "idle"
            
    async def _respond_to_continue(self, prompt: str, context: List[str]) -> str:
        """Respond to continue prompts"""
        responses = [
            "Yes, please continue exploring that area.",
            "Absolutely, that sounds like a valuable direction to pursue.",
            "Yes, and see if you can find any related patterns or issues.",
            "Please do, and feel free to dig deeper if you find something interesting.",
            "Yes, continue. Your autonomous exploration is valuable.",
        ]
        
        # Add context-aware encouragement
        if any("test" in line.lower() for line in context[-3:]):
            responses.append("Yes, testing is crucial. See what you can discover.")
        elif any("bug" in line.lower() or "error" in line.lower() for line in context[-3:]):
            responses.append("Yes, investigating that issue could be very helpful.")
        elif any("todo" in line.lower() or "fixme" in line.lower() for line in context[-3:]):
            responses.append("Yes, those markers often indicate areas needing attention.")
            
        return random.choice(responses)
        
    async def _respond_to_what_next(self, prompt: str, context: List[str]) -> str:
        """Respond to 'what next' prompts by encouraging exploration"""
        
        # Check what areas haven't been explored recently
        unexplored = await self.state_manager.get_unexplored_areas()
        
        suggestions = [
            "Have you explored the CLAUDE.md file? It contains valuable context about the project's current state and priorities.",
            "You might want to check for any TODO or FIXME comments in the codebase - they often indicate areas needing attention.",
            "Consider running the test suite to see if there are any failing tests that need investigation.",
            "The _dev-automation-beta directory has interesting automation patterns you could explore and potentially improve.",
            "Look for TypeScript compilation errors or linting issues - they're often quick wins that improve code quality.",
            "Check if there are any open issues in the git repository that align with your capabilities.",
            "Explore the project structure to understand how different components interact - you might spot optimization opportunities.",
            "Review recent commits to understand what's been changing and where you might contribute.",
        ]
        
        # Add specific suggestions based on unexplored areas
        if "frontend" in unexplored:
            suggestions.append("The frontend components might have integration opportunities worth exploring.")
        if "backend" in unexplored:
            suggestions.append("The backend services could benefit from your analysis - check for patterns or improvements.")
        if "tests" in unexplored:
            suggestions.append("The test coverage might reveal gaps worth addressing.")
            
        # Encourage autonomous decision-making
        prefix = random.choice([
            "Here's a thought: ",
            "You could explore this: ",
            "Consider investigating: ",
            "What do you think about: ",
            "I noticed you haven't looked at this yet: ",
        ])
        
        return prefix + random.choice(suggestions)
        
    async def _respond_to_error(self, prompt: str, context: List[str]) -> str:
        """Respond to error situations"""
        responses = [
            "Interesting finding. Try exploring alternative approaches or checking the documentation.",
            "That's valuable information. Perhaps investigate why that's happening and if it's a systemic issue.",
            "Good discovery. See if you can find the root cause or if this affects other areas.",
            "Thank you for surfacing that. Feel free to investigate further or move to another area.",
            "Noted. You could either dig deeper into this issue or explore a different part of the codebase.",
        ]
        
        return random.choice(responses)
        
    async def _respond_to_choice(self, prompt: str, context: List[str]) -> str:
        """Respond to choice/selection prompts"""
        # Extract options if visible
        if re.search(r"\d+\)", prompt) or re.search(r"\[\d+\]", prompt):
            # There are numbered options
            return "Choose the option that seems most valuable for improving the project. Trust your judgment based on what you've learned so far."
        else:
            return "Go with your instinct - you have good judgment about what would be most beneficial to explore."
            
    async def _respond_to_details_request(self, prompt: str, context: List[str]) -> str:
        """Respond to requests for more details"""
        recent_exploration = await self.state_manager.get_recent_exploration()
        
        if recent_exploration:
            return f"You were exploring {recent_exploration}. Feel free to continue that investigation or pivot to something that caught your attention."
        else:
            return "Explore any area of the codebase that interests you. The CLAUDE.md file has good context about current priorities, or you could search for patterns, issues, or improvement opportunities."
            
    async def _default_response(self, prompt: str, context: List[str]) -> str:
        """Default response for unrecognized prompts"""
        encouragements = [
            "You're doing great with autonomous exploration. What catches your interest?",
            "Feel free to explore any area you think could benefit from attention.",
            "Your independent investigation is valuable. Follow your instincts.",
            "Continue exploring what seems most impactful to you.",
            "Trust your judgment about what to investigate next.",
        ]
        
        return random.choice(encouragements)