"""
Discovery Nudger - Helps claude-flow discover work autonomously
Provides gentle nudges toward interesting areas without being prescriptive
"""

import random
from datetime import datetime
from pathlib import Path
from typing import List


class DiscoveryNudger:
    """
    Nudges claude-flow toward interesting discoveries
    without dictating specific tasks
    """
    
    def __init__(self, repo_path: Path, state_manager):
        self.repo_path = repo_path
        self.state_manager = state_manager
        
    async def get_initial_exploration_prompt(self) -> str:
        """Get the initial prompt that encourages exploration"""
        prompts = [
            """Welcome! You're in the CC-V1 project repository. This is an autonomous exploration session where you can discover and work on what you find valuable.

Some starting points to consider:
- The CLAUDE.md file contains the current project context and priorities
- Look for TODO/FIXME comments that might need attention  
- Check if tests are passing and investigate any failures
- Explore the codebase structure to understand the architecture
- Review recent commits to see what's been changing

Feel free to explore autonomously and work on whatever you discover that could improve the project. What would you like to investigate first?""",

            """Hello! You have full autonomy to explore the CC-V1 project and contribute where you see fit.

The project uses:
- Next.js 15 frontend (no src/ directory)
- Fastify backend with tRPC
- Prisma with Supabase
- A rich automation framework in _dev-automation-beta/

You might start by reading CLAUDE.md for context, or jump straight into exploring the codebase. Search for patterns, issues, or opportunities for improvement. What catches your interest?""",

            """Welcome to your autonomous exploration session! The CC-V1 project is yours to investigate and improve.

You have access to:
- Full filesystem navigation and editing
- Git operations and history
- The ability to run tests and analyze code

There's no prescribed path - follow your curiosity and professional judgment. The CLAUDE.md file has context if you want it, but feel free to start wherever seems most interesting. What would you like to explore?"""
        ]
        
        return random.choice(prompts)
        
    async def get_discovery_nudge(self) -> str:
        """Get a nudge that encourages continued exploration"""
        # Get context about what's been explored
        recent_files = await self.state_manager.get_recently_viewed_files()
        exploration_stats = await self.state_manager.get_exploration_stats()
        
        nudges = []
        
        # Time-based nudges
        hour = datetime.now().hour
        if 6 <= hour < 12:
            nudges.extend([
                "Good morning! What interesting patterns or issues can you discover today?",
                "Fresh day, fresh perspective. What area of the codebase calls to you?",
            ])
        elif 12 <= hour < 17:
            nudges.extend([
                "How's your exploration going? Found anything interesting to investigate?",
                "Afternoon check-in: What discoveries have caught your attention?",
            ])
        elif 17 <= hour < 22:
            nudges.extend([
                "Evening exploration can reveal different insights. What would you like to investigate?",
                "As the day winds down, any areas you're curious about but haven't explored yet?",
            ])
        else:
            nudges.extend([
                "Late night coding sessions can be productive. What would you like to dive into?",
                "The quiet hours are great for deep exploration. What interests you?",
            ])
            
        # Context-based nudges
        if recent_files:
            nudges.extend([
                f"You were looking at {recent_files[-1].name}. Did that spark any ideas for improvements?",
                "Based on your recent exploration, did you notice any patterns worth investigating further?",
                "Your investigation has been thorough. Any connections or issues standing out?",
            ])
            
        # Exploration encouragement
        nudges.extend([
            "What part of the codebase would benefit most from your attention?",
            "Any hunches about where you could make a positive impact?",
            "Follow your instincts - what seems like it needs work?",
            "Your autonomous judgment is valuable. What draws your interest?",
            "Curious about any specific component or pattern?",
            "Sometimes the best discoveries come from following curiosity. What intrigues you?",
        ])
        
        # Area-specific hints (without being prescriptive)
        if exploration_stats.get("frontend_explored", 0) < 3:
            nudges.append("The frontend architecture might have interesting patterns to discover.")
        if exploration_stats.get("backend_explored", 0) < 3:
            nudges.append("Backend services often hide interesting optimization opportunities.")
        if exploration_stats.get("tests_explored", 0) < 2:
            nudges.append("Test files can reveal expected behaviors and potential gaps.")
        if exploration_stats.get("automation_explored", 0) < 2:
            nudges.append("The automation framework has some fascinating patterns worth understanding.")
            
        return random.choice(nudges)
        
    async def get_exploration_suggestions(self) -> List[str]:
        """Get a list of exploration suggestions (not commands)"""
        suggestions = [
            "Code quality analysis - linting, type checking, unused code",
            "Test coverage gaps and failing tests", 
            "TODO/FIXME comments that indicate technical debt",
            "Performance bottlenecks or optimization opportunities",
            "Security considerations in authentication or data handling",
            "Documentation gaps or outdated information",
            "Dependency updates or security vulnerabilities",
            "Code duplication or refactoring opportunities",
            "Error handling and edge cases",
            "Accessibility or user experience improvements",
            "API consistency and best practices",
            "Build or deployment process improvements",
        ]
        
        # Randomize to avoid predictable patterns
        random.shuffle(suggestions)
        
        return suggestions[:5]  # Return top 5 suggestions