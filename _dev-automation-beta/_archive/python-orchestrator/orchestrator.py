#!/usr/bin/env python3
"""
Top-Level Orchestrator for Claude-Flow
Helps claude-flow discover objectives autonomously from project documentation
Acts as a gentle guide, not a task master
"""

import asyncio
import json
import os
import re
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from top_level_orchestrator.prompt_responder import PromptResponder
from top_level_orchestrator.discovery_nudger import DiscoveryNudger
from top_level_orchestrator.state_manager import StateManager
from top_level_orchestrator.overnight_guardian import OvernightGuardian


class ClaudeFlowOrchestrator:
    """
    Orchestrates claude-flow by helping it discover objectives
    Rather than feeding tasks, it nudges claude-flow to explore
    """
    
    def __init__(self, repo_path: Path):
        self.repo_path = repo_path
        self.claude_flow_process = None
        self.state_manager = StateManager(repo_path)
        self.prompt_responder = PromptResponder(repo_path, self.state_manager)
        self.discovery_nudger = DiscoveryNudger(repo_path, self.state_manager)
        self.overnight_guardian = OvernightGuardian()
        
        # Process monitoring
        self.last_activity = time.time()
        self.conversation_buffer = []
        self.waiting_for_response = False
        
    async def start(self):
        """Start the orchestrator"""
        print("🚀 Starting Claude-Flow Orchestrator")
        print("📚 Role: Help claude-flow discover objectives autonomously")
        
        # Initialize state
        await self.state_manager.initialize()
        
        # Start claude-flow process
        await self._start_claude_flow()
        
        # Start monitoring tasks
        asyncio.create_task(self._monitor_output())
        asyncio.create_task(self._activity_monitor())
        asyncio.create_task(self._overnight_monitor())
        
        print("✅ Orchestrator started - claude-flow is now exploring autonomously")
        
    async def _start_claude_flow(self):
        """Start claude-flow process"""
        try:
            # Launch claude-flow with MCP tools for exploration
            cmd = [
                "claude-flow", "chat",
                "--mcp", "filesystem",
                "--mcp", "git", 
                "--mcp", "github",
                "--no-stream"
            ]
            
            self.claude_flow_process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                cwd=self.repo_path
            )
            
            # Send initial greeting that encourages exploration
            initial_prompt = await self.discovery_nudger.get_initial_exploration_prompt()
            await self._send_to_claude_flow(initial_prompt)
            
            print("🤖 Claude-flow started with exploration prompt")
            
        except Exception as e:
            print(f"❌ Failed to start claude-flow: {e}")
            raise
            
    async def _send_to_claude_flow(self, message: str):
        """Send message to claude-flow"""
        if not self.claude_flow_process or self.claude_flow_process.poll() is not None:
            print("⚠️ Claude-flow not running, restarting...")
            await self._restart_claude_flow()
            
        try:
            self.claude_flow_process.stdin.write(message + "\n")
            self.claude_flow_process.stdin.flush()
            self.last_activity = time.time()
            self.waiting_for_response = True
            
            # Log interaction
            await self.state_manager.log_interaction("orchestrator", message)
            
        except Exception as e:
            print(f"❌ Error sending to claude-flow: {e}")
            
    async def _monitor_output(self):
        """Monitor claude-flow output and respond intelligently"""
        while True:
            try:
                if self.claude_flow_process and self.claude_flow_process.poll() is None:
                    # Read output line by line
                    line = self.claude_flow_process.stdout.readline()
                    
                    if line:
                        self.last_activity = time.time()
                        self.conversation_buffer.append(line.strip())
                        
                        # Check if claude-flow is asking for input
                        if await self._is_waiting_for_input(line):
                            # Generate intelligent response
                            response = await self.prompt_responder.generate_response(
                                line,
                                self.conversation_buffer[-10:]  # Last 10 lines of context
                            )
                            
                            if response:
                                await asyncio.sleep(2)  # Natural pause
                                await self._send_to_claude_flow(response)
                                
                await asyncio.sleep(0.1)
                
            except Exception as e:
                print(f"❌ Monitor error: {e}")
                await asyncio.sleep(1)
                
    async def _is_waiting_for_input(self, line: str) -> bool:
        """Detect if claude-flow is waiting for input"""
        # Common prompts that indicate waiting
        prompt_patterns = [
            r"continue\?",
            r"what.*next\?",
            r"what would you like",
            r"how can i help",
            r"what should i",
            r"would you like me to",
            r"shall i",
            r">>",  # Direct prompt
            r"human:",  # Conversation prompt
        ]
        
        line_lower = line.lower()
        return any(re.search(pattern, line_lower) for pattern in prompt_patterns)
        
    async def _activity_monitor(self):
        """Monitor activity and nudge when idle"""
        while True:
            try:
                idle_time = time.time() - self.last_activity
                
                # If idle for too long, send a discovery nudge
                if idle_time > 300:  # 5 minutes
                    nudge = await self.discovery_nudger.get_discovery_nudge()
                    await self._send_to_claude_flow(nudge)
                    
                # Check if claude-flow is still alive
                if self.claude_flow_process and self.claude_flow_process.poll() is not None:
                    print("💀 Claude-flow died, restarting...")
                    await self._restart_claude_flow()
                    
                await asyncio.sleep(30)
                
            except Exception as e:
                print(f"❌ Activity monitor error: {e}")
                await asyncio.sleep(30)
                
    async def _overnight_monitor(self):
        """Special overnight monitoring"""
        while True:
            try:
                if self.overnight_guardian.is_overnight():
                    # More aggressive nudging overnight
                    idle_time = time.time() - self.last_activity
                    
                    if idle_time > 120:  # 2 minutes overnight
                        nudge = await self.overnight_guardian.get_overnight_nudge(
                            self.state_manager
                        )
                        await self._send_to_claude_flow(nudge)
                        
                await asyncio.sleep(60)
                
            except Exception as e:
                print(f"❌ Overnight monitor error: {e}")
                await asyncio.sleep(60)
                
    async def _restart_claude_flow(self):
        """Restart claude-flow process"""
        # Terminate old process
        if self.claude_flow_process:
            self.claude_flow_process.terminate()
            await asyncio.sleep(1)
            
        # Start new process
        await self._start_claude_flow()
        
        # Send context about what was being worked on
        context = await self.state_manager.get_restart_context()
        if context:
            await self._send_to_claude_flow(context)
            

async def main():
    """Main entry point"""
    repo_path = Path("/home/<USER>/github/cc-v1")
    
    orchestrator = ClaudeFlowOrchestrator(repo_path)
    
    try:
        await orchestrator.start()
        
        # Keep running
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Orchestrator stopped by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        

if __name__ == "__main__":
    asyncio.run(main())