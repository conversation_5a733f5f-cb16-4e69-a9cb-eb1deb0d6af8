#!/bin/bash
# Complete Overnight Automation System
# Simple tmux + claude-flow + one claude instance for responses
# No rocket science - just works

set -e

REPO_ROOT="/home/<USER>/github/cc-v1"
AUTOMATION_DIR="$REPO_ROOT/_dev-automation-beta"
TMUX_SESSION="overnight-automation"
LOG_DIR="$AUTOMATION_DIR/logs/overnight_$(date +%Y%m%d_%H%M%S)"

# Create directories
mkdir -p "$LOG_DIR"
mkdir -p "$AUTOMATION_DIR/prompts"

echo "🌙 Starting Overnight Automation System"
echo "======================================="
echo "📁 Logs: $LOG_DIR"
echo "🎯 Goal: Keep claude-flow swarm running overnight with autonomous responses"
echo ""

# Kill any existing session
tmux kill-session -t $TMUX_SESSION 2>/dev/null || true

# Create nightly branch
cd "$REPO_ROOT"
BRANCH_NAME="night_$(date +%Y-%m-%d)"
echo "🔀 Creating nightly branch: $BRANCH_NAME"

if git show-ref --verify --quiet refs/heads/$BRANCH_NAME; then
    git checkout $BRANCH_NAME
else
    git checkout -b $BRANCH_NAME
fi

# Create tmux session with 3 panes
echo "📺 Creating tmux session: $TMUX_SESSION"
tmux new-session -d -s $TMUX_SESSION -n "automation"

# Split into 3 panes:
# +------------------------+
# |     claude-flow        |
# |      (pane 0)          |
# +------------------------+
# |  responder  | monitor  |
# | (pane 1)    |(pane 2)  |
# +------------------------+

tmux split-window -v -t $TMUX_SESSION:0
tmux split-window -h -t $TMUX_SESSION:0.1

# Pane 0: claude-flow swarm (main worker)
echo "🤖 Starting claude-flow swarm..."

# Create initial prompt to start claude-flow on CC-V1
cat > "$AUTOMATION_DIR/prompts/overnight-exploration.txt" << 'EOF'
Work on CC-V1 call center project development. Read CLAUDE.md for current priorities and start working.
EOF

tmux send-keys -t $TMUX_SESSION:0.0 "cd $REPO_ROOT" Enter
tmux send-keys -t $TMUX_SESSION:0.0 "echo '🌙 Starting overnight claude-flow exploration...'" Enter
tmux send-keys -t $TMUX_SESSION:0.0 "claude-flow swarm 'Overnight autonomous exploration and improvement of CC-V1 project' --claude 2>&1 | tee $LOG_DIR/claude-flow.log" Enter

# Give claude-flow time to start
sleep 5

# Pane 1: Responder claude (keeps the swarm fed)
echo "💬 Starting responder claude..."

cat > "$AUTOMATION_DIR/prompts/overnight-responder.txt" << 'EOF'
🤖 CC-V1 Development Orchestrator - Context-Aware Nudger

MISSION: Keep claude-flow actively developing CC-V1 by providing context-rich nudges based on project policies.

NUDGING STRATEGY (rotate and adapt based on progress):

1. "Read CLAUDE.md for current CC-V1 priorities. Focus on fixing the 655+ TypeScript compilation errors blocking the build. Start with: npm run build"

2. "Check AGENTS.md architecture rules, then work on connecting live-demo components to main operator interface as specified in current priorities."

3. "Implement missing tRPC endpoints for analytics, assistant audio, and health diagnostics. Follow the Prisma-first architecture pattern."

4. "Update operator dashboard routing for demo features. Ensure Universal Template pattern consistency across all 13 features."

5. "Run tests and fix failures: npm test. Focus on Jest/Cypress type conflicts mentioned in TODO comments."

6. "Review TODO/FIXME comments in backend/src/trpc/ routers and implement the missing database integrations."

7. "Check deployment readiness: ./deploy.sh --check. Fix any deployment blockers found."

8. "Standardize Piano, Quality Control, and Account features to match Universal Template pattern (FEI/FEIM)."

9. "Implement Assistant feature: text, TTS/STT, and live AI chatbot using Flash 2.5 model and OpenAI API."

10. "Review recent commits and continue work on incomplete features. Verify all changes with build tests."

CONTEXT INJECTION: Always reference CLAUDE.md, AGENTS.md, and current build status. Provide specific file paths and actionable next steps.

Wait for claude-flow to ask for direction, then provide a context-rich nudge with CC-V1 specifics.
EOF

tmux send-keys -t $TMUX_SESSION:0.1 "cd $AUTOMATION_DIR" Enter
tmux send-keys -t $TMUX_SESSION:0.1 "claude chat < prompts/overnight-responder.txt 2>&1 | tee $LOG_DIR/responder.log" Enter

# Pane 2: Monitor and auto-nudger
echo "📊 Starting monitor and auto-nudger..."

cat > "$AUTOMATION_DIR/overnight-monitor.sh" << 'EOF'
#!/bin/bash
# Overnight monitor and auto-nudger

LOG_DIR="$1"
REPO_ROOT="/home/<USER>/github/cc-v1"

echo "🌙 Overnight Automation Monitor"
echo "==============================="
echo "Monitoring claude-flow and providing auto-nudges..."
echo ""

nudge_count=0
last_nudge=0

while true; do
    current_time=$(date +%s)
    
    # Clear screen and show status
    clear
    echo "🌙 Overnight Automation Monitor - $(date)"
    echo "=========================================="
    echo ""
    
    # Check if claude-flow is running
    if pgrep -f "claude-flow" > /dev/null; then
        echo "✅ claude-flow: ACTIVE"
    else
        echo "❌ claude-flow: NOT RUNNING - RESTARTING..."
        tmux send-keys -t overnight-automation:0.0 C-c Enter
        sleep 2
        tmux send-keys -t overnight-automation:0.0 "claude-flow swarm 'Resume overnight exploration' --claude 2>&1 | tee -a $LOG_DIR/claude-flow.log" Enter
    fi
    
    # Check responder
    if pgrep -f "claude chat.*responder" > /dev/null; then
        echo "✅ responder: READY"
    else
        echo "❌ responder: NOT RUNNING - RESTARTING..."
        tmux send-keys -t overnight-automation:0.1 C-c Enter
        sleep 1
        tmux send-keys -t overnight-automation:0.1 "claude chat < prompts/overnight-responder.txt 2>&1 | tee -a $LOG_DIR/responder.log" Enter
    fi
    
    echo ""
    echo "📊 Session Stats:"
    echo "- Nudges sent: $nudge_count"
    echo "- Last nudge: $(date -d @$last_nudge 2>/dev/null || echo 'Never')"
    echo "- Uptime: $(( (current_time - $(stat -c %Y $LOG_DIR)) / 60 )) minutes"
    
    echo ""
    echo "📝 Recent Activity:"
    echo "-------------------"
    if [ -f "$LOG_DIR/claude-flow.log" ]; then
        tail -5 "$LOG_DIR/claude-flow.log" | grep -E "(Exploring|Found|TODO|Error|Complete)" | tail -3 || echo "Waiting for activity..."
    fi
    
    # Auto-nudge every 5 minutes to keep claude-flow active
    if [ $((current_time - last_nudge)) -gt 300 ]; then
        echo ""
        echo "⏰ Nudging claude-flow to stay active..."

        # Simple nudge to keep claude-flow working
        tmux send-keys -t overnight-automation:0.0 "" Enter
        tmux send-keys -t overnight-automation:0.0 "Continue working" Enter

        nudge_count=$((nudge_count + 1))
        last_nudge=$current_time
    fi
    
    echo ""
    echo "🔄 Next check in 30 seconds..."
    echo "Press Ctrl+C to stop monitoring"
    
    sleep 30
done
EOF

chmod +x "$AUTOMATION_DIR/overnight-monitor.sh"
tmux send-keys -t $TMUX_SESSION:0.2 "$AUTOMATION_DIR/overnight-monitor.sh $LOG_DIR" Enter

# Create a simple stop script
cat > "$AUTOMATION_DIR/stop-overnight.sh" << 'EOF'
#!/bin/bash
echo "🛑 Stopping overnight automation..."
tmux kill-session -t overnight-automation 2>/dev/null || echo "Session not running"
echo "✅ Stopped"
EOF

chmod +x "$AUTOMATION_DIR/stop-overnight.sh"

# Wait a moment for everything to initialize
sleep 3

echo ""
echo "✅ Overnight Automation System Started!"
echo "======================================="
echo ""
echo "📺 View session: tmux attach -t $TMUX_SESSION"
echo "📁 Logs: $LOG_DIR"
echo "🛑 Stop: $AUTOMATION_DIR/stop-overnight.sh"
echo ""
echo "Layout:"
echo "+------------------------+"
echo "|     claude-flow        |"
echo "|    (autonomous)        |"
echo "+------------------------+"
echo "|  responder  | monitor  |"
echo "| (auto-feed) |(status)  |"
echo "+------------------------+"
echo ""
echo "The system will:"
echo "- Run claude-flow swarm autonomously"
echo "- Auto-respond to keep it active"
echo "- Monitor and restart if needed"
echo "- Work continuously overnight"
echo ""
echo "🌙 Sweet dreams! The automation is working..."

# Optionally attach to the session
if [ "$1" = "--attach" ]; then
    tmux attach -t $TMUX_SESSION
fi
