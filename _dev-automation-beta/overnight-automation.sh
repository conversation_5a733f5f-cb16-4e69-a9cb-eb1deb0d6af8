#!/bin/bash
# Complete Overnight Automation System
# Simple tmux + claude-flow + one claude instance for responses
# No rocket science - just works

set -e

REPO_ROOT="/home/<USER>/github/cc-v1"
AUTOMATION_DIR="$REPO_ROOT/_dev-automation-beta"
TMUX_SESSION="automated-user"
LOG_DIR="$AUTOMATION_DIR/logs/overnight_$(date +%Y%m%d_%H%M%S)"

# Create directories
mkdir -p "$LOG_DIR"
mkdir -p "$AUTOMATION_DIR/prompts"

echo "🤖 Starting Automated User System"
echo "=================================="
echo "📁 Logs: $LOG_DIR"
echo "🎯 Goal: Act as automated user to keep claude-flow working on CC-V1"
echo ""

# Kill any existing session
tmux kill-session -t $TMUX_SESSION 2>/dev/null || true

# Create nightly branch
cd "$REPO_ROOT"
BRANCH_NAME="night_$(date +%Y-%m-%d)"
echo "🔀 Creating nightly branch: $BRANCH_NAME"

if git show-ref --verify --quiet refs/heads/$BRANCH_NAME; then
    git checkout $BRANCH_NAME
else
    git checkout -b $BRANCH_NAME
fi

# Create tmux session with 3 panes
echo "📺 Creating automated user session: $TMUX_SESSION"
tmux new-session -d -s $TMUX_SESSION -n "automation"

# Split into 3 panes:
# +------------------------+
# |     claude-flow        |
# |      (pane 0)          |
# +------------------------+
# |  responder  | monitor  |
# | (pane 1)    |(pane 2)  |
# +------------------------+

tmux split-window -v -t $TMUX_SESSION:0
tmux split-window -h -t $TMUX_SESSION:0.1

# Pane 0: claude-flow swarm (main worker)
echo "🤖 Starting claude-flow swarm..."

# Create initial prompt to start claude-flow on CC-V1
cat > "$AUTOMATION_DIR/prompts/overnight-exploration.txt" << 'EOF'
Work on CC-V1 call center project development. Read CLAUDE.md for current priorities and start working.
EOF

tmux send-keys -t $TMUX_SESSION:0.0 "cd $REPO_ROOT" Enter
tmux send-keys -t $TMUX_SESSION:0.0 "echo '🌙 Starting claude-flow for CC-V1 development...'" Enter
tmux send-keys -t $TMUX_SESSION:0.0 "claude-flow swarm 'Work on CC-V1 call center project development' --claude 2>&1 | tee $LOG_DIR/claude-flow.log" Enter

# Give claude-flow time to start
sleep 5

# Pane 1: Auto-responder claude (responds to claude-flow prompts)
echo "💬 Starting auto-responder claude..."

# Create auto-responder script that monitors and responds
cat > "$AUTOMATION_DIR/auto-responder.sh" << 'EOF'
#!/bin/bash
# Auto-responder for claude-flow prompts

LOG_DIR="$1"
REPO_ROOT="/home/<USER>/github/cc-v1"

echo "🤖 Auto-responder monitoring claude-flow..."

# Responses to rotate through
responses=(
    "Continue working on CC-V1 development"
    "Check CLAUDE.md and keep developing"
    "Work on the next priority task"
    "Keep building CC-V1"
    "Continue development work"
)

response_index=0

while true; do
    # Check if claude-flow is asking for input (look for common prompts)
    if tail -10 "$LOG_DIR/claude-flow.log" 2>/dev/null | grep -q -E "(What would you like|What should I|How can I help|What's next|waiting for input)"; then
        echo "📝 Detected claude-flow waiting for input, responding..."

        # Send response to claude-flow
        response="${responses[$response_index]}"
        tmux send-keys -t automated-user:0.0 "$response" Enter

        # Rotate to next response
        response_index=$(( (response_index + 1) % ${#responses[@]} ))

        echo "✅ Sent: $response"
        sleep 30  # Wait before checking again
    fi

    sleep 10  # Check every 10 seconds
done
EOF

chmod +x "$AUTOMATION_DIR/auto-responder.sh"
tmux send-keys -t $TMUX_SESSION:0.1 "$AUTOMATION_DIR/auto-responder.sh $LOG_DIR 2>&1 | tee $LOG_DIR/responder.log" Enter

# Pane 2: Monitor and auto-nudger
echo "📊 Starting monitor and auto-nudger..."

cat > "$AUTOMATION_DIR/overnight-monitor.sh" << 'EOF'
#!/bin/bash
# Overnight monitor and auto-nudger

LOG_DIR="$1"
REPO_ROOT="/home/<USER>/github/cc-v1"

echo "🌙 Overnight Automation Monitor"
echo "==============================="
echo "Monitoring claude-flow and providing auto-nudges..."
echo ""

nudge_count=0
last_nudge=0

while true; do
    current_time=$(date +%s)
    
    # Clear screen and show status
    clear
    echo "🌙 Overnight Automation Monitor - $(date)"
    echo "=========================================="
    echo ""
    
    # Check if claude-flow is running
    if pgrep -f "claude-flow" > /dev/null; then
        echo "✅ claude-flow: ACTIVE"
    else
        echo "❌ claude-flow: NOT RUNNING - RESTARTING..."
        tmux send-keys -t automated-user:0.0 C-c Enter
        sleep 2
        tmux send-keys -t automated-user:0.0 "claude-flow swarm 'Resume CC-V1 development' --claude 2>&1 | tee -a $LOG_DIR/claude-flow.log" Enter
    fi
    
    # Check responder
    if pgrep -f "claude chat.*responder" > /dev/null; then
        echo "✅ responder: READY"
    else
        echo "❌ responder: NOT RUNNING - RESTARTING..."
        tmux send-keys -t automated-user:0.1 C-c Enter
        sleep 1
        tmux send-keys -t automated-user:0.1 "claude chat < prompts/automated-user-responder.txt 2>&1 | tee -a $LOG_DIR/responder.log" Enter
    fi
    
    echo ""
    echo "📊 Session Stats:"
    echo "- Nudges sent: $nudge_count"
    echo "- Last nudge: $(date -d @$last_nudge 2>/dev/null || echo 'Never')"
    echo "- Uptime: $(( (current_time - $(stat -c %Y $LOG_DIR)) / 60 )) minutes"
    
    echo ""
    echo "📝 Recent Activity:"
    echo "-------------------"
    if [ -f "$LOG_DIR/claude-flow.log" ]; then
        tail -5 "$LOG_DIR/claude-flow.log" | grep -E "(Exploring|Found|TODO|Error|Complete)" | tail -3 || echo "Waiting for activity..."
    fi
    
    # Auto-nudge every 5 minutes to keep claude-flow active
    if [ $((current_time - last_nudge)) -gt 300 ]; then
        echo ""
        echo "⏰ Nudging claude-flow to stay active..."

        # Automated user nudge to keep claude-flow working
        tmux send-keys -t automated-user:0.0 "" Enter
        tmux send-keys -t automated-user:0.0 "Continue working" Enter

        nudge_count=$((nudge_count + 1))
        last_nudge=$current_time
    fi
    
    echo ""
    echo "🔄 Next check in 30 seconds..."
    echo "Press Ctrl+C to stop monitoring"
    
    sleep 30
done
EOF

chmod +x "$AUTOMATION_DIR/overnight-monitor.sh"
tmux send-keys -t $TMUX_SESSION:0.2 "$AUTOMATION_DIR/overnight-monitor.sh $LOG_DIR" Enter

# Create a simple stop script
cat > "$AUTOMATION_DIR/stop-overnight.sh" << 'EOF'
#!/bin/bash
echo "🛑 Stopping automated user system..."
tmux kill-session -t automated-user 2>/dev/null || echo "Session not running"
echo "✅ Stopped"
EOF

chmod +x "$AUTOMATION_DIR/stop-overnight.sh"

# Wait a moment for everything to initialize
sleep 3

echo ""
echo "✅ Automated User System Started!"
echo "================================="
echo ""
echo "📺 View session: tmux attach -t $TMUX_SESSION"
echo "📁 Logs: $LOG_DIR"
echo "🛑 Stop: $AUTOMATION_DIR/stop-overnight.sh"
echo ""
echo "Layout:"
echo "+------------------------+"
echo "|     claude-flow        |"
echo "|    (development)       |"
echo "+------------------------+"
echo "|automated-user| monitor |"
echo "| (responses)  |(status) |"
echo "+------------------------+"
echo ""
echo "The automated user will:"
echo "- Act as user for claude-flow"
echo "- Keep claude-flow active on CC-V1"
echo "- Monitor and restart if needed"
echo "- Provide responses to prompts"
echo ""
echo "🤖 Automated user is now active!"

# Optionally attach to the session
if [ "$1" = "--attach" ]; then
    tmux attach -t $TMUX_SESSION
fi
