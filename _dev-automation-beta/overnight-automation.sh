#!/bin/bash
# Complete Overnight Automation System
# Simple tmux + claude-flow + one claude instance for responses
# No rocket science - just works

set -e

REPO_ROOT="/home/<USER>/github/cc-v1"
AUTOMATION_DIR="$REPO_ROOT/_dev-automation-beta"
TMUX_SESSION="overnight-automation"
LOG_DIR="$AUTOMATION_DIR/logs/overnight_$(date +%Y%m%d_%H%M%S)"

# Create directories
mkdir -p "$LOG_DIR"
mkdir -p "$AUTOMATION_DIR/prompts"

echo "🌙 Starting Overnight Automation System"
echo "======================================="
echo "📁 Logs: $LOG_DIR"
echo "🎯 Goal: Keep claude-flow swarm running overnight with autonomous responses"
echo ""

# Kill any existing session
tmux kill-session -t $TMUX_SESSION 2>/dev/null || true

# Create nightly branch
cd "$REPO_ROOT"
BRANCH_NAME="night_$(date +%Y-%m-%d)"
echo "🔀 Creating nightly branch: $BRANCH_NAME"

if git show-ref --verify --quiet refs/heads/$BRANCH_NAME; then
    git checkout $BRANCH_NAME
else
    git checkout -b $BRANCH_NAME
fi

# Create tmux session with 3 panes
echo "📺 Creating tmux session: $TMUX_SESSION"
tmux new-session -d -s $TMUX_SESSION -n "automation"

# Split into 3 panes:
# +------------------------+
# |     claude-flow        |
# |      (pane 0)          |
# +------------------------+
# |  responder  | monitor  |
# | (pane 1)    |(pane 2)  |
# +------------------------+

tmux split-window -v -t $TMUX_SESSION:0
tmux split-window -h -t $TMUX_SESSION:0.1

# Pane 0: claude-flow swarm (main worker)
echo "🤖 Starting claude-flow swarm..."

# Create initial exploration prompt
cat > "$AUTOMATION_DIR/prompts/overnight-exploration.txt" << 'EOF'
Welcome to overnight autonomous exploration of the CC-V1 project!

You have complete freedom to explore and improve the codebase. Focus on valuable work that can run unattended.

Priority areas (check CLAUDE.md for current context):
- Fix TypeScript compilation errors
- Resolve TODO/FIXME comments
- Run and fix failing tests
- Improve code quality and documentation
- Optimize performance bottlenecks
- Update dependencies safely
- Clean up console.log statements
- Add missing error handling

You have access to filesystem, git, and github MCPs. Work autonomously and follow your professional judgment.

Start by reading CLAUDE.md for current project priorities, then explore what interests you most.
EOF

tmux send-keys -t $TMUX_SESSION:0.0 "cd $REPO_ROOT" Enter
tmux send-keys -t $TMUX_SESSION:0.0 "echo '🌙 Starting overnight claude-flow exploration...'" Enter
tmux send-keys -t $TMUX_SESSION:0.0 "claude-flow swarm 'Overnight autonomous exploration and improvement of CC-V1 project' --claude 2>&1 | tee $LOG_DIR/claude-flow.log" Enter

# Give claude-flow time to start
sleep 5

# Pane 1: Responder claude (keeps the swarm fed)
echo "💬 Starting responder claude..."

cat > "$AUTOMATION_DIR/prompts/overnight-responder.txt" << 'EOF'
You are the Overnight Responder for a claude-flow swarm automation system.

Your job is simple: When the claude-flow swarm asks "What would you like me to do?" or similar questions, provide exploration suggestions that keep it working autonomously overnight.

RESPONSE PATTERNS (rotate through these):

1. "Explore the CLAUDE.md file for current priorities, then search for TODO and FIXME comments throughout the codebase and work on the most important ones."

2. "Run 'npm test' or 'npm run test' to check for failing tests, then investigate and fix any failures you find."

3. "Search for TypeScript compilation errors with 'npm run typecheck' and fix them systematically."

4. "Look for console.log statements that should be removed from production code and clean them up."

5. "Check for security vulnerabilities with 'npm audit' and update dependencies safely."

6. "Find files over 500 lines that could benefit from refactoring and improve their structure."

7. "Search for missing error handling patterns and add proper error messages."

8. "Review recent git commits and identify any follow-up work that needs to be done."

9. "Explore the _dev-automation-beta directory and suggest improvements to the automation system."

10. "Look for hardcoded values that should be moved to configuration files."

Keep responses concise and actionable. The goal is continuous autonomous work overnight.

Wait for the swarm to ask for direction, then provide one of these exploration tasks.
EOF

tmux send-keys -t $TMUX_SESSION:0.1 "cd $AUTOMATION_DIR" Enter
tmux send-keys -t $TMUX_SESSION:0.1 "claude chat < prompts/overnight-responder.txt 2>&1 | tee $LOG_DIR/responder.log" Enter

# Pane 2: Monitor and auto-nudger
echo "📊 Starting monitor and auto-nudger..."

cat > "$AUTOMATION_DIR/overnight-monitor.sh" << 'EOF'
#!/bin/bash
# Overnight monitor and auto-nudger

LOG_DIR="$1"
REPO_ROOT="/home/<USER>/github/cc-v1"

echo "🌙 Overnight Automation Monitor"
echo "==============================="
echo "Monitoring claude-flow and providing auto-nudges..."
echo ""

nudge_count=0
last_nudge=0

while true; do
    current_time=$(date +%s)
    
    # Clear screen and show status
    clear
    echo "🌙 Overnight Automation Monitor - $(date)"
    echo "=========================================="
    echo ""
    
    # Check if claude-flow is running
    if pgrep -f "claude-flow" > /dev/null; then
        echo "✅ claude-flow: ACTIVE"
    else
        echo "❌ claude-flow: NOT RUNNING - RESTARTING..."
        tmux send-keys -t overnight-automation:0.0 C-c Enter
        sleep 2
        tmux send-keys -t overnight-automation:0.0 "claude-flow swarm 'Resume overnight exploration' --claude 2>&1 | tee -a $LOG_DIR/claude-flow.log" Enter
    fi
    
    # Check responder
    if pgrep -f "claude chat.*responder" > /dev/null; then
        echo "✅ responder: READY"
    else
        echo "❌ responder: NOT RUNNING - RESTARTING..."
        tmux send-keys -t overnight-automation:0.1 C-c Enter
        sleep 1
        tmux send-keys -t overnight-automation:0.1 "claude chat < prompts/overnight-responder.txt 2>&1 | tee -a $LOG_DIR/responder.log" Enter
    fi
    
    echo ""
    echo "📊 Session Stats:"
    echo "- Nudges sent: $nudge_count"
    echo "- Last nudge: $(date -d @$last_nudge 2>/dev/null || echo 'Never')"
    echo "- Uptime: $(( (current_time - $(stat -c %Y $LOG_DIR)) / 60 )) minutes"
    
    echo ""
    echo "📝 Recent Activity:"
    echo "-------------------"
    if [ -f "$LOG_DIR/claude-flow.log" ]; then
        tail -5 "$LOG_DIR/claude-flow.log" | grep -E "(Exploring|Found|TODO|Error|Complete)" | tail -3 || echo "Waiting for activity..."
    fi
    
    # Auto-nudge every 10 minutes if no activity
    if [ $((current_time - last_nudge)) -gt 600 ]; then
        echo ""
        echo "⏰ Sending auto-nudge to keep exploration active..."
        
        # Send nudge to responder
        tmux send-keys -t overnight-automation:0.1 "" Enter
        tmux send-keys -t overnight-automation:0.1 "The swarm needs direction. Please provide an exploration task from your rotation." Enter
        
        nudge_count=$((nudge_count + 1))
        last_nudge=$current_time
    fi
    
    echo ""
    echo "🔄 Next check in 30 seconds..."
    echo "Press Ctrl+C to stop monitoring"
    
    sleep 30
done
EOF

chmod +x "$AUTOMATION_DIR/overnight-monitor.sh"
tmux send-keys -t $TMUX_SESSION:0.2 "$AUTOMATION_DIR/overnight-monitor.sh $LOG_DIR" Enter

# Create a simple stop script
cat > "$AUTOMATION_DIR/stop-overnight.sh" << 'EOF'
#!/bin/bash
echo "🛑 Stopping overnight automation..."
tmux kill-session -t overnight-automation 2>/dev/null || echo "Session not running"
echo "✅ Stopped"
EOF

chmod +x "$AUTOMATION_DIR/stop-overnight.sh"

# Wait a moment for everything to initialize
sleep 3

echo ""
echo "✅ Overnight Automation System Started!"
echo "======================================="
echo ""
echo "📺 View session: tmux attach -t $TMUX_SESSION"
echo "📁 Logs: $LOG_DIR"
echo "🛑 Stop: $AUTOMATION_DIR/stop-overnight.sh"
echo ""
echo "Layout:"
echo "+------------------------+"
echo "|     claude-flow        |"
echo "|    (autonomous)        |"
echo "+------------------------+"
echo "|  responder  | monitor  |"
echo "| (auto-feed) |(status)  |"
echo "+------------------------+"
echo ""
echo "The system will:"
echo "- Run claude-flow swarm autonomously"
echo "- Auto-respond to keep it active"
echo "- Monitor and restart if needed"
echo "- Work continuously overnight"
echo ""
echo "🌙 Sweet dreams! The automation is working..."

# Optionally attach to the session
if [ "$1" = "--attach" ]; then
    tmux attach -t $TMUX_SESSION
fi
