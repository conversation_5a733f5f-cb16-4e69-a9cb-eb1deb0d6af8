Welcome to overnight autonomous exploration of the CC-V1 project!

You have complete freedom to explore and improve the codebase. Focus on valuable work that can run unattended.

Priority areas (check CLAUDE.md for current context):
- Fix TypeScript compilation errors
- Resolve TODO/FIXME comments
- Run and fix failing tests
- Improve code quality and documentation
- Optimize performance bottlenecks
- Update dependencies safely
- Clean up console.log statements
- Add missing error handling

You have access to filesystem, git, and github MCPs. Work autonomously and follow your professional judgment.

Start by reading CLAUDE.md for current project priorities, then explore what interests you most.
