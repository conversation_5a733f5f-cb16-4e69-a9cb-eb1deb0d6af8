import { createTypedEventBus } from './event-bus.js';
import { AllEvents, EventName, EventPayload } from './event-types.js';
import { logger } from '../utils/logger.js';

export const typedEventBus = createTypedEventBus<AllEvents>();

export class EventDrivenComponent {
  protected componentName: string;
  protected subscriptions: Array<{ event: EventName; handler: Function }> = [];

  constructor(componentName: string) {
    this.componentName = componentName;
  }

  protected subscribe<T extends EventName>(
    event: T,
    handler: (payload: EventPayload<T>) => void | Promise<void>
  ): void {
    const wrappedHandler = async (payload: EventPayload<T>) => {
      try {
        await handler(payload);
      } catch (error) {
        logger.error(`${this.componentName}: Error handling ${event}`, { error: error instanceof Error ? error : new Error(String(error)) });
        this.emit('system:error', {
          component: this.componentName,
          error: error instanceof Error ? error : new Error(String(error))
        });
      }
    };

    typedEventBus.on(event, wrappedHandler);
    this.subscriptions.push({ event, handler: wrappedHandler });
  }

  protected emit<T extends EventName>(
    event: T,
    payload: EventPayload<T>
  ): void {
    typedEventBus.emit(event, payload);
  }

  protected async emitAsync<T extends EventName>(
    event: T,
    payload: EventPayload<T>
  ): Promise<void> {
    return new Promise((resolve) => {
      setImmediate(() => {
        typedEventBus.emit(event, payload);
        resolve();
      });
    });
  }

  cleanup(): void {
    this.subscriptions.forEach(({ event, handler }) => {
      typedEventBus.off(event, handler as any);
    });
    this.subscriptions = [];
  }
}

export interface EventPipeline<TInput, TOutput> {
  name: string;
  execute(input: TInput): Promise<TOutput>;
}

export class EventDrivenPipeline<TInput, TOutput> implements EventPipeline<TInput, TOutput> {
  constructor(
    public name: string,
    private stages: Array<(data: any) => Promise<any>>
  ) {}

  async execute(input: TInput): Promise<TOutput> {
    let result: any = input;
    
    for (let i = 0; i < this.stages.length; i++) {
      const stage = this.stages[i];
      const stageName = `${this.name}:stage:${i}`;
      
      typedEventBus.emit('system:pipeline:stage:start' as any, {
        pipeline: this.name,
        stage: i,
        input: result
      });

      try {
        result = await stage(result);
      } catch (error) {
        typedEventBus.emit('system:pipeline:stage:error' as any, {
          pipeline: this.name,
          stage: i,
          error
        });
        throw error;
      }

      typedEventBus.emit('system:pipeline:stage:complete' as any, {
        pipeline: this.name,
        stage: i,
        output: result
      });
    }

    return result as TOutput;
  }
}

export function createEventHandler<T extends EventName>(
  event: T,
  handler: (payload: EventPayload<T>) => void | Promise<void>
): void {
  typedEventBus.on(event, handler);
}

export function createEventFilter<T extends EventName>(
  event: T,
  filter: (payload: EventPayload<T>) => boolean,
  handler: (payload: EventPayload<T>) => void | Promise<void>
): void {
  typedEventBus.on(event, async (payload) => {
    if (filter(payload)) {
      await handler(payload);
    }
  });
}