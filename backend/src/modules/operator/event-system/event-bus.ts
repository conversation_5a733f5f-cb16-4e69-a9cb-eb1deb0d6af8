import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';

export interface TypedEventEmitter<TEvents extends Record<string, any>> {
  on<TEventName extends keyof TEvents>(
    event: TEventName,
    listener: (payload: TEvents[TEventName]) => void
  ): this;

  once<TEventName extends keyof TEvents>(
    event: TEventName,
    listener: (payload: TEvents[TEventName]) => void
  ): this;

  emit<TEventName extends keyof TEvents>(
    event: TEventName,
    payload: TEvents[TEventName]
  ): boolean;

  off<TEventName extends keyof TEvents>(
    event: TEventName,
    listener: (payload: TEvents[TEventName]) => void
  ): this;

  removeAllListeners<TEventName extends keyof TEvents>(event?: TEventName): this;
}

class EventBusImplementation extends EventEmitter {
  private static instance: EventBusImplementation;
  public eventHistory: Map<string, any[]> = new Map();
  public readonly maxHistorySize = 100;

  private constructor() {
    super();
    this.setMaxListeners(100);
  }

  static getInstance(): EventBusImplementation {
    if (!EventBusImplementation.instance) {
      EventBusImplementation.instance = new EventBusImplementation();
    }
    return EventBusImplementation.instance;
  }

  emitWithHistory(event: string, payload: any): boolean {
    if (!this.eventHistory.has(event)) {
      this.eventHistory.set(event, []);
    }
    
    const history = this.eventHistory.get(event)!;
    history.push({ timestamp: Date.now(), payload });
    
    if (history.length > this.maxHistorySize) {
      history.shift();
    }

    logger.debug(`EventBus: Emitting ${event}`, { 
      listeners: this.listenerCount(event),
      payload: JSON.stringify(payload).substring(0, 200)
    });

    return super.emit(event, payload);
  }

  getEventHistory(event: string, limit = 10): any[] {
    const history = this.eventHistory.get(event) || [];
    return history.slice(-limit);
  }

  clearHistory(): void {
    this.eventHistory.clear();
  }

  getActiveEvents(): string[] {
    return Array.from(this.eventHistory.keys());
  }
}

export const eventBus = EventBusImplementation.getInstance() as EventBusImplementation & 
  TypedEventEmitter<any>;

export function createTypedEventBus<TEvents extends Record<string, any>>(): 
  EventBusImplementation & TypedEventEmitter<TEvents> {
  return eventBus as EventBusImplementation & TypedEventEmitter<TEvents>;
}