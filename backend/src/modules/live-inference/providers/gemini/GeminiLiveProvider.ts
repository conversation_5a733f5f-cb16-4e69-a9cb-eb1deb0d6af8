/**
 * Gemini Live Inference Provider
 * 
 * Implements real-time inference using Google's Gemini Live API
 * Extracted and refactored from existing geminiLiveAudioService.ts
 */

import { EventEmitter } from 'events';
import { GoogleGenAI, LiveServerMessage, Session } from '@google/genai';
import logger from '../../../../utils/logger.js';
import { 
  ILiveInferenceProvider, 
  InferenceSession, 
  InferenceSessionConfig,
  AudioConfig,
  SessionMetrics,
  ProviderStatus
} from '../../interfaces/ILiveInferenceProvider.js';

class GeminiInferenceSession extends EventEmitter implements InferenceSession {
  public readonly id: string;
  public readonly providerId: string;
  public readonly config: InferenceSessionConfig;
  public readonly startTime: Date;
  public endTime?: Date;
  public isActive: boolean = false;
  
  private session: Session | null = null;
  private metrics: SessionMetrics = {
    audioBytesSent: 0,
    audioBytesReceived: 0,
    messagesSent: 0,
    messagesReceived: 0,
    latencyMs: 0,
    duration: 0
  };
  
  constructor(id: string, providerId: string, config: InferenceSessionConfig) {
    super();
    this.id = id;
    this.providerId = providerId;
    this.config = config;
    this.startTime = new Date();
  }
  
  setGeminiSession(session: Session): void {
    this.session = session;
    this.isActive = true;
  }
  
  async sendAudio(audio: Buffer): Promise<void> {
    if (!this.session || !this.isActive) {
      throw new Error('Session is not active');
    }
    
    try {
      // Convert audio to base64 for Gemini
      const base64Audio = audio.toString('base64');
      
      // Send to Gemini Live API
      this.session.sendRealtimeInput({
        media: {
          mimeType: 'audio/pcm;rate=16000',
          data: base64Audio
        }
      });
      
      this.metrics.audioBytesSent += audio.length;
      this.metrics.messagesSent++;
      
      logger.debug(`GeminiSession ${this.id}: Sent ${audio.length} bytes of audio`);
    } catch (error) {
      logger.error(`GeminiSession ${this.id}: Failed to send audio:`, error);
      throw error;
    }
  }
  
  onAudioResponse(callback: (audio: Buffer) => void): void {
    this.on('audio', callback);
  }
  
  async sendText(text: string): Promise<void> {
    if (!this.session || !this.isActive) {
      throw new Error('Session is not active');
    }
    
    try {
      this.session.sendRealtimeInput({ text });
      this.metrics.messagesSent++;
      
      logger.debug(`GeminiSession ${this.id}: Sent text message`);
    } catch (error) {
      logger.error(`GeminiSession ${this.id}: Failed to send text:`, error);
      throw error;
    }
  }
  
  onTextResponse(callback: (text: string) => void): void {
    this.on('text', callback);
  }
  
  async pause(): Promise<void> {
    // Gemini doesn't support pause, just track state
    this.isActive = false;
    logger.info(`GeminiSession ${this.id}: Paused (state only)`);
  }
  
  async resume(): Promise<void> {
    // Gemini doesn't support resume, just track state
    this.isActive = true;
    logger.info(`GeminiSession ${this.id}: Resumed (state only)`);
  }
  
  async end(): Promise<void> {
    if (this.session) {
      try {
        // Close the Gemini session
        (this.session as any).close?.();
      } catch (error) {
        logger.error(`GeminiSession ${this.id}: Error closing session:`, error);
      }
    }
    
    this.isActive = false;
    this.endTime = new Date();
    this.metrics.duration = this.endTime.getTime() - this.startTime.getTime();
    
    logger.info(`GeminiSession ${this.id}: Ended after ${this.metrics.duration}ms`);
  }
  
  getMetrics(): SessionMetrics {
    if (this.isActive) {
      this.metrics.duration = Date.now() - this.startTime.getTime();
    }
    return { ...this.metrics };
  }
  
  handleServerMessage(message: LiveServerMessage): void {
    try {
      // Handle audio response
      const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
      if (audio && audio.mimeType?.includes('audio') && audio.data) {
        const audioBuffer = Buffer.from(audio.data, 'base64');
        this.metrics.audioBytesReceived += audioBuffer.length;
        this.metrics.messagesReceived++;
        this.emit('audio', audioBuffer);
      }
      
      // Handle text response
      const textPart = message.serverContent?.modelTurn?.parts?.find(
        part => part.text !== undefined
      );
      if (textPart?.text) {
        this.metrics.messagesReceived++;
        this.emit('text', textPart.text);
      }
      
      // Handle interruption
      if (message.serverContent?.interrupted) {
        logger.info(`GeminiSession ${this.id}: Interrupted by user`);
        this.emit('interrupted');
      }
      
      // Update latency estimate
      if (message.serverContent?.turnComplete) {
        this.metrics.latencyMs = Date.now() - this.startTime.getTime();
      }
    } catch (error) {
      logger.error(`GeminiSession ${this.id}: Error handling server message:`, error);
    }
  }
}

export class GeminiLiveProvider implements ILiveInferenceProvider {
  public readonly name = 'gemini-live';
  public readonly version = '1.0.0';
  
  private client!: GoogleGenAI;
  private sessions = new Map<string, GeminiInferenceSession>();
  private isInitialized = false;
  private config: {
    apiKey: string;
    defaultModel: string;
    defaultVoice: string;
  };
  
  constructor(apiKey: string) {
    this.config = {
      apiKey,
      defaultModel: 'gemini-2.5-flash-preview-native-audio-dialog',
      defaultVoice: 'Orus'
    };
    this.initialize();
  }
  
  private initialize(): void {
    try {
      this.client = new GoogleGenAI({
        apiKey: this.config.apiKey
      });
      this.isInitialized = true;
      logger.info('GeminiLiveProvider: Initialized successfully');
    } catch (error) {
      logger.error('GeminiLiveProvider: Failed to initialize:', error);
      throw error;
    }
  }
  
  async createSession(config: InferenceSessionConfig): Promise<InferenceSession> {
    if (!this.isInitialized) {
      throw new Error('Provider not initialized');
    }
    
    const sessionId = `gemini_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const session = new GeminiInferenceSession(sessionId, this.name, config);
    
    try {
      logger.info(`GeminiLiveProvider: Creating session ${sessionId}`);
      
      // Create Gemini Live connection
      const geminiSession = await this.client.live.connect({
        model: config.model || this.config.defaultModel,
        callbacks: {
          onopen: () => {
            logger.info(`GeminiLiveProvider: Session ${sessionId} opened`);
            this.emit('sessionOpened', sessionId);
          },
          onmessage: async (message: LiveServerMessage) => {
            session.handleServerMessage(message);
          },
          onerror: (error: any) => {
            logger.error(`GeminiLiveProvider: Session ${sessionId} error:`, error);
            this.emit('sessionError', sessionId, error);
          },
          onclose: (event: any) => {
            logger.info(`GeminiLiveProvider: Session ${sessionId} closed: ${event.reason || 'Unknown'}`);
            this.handleSessionClose(sessionId);
          }
        }
      });
      
      // Configure session with system instruction
      if (config.systemPrompt) {
        await geminiSession.sendRealtimeInput({
          text: `System: ${config.systemPrompt}`
        });
      }
      
      session.setGeminiSession(geminiSession);
      this.sessions.set(sessionId, session);
      
      logger.info(`GeminiLiveProvider: Session ${sessionId} created successfully`);
      return session;
    } catch (error) {
      logger.error(`GeminiLiveProvider: Failed to create session:`, error);
      throw error;
    }
  }
  
  getSession(sessionId: string): InferenceSession | undefined {
    return this.sessions.get(sessionId);
  }
  
  listActiveSessions(): InferenceSession[] {
    return Array.from(this.sessions.values()).filter(s => s.isActive);
  }
  
  async closeSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      logger.warn(`GeminiLiveProvider: Session ${sessionId} not found`);
      return;
    }
    
    await session.end();
    this.sessions.delete(sessionId);
    logger.info(`GeminiLiveProvider: Session ${sessionId} closed and removed`);
  }
  
  getSupportedModels(): string[] {
    return [
      'gemini-2.5-flash-preview-native-audio-dialog',
      'gemini-2.0-flash-exp',
      'gemini-1.5-flash-native-audio'
    ];
  }
  
  getSupportedVoices(): string[] {
    return ['Orus', 'Puck', 'Charon', 'Kore', 'Fenrir', 'Aoede'];
  }
  
  getSupportedLanguages(): string[] {
    return ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'];
  }
  
  getAudioConfig(): AudioConfig {
    return {
      inputSampleRate: 16000,  // Gemini expects 16kHz input
      outputSampleRate: 24000, // Gemini outputs 24kHz
      inputFormat: 'pcm',
      outputFormat: 'pcm',
      channels: 1,
      bitDepth: 16
    };
  }
  
  async isHealthy(): Promise<boolean> {
    try {
      // Simple health check - try to verify API key is valid
      return this.isInitialized && !!this.config.apiKey;
    } catch (error) {
      logger.error('GeminiLiveProvider: Health check failed:', error);
      return false;
    }
  }
  
  getStatus(): ProviderStatus {
    const activeSessions = this.listActiveSessions();
    return {
      healthy: this.isInitialized,
      latency: activeSessions.length > 0 
        ? this.calculateAverage(activeSessions.map(s => s.getMetrics().latencyMs))
        : undefined,
      lastChecked: new Date()
    };
  }
  
  private handleSessionClose(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.isActive = false;
      session.endTime = new Date();
      this.emit('sessionClosed', sessionId);
    }
  }
  
  private emit(event: string, ...args: any[]): void {
    // Provider-level events can be handled here if needed
    logger.debug(`GeminiLiveProvider: Event ${event}`, ...args);
  }
  
  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((a, b) => a + b, 0) / values.length;
  }
}