/**
 * OpenAI Realtime Inference Provider
 * 
 * Implements real-time inference using OpenAI's Realtime API
 * Extracted and refactored from existing OpenAIWebSocketHandler.ts
 */

import { EventEmitter } from 'events';
import WebSocket from 'ws';
import logger from '../../../../utils/logger.js';
import {
  ILiveInferenceProvider,
  InferenceSession,
  InferenceSessionConfig,
  AudioConfig,
  SessionMetrics,
  ProviderStatus
} from '../../interfaces/ILiveInferenceProvider.js';

class OpenAIInferenceSession extends EventEmitter implements InferenceSession {
  public readonly id: string;
  public readonly providerId: string;
  public readonly config: InferenceSessionConfig;
  public readonly startTime: Date;
  public endTime?: Date;
  public isActive: boolean = false;
  
  private ws: WebSocket | null = null;
  private metrics: SessionMetrics = {
    audioBytesSent: 0,
    audioBytesReceived: 0,
    messagesSent: 0,
    messagesReceived: 0,
    latencyMs: 0,
    duration: 0
  };
  
  constructor(id: string, providerId: string, config: InferenceSessionConfig) {
    super();
    this.id = id;
    this.providerId = providerId;
    this.config = config;
    this.startTime = new Date();
  }
  
  setWebSocket(ws: WebSocket): void {
    this.ws = ws;
    this.isActive = true;
    this.setupWebSocketHandlers();
  }
  
  private setupWebSocketHandlers(): void {
    if (!this.ws) return;
    
    this.ws.on('message', (data: any) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleServerMessage(message);
      } catch (error) {
        logger.error(`OpenAISession ${this.id}: Failed to parse message:`, error);
      }
    });
    
    this.ws.on('error', (error) => {
      logger.error(`OpenAISession ${this.id}: WebSocket error:`, error);
      this.emit('error', error);
    });
    
    this.ws.on('close', () => {
      logger.info(`OpenAISession ${this.id}: WebSocket closed`);
      this.isActive = false;
      this.emit('closed');
    });
  }
  
  async sendAudio(audio: Buffer): Promise<void> {
    if (!this.ws || !this.isActive) {
      throw new Error('Session is not active');
    }
    
    try {
      // OpenAI expects base64 encoded audio in specific format
      const base64Audio = audio.toString('base64');
      
      const audioAppend = {
        type: 'input_audio_buffer.append',
        audio: base64Audio
      };
      
      this.ws.send(JSON.stringify(audioAppend));
      this.metrics.audioBytesSent += audio.length;
      this.metrics.messagesSent++;
      
      logger.debug(`OpenAISession ${this.id}: Sent ${audio.length} bytes of audio`);
    } catch (error) {
      logger.error(`OpenAISession ${this.id}: Failed to send audio:`, error);
      throw error;
    }
  }
  
  onAudioResponse(callback: (audio: Buffer) => void): void {
    this.on('audio', callback);
  }
  
  async sendText(text: string): Promise<void> {
    if (!this.ws || !this.isActive) {
      throw new Error('Session is not active');
    }
    
    try {
      const conversationItem = {
        type: 'conversation.item.create',
        item: {
          type: 'message',
          role: 'user',
          content: [{
            type: 'input_text',
            text: text
          }]
        }
      };
      
      this.ws.send(JSON.stringify(conversationItem));
      
      // Trigger response
      this.ws.send(JSON.stringify({ type: 'response.create' }));
      
      this.metrics.messagesSent++;
      logger.debug(`OpenAISession ${this.id}: Sent text message`);
    } catch (error) {
      logger.error(`OpenAISession ${this.id}: Failed to send text:`, error);
      throw error;
    }
  }
  
  onTextResponse(callback: (text: string) => void): void {
    this.on('text', callback);
  }
  
  async pause(): Promise<void> {
    if (!this.ws || !this.isActive) return;
    
    try {
      // Cancel current response
      this.ws.send(JSON.stringify({ type: 'response.cancel' }));
      this.isActive = false;
      logger.info(`OpenAISession ${this.id}: Paused`);
    } catch (error) {
      logger.error(`OpenAISession ${this.id}: Failed to pause:`, error);
    }
  }
  
  async resume(): Promise<void> {
    if (!this.ws) return;
    
    this.isActive = true;
    logger.info(`OpenAISession ${this.id}: Resumed`);
  }
  
  async end(): Promise<void> {
    if (this.ws) {
      try {
        this.ws.close();
      } catch (error) {
        logger.error(`OpenAISession ${this.id}: Error closing WebSocket:`, error);
      }
    }
    
    this.isActive = false;
    this.endTime = new Date();
    this.metrics.duration = this.endTime.getTime() - this.startTime.getTime();
    
    logger.info(`OpenAISession ${this.id}: Ended after ${this.metrics.duration}ms`);
  }
  
  getMetrics(): SessionMetrics {
    if (this.isActive) {
      this.metrics.duration = Date.now() - this.startTime.getTime();
    }
    return { ...this.metrics };
  }
  
  private handleServerMessage(message: any): void {
    try {
      switch (message.type) {
        case 'response.audio.delta':
          if (message.delta) {
            const audioBuffer = Buffer.from(message.delta, 'base64');
            this.metrics.audioBytesReceived += audioBuffer.length;
            this.metrics.messagesReceived++;
            this.emit('audio', audioBuffer);
          }
          break;
          
        case 'response.text.delta':
          if (message.delta) {
            this.metrics.messagesReceived++;
            this.emit('text', message.delta);
          }
          break;
          
        case 'response.audio_transcript.delta':
          if (message.delta) {
            this.emit('transcript', message.delta);
          }
          break;
          
        case 'response.done':
          logger.debug(`OpenAISession ${this.id}: Response completed`);
          this.emit('response_done');
          break;
          
        case 'error':
          logger.error(`OpenAISession ${this.id}: Server error:`, message.error);
          this.emit('error', new Error(message.error.message || 'Unknown error'));
          break;
          
        case 'session.created':
          logger.info(`OpenAISession ${this.id}: Session created on server`);
          break;
          
        case 'session.updated':
          logger.debug(`OpenAISession ${this.id}: Session updated`);
          break;
      }
      
      // Update latency estimate
      if (message.type?.startsWith('response.')) {
        this.metrics.latencyMs = Date.now() - this.startTime.getTime();
      }
    } catch (error) {
      logger.error(`OpenAISession ${this.id}: Error handling server message:`, error);
    }
  }
}

export class OpenAIRealtimeProvider implements ILiveInferenceProvider {
  public readonly name = 'openai-realtime';
  public readonly version = '1.0.0';
  
  private sessions = new Map<string, OpenAIInferenceSession>();
  private config: {
    apiKey: string;
    defaultModel: string;
    defaultVoice: string;
    baseUrl: string;
  };
  
  constructor(apiKey: string) {
    this.config = {
      apiKey,
      defaultModel: 'gpt-4o-realtime-preview-2024-12-17',
      defaultVoice: 'shimmer',
      baseUrl: 'wss://api.openai.com/v1/realtime'
    };
  }
  
  async createSession(config: InferenceSessionConfig): Promise<InferenceSession> {
    const sessionId = `openai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const session = new OpenAIInferenceSession(sessionId, this.name, config);
    
    try {
      logger.info(`OpenAIRealtimeProvider: Creating session ${sessionId}`);
      
      // Create WebSocket connection to OpenAI Realtime API
      const url = `${this.config.baseUrl}?model=${config.model || this.config.defaultModel}`;
      const ws = new WebSocket(url, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'OpenAI-Beta': 'realtime=v1'
        }
      });
      
      // Wait for connection
      await new Promise<void>((resolve, reject) => {
        ws.once('open', () => {
          logger.info(`OpenAIRealtimeProvider: WebSocket connected for session ${sessionId}`);
          resolve();
        });
        ws.once('error', (error) => {
          logger.error(`OpenAIRealtimeProvider: WebSocket connection failed:`, error);
          reject(error);
        });
      });
      
      // Configure session
      const sessionConfig = {
        type: 'session.update',
        session: {
          modalities: ['text', 'audio'],
          instructions: config.systemPrompt || 'You are a helpful AI assistant.',
          voice: config.voice || this.config.defaultVoice,
          input_audio_format: 'g711_ulaw',
          output_audio_format: 'g711_ulaw',
          temperature: config.temperature || 0.8,
          max_response_output_tokens: config.maxTokens || 4096,
          turn_detection: {
            type: 'server_vad',
            threshold: 0.5,
            prefix_padding_ms: 300,
            silence_duration_ms: 500
          }
        }
      };
      
      ws.send(JSON.stringify(sessionConfig));
      
      session.setWebSocket(ws);
      this.sessions.set(sessionId, session);
      
      logger.info(`OpenAIRealtimeProvider: Session ${sessionId} created successfully`);
      return session;
    } catch (error) {
      logger.error(`OpenAIRealtimeProvider: Failed to create session:`, error);
      throw error;
    }
  }
  
  getSession(sessionId: string): InferenceSession | undefined {
    return this.sessions.get(sessionId);
  }
  
  listActiveSessions(): InferenceSession[] {
    return Array.from(this.sessions.values()).filter(s => s.isActive);
  }
  
  async closeSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      logger.warn(`OpenAIRealtimeProvider: Session ${sessionId} not found`);
      return;
    }
    
    await session.end();
    this.sessions.delete(sessionId);
    logger.info(`OpenAIRealtimeProvider: Session ${sessionId} closed and removed`);
  }
  
  getSupportedModels(): string[] {
    return [
      'gpt-4o-realtime-preview-2024-12-17',
      'gpt-4o-realtime-preview'
    ];
  }
  
  getSupportedVoices(): string[] {
    return ['shimmer', 'alloy', 'echo', 'fable', 'onyx', 'nova'];
  }
  
  getSupportedLanguages(): string[] {
    return ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'];
  }
  
  getAudioConfig(): AudioConfig {
    return {
      inputSampleRate: 8000,   // OpenAI can work directly with 8kHz μ-law
      outputSampleRate: 8000,  // No conversion needed for Twilio
      inputFormat: 'ulaw',
      outputFormat: 'ulaw',
      channels: 1,
      bitDepth: 8
    };
  }
  
  async isHealthy(): Promise<boolean> {
    try {
      // Simple health check - verify API key format
      return !!this.config.apiKey && this.config.apiKey.startsWith('sk-');
    } catch (error) {
      logger.error('OpenAIRealtimeProvider: Health check failed:', error);
      return false;
    }
  }
  
  getStatus(): ProviderStatus {
    const activeSessions = this.listActiveSessions();
    return {
      healthy: !!this.config.apiKey,
      latency: activeSessions.length > 0
        ? this.calculateAverage(activeSessions.map(s => s.getMetrics().latencyMs))
        : undefined,
      lastChecked: new Date()
    };
  }
  
  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((a, b) => a + b, 0) / values.length;
  }
}