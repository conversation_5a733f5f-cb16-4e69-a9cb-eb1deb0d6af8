/**
 * Session Manager Interface
 * 
 * Manages live inference sessions across providers
 * Handles session lifecycle, persistence, and recovery
 */

export interface SessionState {
  id: string;
  providerId: string;
  providerType: 'inference' | 'telephony';
  status: 'initializing' | 'active' | 'paused' | 'ending' | 'ended' | 'failed';
  startTime: Date;
  endTime?: Date;
  metadata: Record<string, any>;
  error?: string;
}

export interface SessionEvent {
  sessionId: string;
  type: 'created' | 'started' | 'paused' | 'resumed' | 'ended' | 'failed' | 'audio_sent' | 'audio_received' | 'text_sent' | 'text_received';
  timestamp: Date;
  data?: any;
}

export interface ISessionManager {
  // Session lifecycle
  createSession(providerId: string, providerType: 'inference' | 'telephony', metadata?: Record<string, any>): string;
  startSession(sessionId: string): Promise<void>;
  pauseSession(sessionId: string): Promise<void>;
  resumeSession(sessionId: string): Promise<void>;
  endSession(sessionId: string, reason?: string): Promise<void>;
  
  // Session queries
  getSession(sessionId: string): SessionState | undefined;
  getSessionsByProvider(providerId: string): SessionState[];
  getActiveSessions(): SessionState[];
  getAllSessions(): SessionState[];
  
  // Session linking (for coordinating inference + telephony)
  linkSessions(inferenceSessionId: string, telephonySessionId: string): void;
  getLinkedSession(sessionId: string): string | undefined;
  unlinkSessions(sessionId: string): void;
  
  // Event handling
  recordEvent(event: SessionEvent): void;
  getSessionEvents(sessionId: string, limit?: number): SessionEvent[];
  onSessionEvent(callback: (event: SessionEvent) => void): void;
  
  // Persistence
  saveSession(sessionId: string): Promise<void>;
  loadSession(sessionId: string): Promise<SessionState | undefined>;
  cleanupOldSessions(olderThanMs: number): Promise<number>;
  
  // Statistics
  getSessionStats(sessionId: string): SessionStats;
  getProviderStats(providerId: string): ProviderStats;
}

export interface SessionStats {
  duration: number;
  eventCount: number;
  audioBytesSent: number;
  audioBytesReceived: number;
  messagesSent: number;
  messagesReceived: number;
  errors: number;
}

export interface ProviderStats {
  totalSessions: number;
  activeSessions: number;
  failedSessions: number;
  averageDuration: number;
  totalDuration: number;
}

export interface SessionConfig {
  sessionId?: string;
  providerId: string;
  providerType: 'inference' | 'telephony';
  audioConfig?: {
    sampleRate: number;
    channels: number;
    format: 'pcm' | 'wav' | 'mp3';
  };
  metadata?: Record<string, any>;
}

export type SessionType = 'inference' | 'telephony' | 'bridge';

export type SessionErrorHandler = (sessionId: string, error: Error) => void;
export type SessionEventHandler = (event: SessionEvent) => void;