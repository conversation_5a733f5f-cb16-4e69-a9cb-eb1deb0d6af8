/**
 * Campaign Management Service
 * 
 * Direct service for campaign management with playback integration.
 * Handles campaign lifecycle, script management, and target processing.
 * Includes multi-tenant support and comprehensive logging.
 */

import { v4 as uuidv4 } from 'uuid';
import prisma from '../../lib/prisma.js';
import logger from '../../utils/logger.js';
import { AppError, ErrorSeverity } from '../../utils/errorHandler.js';

export interface CampaignOptions {
  scriptId?: string;
  projectId?: string;
  maxConcurrentCalls?: number;
  callTimeout?: number; // in seconds
  scheduleConfig?: ScheduleConfig;
  playbackConfig?: PlaybackConfig;
}

export interface ScheduleConfig {
  startTime?: string; // HH:MM format
  endTime?: string; // HH:MM format
  days?: number[]; // 1-7, where 1 is Monday
  timezone?: string;
}

export interface PlaybackConfig {
  voice?: string;
  language?: string;
  generateClips?: boolean;
  clipLibraryId?: string;
}

export interface CampaignResult {
  id: string;
  name: string;
  description?: string;
  status: CampaignStatus;
  content: Record<string, unknown>;
  userId: string;
  tenantId: string;
  scriptId?: string;
  projectId?: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, unknown>;
}

export enum CampaignStatus {
  DRAFT = 'draft',
  PREPARING = 'preparing',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ERROR = 'error'
}

export interface TenantContext {
  tenantId: string;
  userId?: string;
}

export interface CampaignStats {
  totalTargets: number;
  callsInitiated: number;
  callsCompleted: number;
  callsInProgress: number;
  callsFailed: number;
  averageDuration?: number;
  successRate: number;
}

interface DatabaseCampaign {
  id: string;
  name: string;
  description?: string | null;
  content: unknown;
  user_id: string | null;
  tenant_id: string | null;
  tenant_path: string;
  script_id?: string | null;
  project_id?: string | null;
  status: string;
  created_at: Date | null;
  updated_at: Date | null;
  script?: { id: string; name: string } | null;
  project?: { id: string; name: string } | null;
  targets?: Array<{ id: string; name: string; phoneNumber: string }>;
}

export class CampaignService {
  constructor() {
    logger.log('CampaignService initialized');
  }

  /**
   * Create a new campaign
   */
  async createCampaign(
    name: string,
    description: string,
    tenantContext: TenantContext,
    options: CampaignOptions = {}
  ): Promise<CampaignResult> {
    const campaignId = uuidv4();
    const startTime = Date.now();
    
    logger.log(`[${campaignId}] CAMPAIGN_CREATE: Creating campaign "${name}"`, {
      tenantId: tenantContext.tenantId,
      name,
      options
    });

    try {
      const campaign = await prisma.campaign.create({
        data: {
          id: campaignId,
          name,
          description,
          content: JSON.stringify({
            options: JSON.parse(JSON.stringify(options)),
            createdAt: new Date().toISOString()
          }),
          user_id: tenantContext.userId || '',
          tenant_id: tenantContext.tenantId,
          tenant_path: '',
          script_id: options.scriptId,
          project_id: options.projectId,
          status: CampaignStatus.DRAFT
        }
      });

      const result = this.mapToCampaignResult(campaign);

      logger.log(`[${campaignId}] CAMPAIGN_CREATED: Successfully created`, {
        duration: Date.now() - startTime
      });

      return result;

    } catch (error) {
      logger.error(`[${campaignId}] CAMPAIGN_CREATE_FAILED:`, error);
      throw new AppError({
        code: 'CAMPAIGN_CREATION_FAILED',
        message: `Failed to create campaign "${name}"`,
        originalError: error instanceof Error ? error : new Error(String(error)),
        severity: ErrorSeverity.HIGH,
        component: 'campaign-service'
      });
    }
  }

  /**
   * Get campaign by ID
   */
  async getCampaign(campaignId: string, tenantContext: TenantContext): Promise<CampaignResult | null> {
    try {
      const campaign = await prisma.campaign.findUnique({
        where: {
          id: campaignId,
          tenant_id: tenantContext.tenantId
        },
        include: {
          script: {
            select: { id: true, name: true }
          },
          project: {
            select: { id: true, name: true }
          },
          targets: {
            select: { id: true, name: true, phoneNumber: true },
            take: 10 // Limit for performance
          }
        }
      });

      if (!campaign) {
        logger.warn(`[${campaignId}] CAMPAIGN_NOT_FOUND: Campaign not found for tenant ${tenantContext.tenantId}`);
        return null;
      }

      return this.mapToCampaignResult(campaign);

    } catch (error) {
      logger.error(`[${campaignId}] CAMPAIGN_GET_ERROR:`, error);
      throw new AppError({
        code: 'CAMPAIGN_GET_ERROR',
        message: `Failed to get campaign ${campaignId}`,
        originalError: error instanceof Error ? error : new Error(String(error)),
        severity: ErrorSeverity.MEDIUM,
        component: 'campaign-service'
      });
    }
  }

  /**
   * Update campaign
   */
  async updateCampaign(
    campaignId: string,
    updates: Partial<{
      name: string;
      description: string;
      status: CampaignStatus;
      content: Record<string, unknown>;
      scriptId: string;
      projectId: string;
    }>,
    tenantContext: TenantContext
  ): Promise<CampaignResult> {
    const startTime = Date.now();
    
    logger.log(`[${campaignId}] CAMPAIGN_UPDATE: Updating campaign`, {
      tenantId: tenantContext.tenantId,
      updates
    });

    try {
      // Construct the update data object to match Prisma schema
      const updateData: Record<string, unknown> = {
        updated_at: new Date()
      };

      // Only include fields that are being updated
      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.description !== undefined) updateData.description = updates.description;
      if (updates.status !== undefined) updateData.status = updates.status;
      if (updates.content !== undefined) updateData.content = updates.content;
      if (updates.scriptId !== undefined) updateData.script_id = updates.scriptId;
      if (updates.projectId !== undefined) updateData.project_id = updates.projectId;

      const campaign = await prisma.campaign.update({
        where: {
          id: campaignId,
          tenant_id: tenantContext.tenantId
        },
        data: updateData,
        include: {
          script: {
            select: { id: true, name: true }
          },
          project: {
            select: { id: true, name: true }
          }
        }
      });

      logger.log(`[${campaignId}] CAMPAIGN_UPDATED: Successfully updated`, {
        duration: Date.now() - startTime
      });

      return this.mapToCampaignResult(campaign);

    } catch (error) {
      logger.error(`[${campaignId}] CAMPAIGN_UPDATE_FAILED:`, error);
      throw new AppError({
        code: 'CAMPAIGN_UPDATE_FAILED',
        message: `Failed to update campaign ${campaignId}`,
        originalError: error instanceof Error ? error : new Error(String(error)),
        severity: ErrorSeverity.HIGH,
        component: 'campaign-service'
      });
    }
  }

  /**
   * Delete campaign
   */
  async deleteCampaign(campaignId: string, tenantContext: TenantContext): Promise<void> {
    const startTime = Date.now();
    
    logger.log(`[${campaignId}] CAMPAIGN_DELETE: Deleting campaign`, {
      tenantId: tenantContext.tenantId
    });

    try {
      await prisma.campaign.delete({
        where: {
          id: campaignId,
          tenant_id: tenantContext.tenantId
        }
      });

      logger.log(`[${campaignId}] CAMPAIGN_DELETED: Successfully deleted`, {
        duration: Date.now() - startTime
      });

    } catch (error) {
      logger.error(`[${campaignId}] CAMPAIGN_DELETE_FAILED:`, error);
      throw new AppError({
        code: 'CAMPAIGN_DELETE_FAILED',
        message: `Failed to delete campaign ${campaignId}`,
        originalError: error instanceof Error ? error : new Error(String(error)),
        severity: ErrorSeverity.HIGH,
        component: 'campaign-service'
      });
    }
  }

  /**
   * List campaigns for a tenant with pagination
   */
  async listCampaigns(
    tenantContext: TenantContext,
    options: {
      limit?: number;
      offset?: number;
      status?: CampaignStatus;
      projectId?: string;
      searchTerm?: string;
    } = {}
  ): Promise<{ campaigns: CampaignResult[]; total: number }> {
    const { limit = 50, offset = 0, status, projectId, searchTerm } = options;

    try {
      const where = {
        tenant_id: tenantContext.tenantId,
        ...(status && { status }),
        ...(projectId && { project_id: projectId }),
        ...(searchTerm && {
          OR: [
            { name: { contains: searchTerm, mode: 'insensitive' as const } },
            { description: { contains: searchTerm, mode: 'insensitive' as const } }
          ]
        })
      };

      const [campaigns, total] = await Promise.all([
        prisma.campaign.findMany({
          where,
          orderBy: { created_at: 'desc' },
          take: limit,
          skip: offset,
          include: {
            script: { select: { id: true, name: true } },
            project: { select: { id: true, name: true } },
            _count: {
              select: { targets: true }
            }
          }
        }),
        prisma.campaign.count({ where })
      ]);

      return {
        campaigns: campaigns.map(campaign => this.mapToCampaignResult(campaign)),
        total
      };

    } catch (error) {
      logger.error('CAMPAIGN_LIST_ERROR:', error);
      throw new AppError({
        code: 'CAMPAIGN_LIST_ERROR',
        message: 'Failed to list campaigns',
        originalError: error instanceof Error ? error : new Error(String(error)),
        severity: ErrorSeverity.MEDIUM,
        component: 'campaign-service'
      });
    }
  }

  /**
   * Start a campaign
   */
  async startCampaign(campaignId: string, tenantContext: TenantContext): Promise<CampaignResult> {
    const startTime = Date.now();
    
    logger.log(`[${campaignId}] CAMPAIGN_START: Starting campaign`, {
      tenantId: tenantContext.tenantId
    });

    try {
      // Validate campaign can be started
      await this.validateCampaignCanStart(campaignId, tenantContext);

      // Update campaign status
      const campaign = await this.updateCampaign(
        campaignId,
        { status: CampaignStatus.ACTIVE },
        tenantContext
      );

      // Initialize playback clips generation if campaign has clips
      if (campaign.settings && campaign.settings.playbackEnabled) {
        logger.log(`[${campaignId}] CAMPAIGN_PLAYBACK: Initializing playback clips`);
        // Playback generation is handled by the call processing logic
      }

      // Start background job for call processing
      logger.log(`[${campaignId}] CAMPAIGN_JOB: Starting background job for call processing`);
      // Note: Actual job processing is handled by the QueueService when calls are initiated

      logger.log(`[${campaignId}] CAMPAIGN_STARTED: Successfully started`, {
        duration: Date.now() - startTime
      });

      return campaign;

    } catch (error) {
      logger.error(`[${campaignId}] CAMPAIGN_START_FAILED:`, error);
      throw new AppError({
        code: 'CAMPAIGN_START_FAILED',
        message: `Failed to start campaign ${campaignId}`,
        originalError: error instanceof Error ? error : new Error(String(error)),
        severity: ErrorSeverity.HIGH,
        component: 'campaign-service'
      });
    }
  }

  /**
   * Stop a campaign
   */
  async stopCampaign(campaignId: string, tenantContext: TenantContext): Promise<CampaignResult> {
    const startTime = Date.now();
    
    logger.log(`[${campaignId}] CAMPAIGN_STOP: Stopping campaign`, {
      tenantId: tenantContext.tenantId
    });

    try {
      const campaign = await this.updateCampaign(
        campaignId,
        { status: CampaignStatus.PAUSED },
        tenantContext
      );

      // Cancel pending calls
      const pendingCallsCount = await prisma.call.count({
        where: {
          campaignId,
          tenantId: tenantContext.tenantId,
          status: 'pending'
        }
      });

      if (pendingCallsCount > 0) {
        await prisma.call.updateMany({
          where: {
            campaignId,
            tenantId: tenantContext.tenantId,
            status: 'pending'
          },
          data: {
            status: 'cancelled',
            metadata: {
              cancelledAt: new Date().toISOString(),
              reason: 'Campaign stopped'
            }
          }
        });
        logger.log(`[${campaignId}] CAMPAIGN_CALLS_CANCELLED: Cancelled ${pendingCallsCount} pending calls`);
      }

      // Stop background job processing
      logger.log(`[${campaignId}] CAMPAIGN_JOB: Stopping background job processing`);
      // Note: Job processing automatically stops when campaign status changes to PAUSED

      logger.log(`[${campaignId}] CAMPAIGN_STOPPED: Successfully stopped`, {
        duration: Date.now() - startTime
      });

      return campaign;

    } catch (error) {
      logger.error(`[${campaignId}] CAMPAIGN_STOP_FAILED:`, error);
      throw new AppError({
        code: 'CAMPAIGN_STOP_FAILED',
        message: `Failed to stop campaign ${campaignId}`,
        originalError: error instanceof Error ? error : new Error(String(error)),
        severity: ErrorSeverity.HIGH,
        component: 'campaign-service'
      });
    }
  }

  /**
   * Get campaign statistics
   */
  async getCampaignStats(campaignId: string, tenantContext: TenantContext): Promise<CampaignStats> {
    try {
      // Get targets count
      const targetCount = await prisma.target.count({
        where: {
          campaignId,
          tenantId: tenantContext.tenantId
        }
      });

      // Get call statistics from Call table (which has campaign_id)
      const callStats = await prisma.call.groupBy({
        by: ['status'],
        where: {
          campaignId,
          tenantId: tenantContext.tenantId
        },
        _count: {
          status: true
        }
      });

      // Process call statistics
      const statusCounts = callStats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.status;
        return acc;
      }, {} as Record<string, number>);

      const callsInitiated = Object.values(statusCounts).reduce((sum, count) => sum + count, 0) - (statusCounts.pending || 0);
      const callsCompleted = statusCounts.completed || 0;
      const callsInProgress = statusCounts.in_progress || 0;
      const callsFailed = (statusCounts.failed || 0) + (statusCounts.cancelled || 0);
      const successRate = callsInitiated > 0 ? (callsCompleted / callsInitiated) * 100 : 0;

      const stats: CampaignStats = {
        totalTargets: targetCount,
        callsInitiated,
        callsCompleted,
        callsInProgress,
        callsFailed,
        successRate: Math.round(successRate * 100) / 100 // Round to 2 decimal places
      };

      return stats;

    } catch (error) {
      logger.error(`[${campaignId}] CAMPAIGN_STATS_ERROR:`, error);
      throw new AppError({
        code: 'CAMPAIGN_STATS_ERROR',
        message: `Failed to get campaign stats for ${campaignId}`,
        originalError: error instanceof Error ? error : new Error(String(error)),
        severity: ErrorSeverity.MEDIUM,
        component: 'campaign-service'
      });
    }
  }

  /**
   * Get predefined campaign script by ID (for Gemini Live integration)
   * Based on the proven working solution
   */
  async getPredefinedCampaignScript(id: string): Promise<{
    id: string;
    name: string;
    content: string;
    language: 'en' | 'es' | 'cz';
    type: 'insurance' | 'fundraising';
    format: 'json' | 'html' | 'markdown';
  }> {
    // Validate campaign ID
    const validCampaignIds = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];
    if (!validCampaignIds.includes(id)) {
      throw new Error('Invalid campaign ID. Must be 1-12.');
    }

    // Campaign metadata based on the working solution
    const campaignMetadata = {
      '1': { name: 'English Insurance Campaign (JSON)', language: 'en' as const, type: 'insurance' as const, format: 'json' as const },
      '2': { name: 'English Insurance Campaign (HTML)', language: 'en' as const, type: 'insurance' as const, format: 'html' as const },
      '3': { name: 'English Insurance Campaign (Markdown)', language: 'en' as const, type: 'insurance' as const, format: 'markdown' as const },
      '4': { name: 'English Fundraising Campaign (JSON)', language: 'en' as const, type: 'fundraising' as const, format: 'json' as const },
      '5': { name: 'English Fundraising Campaign (HTML)', language: 'en' as const, type: 'fundraising' as const, format: 'html' as const },
      '6': { name: 'English Fundraising Campaign (Markdown)', language: 'en' as const, type: 'fundraising' as const, format: 'markdown' as const },
      '7': { name: 'Czech Insurance Campaign (JSON)', language: 'cz' as const, type: 'insurance' as const, format: 'json' as const },
      '8': { name: 'Czech Insurance Campaign (HTML)', language: 'cz' as const, type: 'insurance' as const, format: 'html' as const },
      '9': { name: 'Czech Insurance Campaign (Markdown)', language: 'cz' as const, type: 'insurance' as const, format: 'markdown' as const },
      '10': { name: 'Czech Fundraising Campaign (JSON)', language: 'cz' as const, type: 'fundraising' as const, format: 'json' as const },
      '11': { name: 'Czech Fundraising Campaign (HTML)', language: 'cz' as const, type: 'fundraising' as const, format: 'html' as const },
      '12': { name: 'Czech Fundraising Campaign (Markdown)', language: 'cz' as const, type: 'fundraising' as const, format: 'markdown' as const }
    };

    const metadata = campaignMetadata[id as keyof typeof campaignMetadata];

    // Generate default content based on type and language
    const content = this.generateDefaultCampaignContent(metadata.type, metadata.language, metadata.format);

    return {
      id,
      name: metadata.name,
      content,
      language: metadata.language,
      type: metadata.type,
      format: metadata.format
    };
  }

  /**
   * Generate default campaign content
   */
  private generateDefaultCampaignContent(
    type: 'insurance' | 'fundraising',
    language: 'en' | 'es' | 'cz',
    format: 'json' | 'html' | 'markdown'
  ): string {
    const isEnglish = language === 'en';
    const isSpanish = language === 'es';
    const isInsurance = type === 'insurance';

    let greeting: string;
    let purpose: string;
    let closing: string;

    if (isEnglish) {
      greeting = isInsurance ?
        "Hello! I'm calling about our insurance services." :
        "Hello! I'm calling about an important fundraising campaign.";
      purpose = isInsurance ?
        "We have special offers on life and health insurance that could save you money." :
        "We're raising funds for a great cause that makes a real difference in our community.";
      closing = "Thank you for your time! Have a wonderful day.";
    } else if (isSpanish) {
      greeting = isInsurance ?
        "¡Hola! Le llamo sobre nuestros servicios de seguros." :
        "¡Hola! Le llamo sobre una campaña de recaudación de fondos importante.";
      purpose = isInsurance ?
        "Tenemos ofertas especiales en seguros de vida y salud que podrían ahorrarle dinero." :
        "Estamos recaudando fondos para una gran causa que marca una diferencia real en nuestra comunidad.";
      closing = "¡Gracias por su tiempo! Que tenga un día maravilloso.";
    } else { // Czech
      greeting = isInsurance ?
        "Dobrý den! Volám ohledně našich pojišťovacích služeb." :
        "Dobrý den! Volám ohledně důležité sbírkové kampaně.";
      purpose = isInsurance ?
        "Máme speciální nabídky životního a zdravotního pojištění, které vám mohou ušetřit peníze." :
        "Sbíráme prostředky na dobrou věc, která skutečně mění naši komunitu.";
      closing = "Děkuji za váš čas! Přeji vám krásný den.";
    }

    if (format === 'json') {
      return JSON.stringify({
        campaign: `${type.charAt(0).toUpperCase() + type.slice(1)} Campaign`,
        agentPersona: {
          name: isEnglish ? (isInsurance ? "Insurance Agent" : "Fundraising Representative") :
                isSpanish ? (isInsurance ? "Agente de Seguros" : "Representante de Recaudación") :
                (isInsurance ? "Pojišťovací agent" : "Zástupce sbírky"),
          personality: isEnglish ? "Professional, friendly, and helpful" :
                      isSpanish ? "Profesional, amigable y servicial" :
                      "Profesionální, přátelský a nápomocný"
        },
        customerData: {
          targetAudience: isInsurance ? "Adults 25-65" : "Community members",
          interests: isInsurance ? ["financial security", "health coverage"] : ["community support", "charitable giving"]
        },
        script: {
          greeting,
          purpose,
          objectionHandling: isEnglish ? "I understand your concerns. Let me explain the benefits..." :
                            isSpanish ? "Entiendo sus preocupaciones. Permítame explicarle los beneficios..." :
                            "Rozumím vašim obavám. Dovolte mi vysvětlit výhody...",
          closing
        }
      }, null, 2);
    } else if (format === 'html') {
      return `<!DOCTYPE html>
<html>
<head>
    <title>${type.charAt(0).toUpperCase() + type.slice(1)} Campaign Script</title>
</head>
<body>
    <h1>Campaign Script</h1>
    <h2>Greeting</h2>
    <p>${greeting}</p>

    <h2>Purpose</h2>
    <p>${purpose}</p>

    <h2>Closing</h2>
    <p>${closing}</p>
</body>
</html>`;
    } else { // markdown
      return `# ${type.charAt(0).toUpperCase() + type.slice(1)} Campaign Script

## Greeting
${greeting}

## Purpose
${purpose}

## Closing
${closing}

---
*Generated campaign script for ${language.toUpperCase()} ${type} campaign*`;
    }
  }

  // Private helper methods

  private async validateCampaignCanStart(campaignId: string, tenantContext: TenantContext): Promise<void> {
    const campaign = await this.getCampaign(campaignId, tenantContext);
    
    if (!campaign) {
      throw new Error(`Campaign ${campaignId} not found`);
    }

    if (campaign.status === CampaignStatus.ACTIVE) {
      throw new Error('Campaign is already active');
    }

    if (campaign.status === CampaignStatus.COMPLETED) {
      throw new Error('Campaign is already completed');
    }

    // TODO: Add more validation rules
    // - Check if campaign has targets
    // - Check if script is available
    // - Check if user has sufficient credits
  }

  private mapToCampaignResult(campaign: DatabaseCampaign): CampaignResult {
    return {
      id: campaign.id,
      name: campaign.name,
      description: campaign.description || undefined,
      status: campaign.status as CampaignStatus,
      content: campaign.content as Record<string, unknown>,
      userId: campaign.user_id || '',
      tenantId: campaign.tenant_id || '',
      scriptId: campaign.script_id || undefined,
      projectId: campaign.project_id || undefined,
      createdAt: campaign.created_at || new Date(),
      updatedAt: campaign.updated_at || new Date(),
      metadata: {
        script: campaign.script,
        project: campaign.project,
        targets: campaign.targets
      }
    };
  }
} 