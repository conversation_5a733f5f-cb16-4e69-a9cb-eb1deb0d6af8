// Enhanced Campaign Script Loader
// Provides robust loading, caching, and validation of campaign scripts

import { CampaignScript, ScriptInfo } from './campaign-types.js';
import { logger } from '@/utils/logger';
import fs from 'fs';
import path from 'path';

// Script cache for performance optimization
const scriptCache = new Map<string, { script: CampaignScript; timestamp: number }>();
const CACHE_TIMEOUT = 5 * 60 * 1000; // 5 minutes

// Default campaign scripts for fallback
const DEFAULT_CAMPAIGNS: CampaignScript[] = [
  {
    id: 1,
    type: 'outbound',
    language: 'en',
    category: 'sales',
    title: 'Default Outbound Campaign',
    campaign: 'Sales Campaign',
    agentPersona: {
      name: 'Sales Agent',
      voice: '<PERSON><PERSON>',
      model: 'gemini-2.5-flash-preview-native-audio-dialog',
      tone: 'professional',
      humanEmulation: true
    }
  },
  {
    id: 1,
    type: 'incoming',
    language: 'en',
    category: 'support',
    title: 'Default Incoming Campaign',
    campaign: 'Customer Support',
    agentPersona: {
      name: 'Support Agent',
      voice: '<PERSON><PERSON>',
      model: 'gemini-2.5-flash-preview-native-audio-dialog',
      tone: 'helpful',
      humanEmulation: true
    }
  }
];

export function loadCampaignScript(
  campaignId: number,
  type: 'incoming' | 'outbound' = 'outbound',
  useCache: boolean = true
): CampaignScript | null {
  try {
    // PERMANENT FIX: Use correct file paths for campaign scripts
    let scriptPath: string;
    if (type === 'incoming') {
      scriptPath = path.join(process.cwd(), 'call-center-frontend', 'public', `incoming-campaign${campaignId}.json`);
    } else {
      scriptPath = path.join(process.cwd(), 'call-center-frontend', 'public', `campaign${campaignId}.json`);
    }

    logger.info(`🔍 [SCRIPT-LOADER] Attempting to load: ${scriptPath}`);
    logger.info(`🔍 [SCRIPT-LOADER] File exists: ${fs.existsSync(scriptPath)}`);

    if (fs.existsSync(scriptPath)) {
      try {
        const scriptContent = fs.readFileSync(scriptPath, 'utf-8');
        const script = JSON.parse(scriptContent) as CampaignScript;
        logger.info(`✅ [SCRIPT-LOADER] Successfully loaded campaign script from: ${scriptPath}`);
        return script;
      } catch (fileError) {
        logger.error(`❌ [SCRIPT-LOADER] Error reading/parsing file ${scriptPath}:`, fileError instanceof Error ? fileError : new Error(String(fileError)));
        return DEFAULT_CAMPAIGNS.find(c => c.type === type) || null;
      }
    }

    // Fallback to default
    logger.warn(`⚠️ [SCRIPT-LOADER] Campaign script not found: ${scriptPath}, using default`);
    return DEFAULT_CAMPAIGNS.find(c => c.type === type) || null;
  } catch (error) {
    logger.error('❌ [SCRIPT-LOADER] Error loading campaign script:', error instanceof Error ? error : new Error(String(error)));
    return DEFAULT_CAMPAIGNS.find(c => c.type === type) || null;
  }
}

export function getAllCampaigns(): CampaignScript[] {
  const campaigns: CampaignScript[] = [];
  
  try {
    // Load all campaigns from 1-6 for both types
    for (let i = 1; i <= 6; i++) {
      const outbound = loadCampaignScript(i, 'outbound');
      if (outbound) {campaigns.push(outbound);}
      
      const incoming = loadCampaignScript(i, 'incoming');
      if (incoming) {campaigns.push(incoming);}
    }
  } catch (error) {
    logger.error('Error loading all campaigns:', error instanceof Error ? error : new Error(String(error)));
  }
  
  return campaigns.length > 0 ? campaigns : DEFAULT_CAMPAIGNS;
}

export function formatCampaignScript(script: CampaignScript): string {
  if (!script) {return '';}
  
  let formatted = '';
  
  if (script.title) {
    formatted += `Campaign: ${script.title}\n`;
  }
  
  if (script.campaign) {
    formatted += `\n${script.campaign}\n`;
  }
  
  if (script.objectives) {
    formatted += `\nObjectives:\n${script.objectives.join('\n')}\n`;
  }
  
  return formatted;
}

// Placeholder functions for compatibility
export function getIncomingCallScript(scriptId: string): CampaignScript | null {
  const id = parseInt(scriptId) || 1;
  return loadCampaignScript(id, 'incoming');
}

export function listIncomingCallScripts(): ScriptInfo[] {
  const scripts: ScriptInfo[] = [];
  for (let i = 1; i <= 6; i++) {
    scripts.push({
      id: `incoming-${i}`,
      name: `Incoming Campaign ${i}`,
      description: `Customer support campaign ${i}`,
      type: 'incoming',
      category: 'support'
    });
  }
  return scripts;
}

export function setIncomingCallScript(scriptId: string): boolean {
  const id = parseInt(scriptId.replace(/\D/g, '')) || 1;
  if (id < 1 || id > 6) {
    logger.error(`❌ [SCRIPT-LOADER] Invalid script ID for setting: ${scriptId}`);
    return false;
  }
  
  // Validate that the script exists before setting it
  const script = loadCampaignScript(id, 'incoming');
  if (!script) {
    logger.error(`❌ [SCRIPT-LOADER] Script ${scriptId} not found, cannot set as current`);
    return false;
  }
  
  logger.info(`✅ [SCRIPT-LOADER] Setting incoming call script to: ${scriptId}`);
  return true;
}

export function getCurrentIncomingScript(): string {
  // Return numeric ID 7 as string which maps to incoming campaign 1 (7-6=1)
  // This fixes the "Non-numeric ID 'incoming-1', defaulting to campaign 1" error
  logger.debug('📋 [SCRIPT-LOADER] Returning current incoming script: 7 (incoming campaign 1)');
  return '7';
}

export function getCurrentIncomingScriptId(): number {
  // Return the actual numeric ID for proper type consistency in validation
  return 7;
}

export function createCustomIncomingScript(scriptData: any): boolean {
  logger.warn('⚠️ [SCRIPT-LOADER] Custom incoming scripts not supported - use campaigns 1-6');
  logger.debug('📋 [SCRIPT-LOADER] Received script data:', {
    hasData: !!scriptData,
    keys: scriptData ? Object.keys(scriptData) : []
  });
  return false;
}

// Incoming scenario functions
export function getIncomingScenario(scenarioId: string): CampaignScript | null {
  const id = parseInt(scenarioId.replace(/\D/g, '')) || 1;
  return loadCampaignScript(id, 'incoming');
}

export function listIncomingScenarios(): ScriptInfo[] {
  return listIncomingCallScripts();
}

export function setActiveIncomingScenario(scenarioId: string): boolean {
  return setIncomingCallScript(scenarioId);
}

export function getCurrentIncomingScenario(): string {
  return getCurrentIncomingScript();
}