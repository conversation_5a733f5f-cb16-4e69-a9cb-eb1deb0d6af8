import { websocketLogger } from '@/utils/logger';
import { globalHeartbeatManager } from './heartbeat-manager.js';
import type { WebSocketDependencies, ConnectionData } from './types/websocket.js';
import { timerManager } from '@/utils/timer-manager';

export async function endSession(
    sessionId: string, 
    deps: WebSocketDependencies, 
    reason: string = 'user_requested'
): Promise<void> {
    const {
        activeConnections,
        sessionManager,
        contextManager,
        summaryManager,
        transcriptionManager,
        lifecycleManager
    } = deps;

    websocketLogger.info(`Ending session`, { sessionId, reason });

    try {
        const connectionData = activeConnections.get(sessionId);
        if (!connectionData) {
            websocketLogger.warn(`Session not found for cleanup`, { sessionId });
            return;
        }

        // Stop heartbeat monitoring
        globalHeartbeatManager.stopHeartbeat(sessionId);

        // Clean up transcription
        if (transcriptionManager && connectionData.deepgramConnection) {
            transcriptionManager.closeTranscription(sessionId);
        }

        // Generate summary if requested
        if (summaryManager && connectionData.summaryRequested && !connectionData.summaryReceived) {
            try {
                await summaryManager.requestSummary(sessionId, connectionData, contextManager);
            } catch (error) {
                websocketLogger.error(`Error generating summary`, error instanceof Error ? error : new Error(String(error)));
            }
        }

        // Clean up Gemini session
        if (sessionManager && connectionData.geminiSession) {
            try {
                await sessionManager.cleanupSession(sessionId);
            } catch (error) {
                websocketLogger.error(`Error cleaning up Gemini session`, error instanceof Error ? error : new Error(String(error)));
            }
        }

        // Clean up context
        if (contextManager) {
            (contextManager as any).clearContext(sessionId);
        }

        // Remove from active connections
        activeConnections.delete(sessionId);

        websocketLogger.info(`Session ended successfully`, { sessionId, reason });

    } catch (error) {
        websocketLogger.error(`Error during session cleanup`, error instanceof Error ? error : new Error(String(error)));
    }
}

export function getConnectionData(
    sessionId: string, 
    activeConnections: Map<string, ConnectionData>
): ConnectionData | undefined {
    return activeConnections.get(sessionId);
}

export function updateConnectionActivity(
    sessionId: string, 
    activeConnections: Map<string, ConnectionData>
): void {
    const connectionData = activeConnections.get(sessionId);
    if (connectionData) {
        connectionData.lastActivity = Date.now();
    }
}

export function isSessionActive(
    sessionId: string,
    activeConnections: Map<string, ConnectionData>
): boolean {
    const connectionData = activeConnections.get(sessionId);
    return !!(connectionData && connectionData.isSessionActive);
}

export function scheduleRecovery(
    sessionId: string,
    reason: string,
    recoveryManager: any,
    activeConnections: Map<string, ConnectionData>,
    delay: number = 1000
): void {
    timerManager.setTimeout(`${sessionId}_recovery`, async () => {
        try {
            await recoveryManager.recoverSession(sessionId, reason, activeConnections);
        } catch (err) {
            websocketLogger.error('Error during scheduled recovery', err instanceof Error ? err : new Error(String(err)));
        }
    }, delay);
}
