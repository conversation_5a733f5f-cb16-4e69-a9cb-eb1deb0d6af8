// WebSocket performance optimizations to reduce session startup time
import { websocketLogger } from '@/utils/logger';
import type { ConnectionData } from './types/global.js';

export interface SessionStartupMetrics {
    connectionStart: number;
    authComplete: number;
    configLoaded: number;
    geminiSessionCreated: number;
    sessionReady: number;
    totalTime: number;
}

export interface OptimizedSessionConfig {
    enableFastStart: boolean;
    skipNonEssentialChecks: boolean;
    preloadGeminiSession: boolean;
    parallelInitialization: boolean;
    timeoutMs: number;
}

/**
 * Optimized session startup with performance tracking
 */
export class SessionStartupOptimizer {
    private metrics: Map<string, SessionStartupMetrics> = new Map();
    private config: OptimizedSessionConfig;

    constructor(config: OptimizedSessionConfig = {
        enableFastStart: true,
        skipNonEssentialChecks: false,
        preloadGeminiSession: false,
        parallelInitialization: true,
        timeoutMs: 10000 // 10 second timeout instead of 15+
    }) {
        this.config = config;
    }

    /**
     * Start tracking session startup performance
     */
    startTracking(sessionId: string): void {
        this.metrics.set(sessionId, {
            connectionStart: Date.now(),
            authComplete: 0,
            configLoaded: 0,
            geminiSessionCreated: 0,
            sessionReady: 0,
            totalTime: 0
        });
    }

    /**
     * Mark authentication complete
     */
    markAuthComplete(sessionId: string): void {
        const metrics = this.metrics.get(sessionId);
        if (metrics) {
            metrics.authComplete = Date.now();
        }
    }

    /**
     * Mark configuration loaded
     */
    markConfigLoaded(sessionId: string): void {
        const metrics = this.metrics.get(sessionId);
        if (metrics) {
            metrics.configLoaded = Date.now();
        }
    }

    /**
     * Mark Gemini session created
     */
    markGeminiSessionCreated(sessionId: string): void {
        const metrics = this.metrics.get(sessionId);
        if (metrics) {
            metrics.geminiSessionCreated = Date.now();
        }
    }

    /**
     * Mark session ready and calculate total time
     */
    markSessionReady(sessionId: string): SessionStartupMetrics | null {
        const metrics = this.metrics.get(sessionId);
        if (metrics) {
            metrics.sessionReady = Date.now();
            metrics.totalTime = metrics.sessionReady - metrics.connectionStart;
            
            // Log performance metrics
            this.logPerformanceMetrics(sessionId, metrics);
            
            return metrics;
        }
        return null;
    }

    /**
     * Get current metrics for a session
     */
    getMetrics(sessionId: string): SessionStartupMetrics | null {
        return this.metrics.get(sessionId) || null;
    }

    /**
     * Clean up metrics for completed session
     */
    cleanup(sessionId: string): void {
        this.metrics.delete(sessionId);
    }

    /**
     * Log performance metrics with warnings for slow operations
     */
    private logPerformanceMetrics(sessionId: string, metrics: SessionStartupMetrics): void {
        const authTime = metrics.authComplete - metrics.connectionStart;
        const configTime = metrics.configLoaded - metrics.authComplete;
        const geminiTime = metrics.geminiSessionCreated - metrics.configLoaded;
        const readyTime = metrics.sessionReady - metrics.geminiSessionCreated;

        websocketLogger.info(`Session startup performance for ${sessionId}:`, {
            totalTime: metrics.totalTime,
            authTime,
            configTime,
            geminiTime,
            readyTime
        });

        // Warn about slow operations
        if (metrics.totalTime > 10000) {
            websocketLogger.warn(`Slow session startup detected: ${metrics.totalTime}ms`, { sessionId });
        }
        if (authTime > 2000) {
            websocketLogger.warn(`Slow authentication: ${authTime}ms`, { sessionId });
        }
        if (configTime > 1000) {
            websocketLogger.warn(`Slow config loading: ${configTime}ms`, { sessionId });
        }
        if (geminiTime > 5000) {
            websocketLogger.warn(`Slow Gemini session creation: ${geminiTime}ms`, { sessionId });
        }
    }
}

/**
 * Optimized WebSocket connection handler with timeout protection
 */
export function createOptimizedConnectionHandler(
    originalHandler: Function,
    optimizer: SessionStartupOptimizer
): Function {
    return async function(connection: any, deps: any) {
        const sessionId = deps.sessionId || connection.sessionId || `session_${Date.now()}`;
        
        // Start performance tracking
        optimizer.startTracking(sessionId);

        // Set up timeout protection
        const timeoutId = setTimeout(() => {
            websocketLogger.error(`Session startup timeout after ${optimizer['config'].timeoutMs}ms`, { sessionId });
            if (connection.socket && connection.socket.readyState === 1) {
                connection.socket.send(JSON.stringify({
                    type: 'session-error',
                    error: 'Session startup timeout. Please try again.',
                    timeout: true
                }));
                connection.socket.close();
            }
        }, optimizer['config'].timeoutMs);

        try {
            // Call original handler with optimizations
            await originalHandler(connection, {
                ...deps,
                optimizer,
                sessionId
            });
            
            // Clear timeout if successful
            clearTimeout(timeoutId);
        } catch (error) {
            clearTimeout(timeoutId);
            websocketLogger.error(`Optimized connection handler error`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    };
}

/**
 * Parallel initialization helper for faster startup
 */
export async function parallelInitialization<T>(
    tasks: Array<() => Promise<T>>,
    timeoutMs: number = 5000
): Promise<T[]> {
    const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Parallel initialization timeout')), timeoutMs);
    });

    try {
        const results = await Promise.race([
            Promise.all(tasks.map(task => task())),
            timeoutPromise
        ]);
        return results;
    } catch (error) {
        websocketLogger.error('Parallel initialization failed', error instanceof Error ? error : new Error(String(error)));
        throw error;
    }
}

/**
 * Fast session configuration loader with caching
 */
export class FastConfigLoader {
    private configCache: Map<string, any> = new Map();
    private cacheTimeout: number = 30000; // 30 seconds

    /**
     * Load configuration with caching for better performance
     */
    async loadConfig(
        sessionId: string,
        configLoader: (callSid?: string) => Promise<any>,
        useCache: boolean = true
    ): Promise<any> {
        const cacheKey = `config_${sessionId}`;
        
        if (useCache && this.configCache.has(cacheKey)) {
            const cached = this.configCache.get(cacheKey);
            if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
                websocketLogger.debug(`Using cached config for ${sessionId}`);
                return cached.config;
            }
        }

        try {
            const config = await configLoader(sessionId);
            
            if (useCache) {
                this.configCache.set(cacheKey, {
                    config,
                    timestamp: Date.now()
                });
            }
            
            return config;
        } catch (error) {
            websocketLogger.error(`Config loading failed for ${sessionId}`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    /**
     * Clear expired cache entries
     */
    clearExpiredCache(): void {
        const now = Date.now();
        for (const [key, value] of this.configCache.entries()) {
            if ((now - value.timestamp) > this.cacheTimeout) {
                this.configCache.delete(key);
            }
        }
    }

    /**
     * Clear all cache
     */
    clearCache(): void {
        this.configCache.clear();
    }
}

/**
 * Connection quality monitor for early detection of issues
 */
export class ConnectionQualityMonitor {
    private qualityMetrics: Map<string, any> = new Map();

    /**
     * Start monitoring connection quality
     */
    startMonitoring(sessionId: string, ws: any): void {
        const metrics = {
            startTime: Date.now(),
            messageCount: 0,
            errorCount: 0,
            lastActivity: Date.now(),
            quality: 'good'
        };

        this.qualityMetrics.set(sessionId, metrics);

        // Monitor WebSocket events
        if (ws) {
            ws.on('message', () => {
                metrics.messageCount++;
                metrics.lastActivity = Date.now();
            });

            ws.on('error', () => {
                metrics.errorCount++;
                this.updateQuality(sessionId);
            });
        }
    }

    /**
     * Update connection quality based on metrics
     */
    private updateQuality(sessionId: string): void {
        const metrics = this.qualityMetrics.get(sessionId);
        if (!metrics) {return;}

        const now = Date.now();
        const timeSinceStart = now - metrics.startTime;
        const timeSinceActivity = now - metrics.lastActivity;

        if (metrics.errorCount > 3 || timeSinceActivity > 30000) {
            metrics.quality = 'poor';
        } else if (metrics.errorCount > 1 || timeSinceActivity > 15000) {
            metrics.quality = 'fair';
        } else {
            metrics.quality = 'good';
        }
    }

    /**
     * Get current connection quality
     */
    getQuality(sessionId: string): string {
        const metrics = this.qualityMetrics.get(sessionId);
        if (metrics) {
            this.updateQuality(sessionId);
            return metrics.quality;
        }
        return 'unknown';
    }

    /**
     * Stop monitoring and cleanup
     */
    stopMonitoring(sessionId: string): void {
        this.qualityMetrics.delete(sessionId);
    }
}
