import { getConfigValue } from './config/config.js';
import { websocketLogger } from '@/utils/logger';
import type { WebSocketDependencies, SessionConfig, GeminiVoice, GeminiModel } from './types/websocket.js';

// 1. OUTBOUND CALL Configuration
export async function getOutboundCallConfig(deps: WebSocketDependencies, callSid?: string): Promise<SessionConfig> {
    // CRITICAL FIX: Wait for stored config with retry mechanism to handle race conditions
    const logSid = callSid ? `[${callSid}] ` : '';
    websocketLogger.debug('[OUTBOUND] Waiting for stored configuration from make-call endpoint', { callSid });
    const configStartTime = Date.now();
    
    const storedConfig = await waitForStoredConfig(deps, callSid);
    if (storedConfig) {
        const configLoadTime = Date.now() - configStartTime;
        websocketLogger.info('[OUTBOUND] Using stored config from make-call', { callSid, scriptId: storedConfig.scriptId, loadTime: configLoadTime });
        
        // Validate AI instructions are present
        if (!storedConfig.aiInstructions || storedConfig.aiInstructions.trim().length === 0) {
            websocketLogger.error('[OUTBOUND] Stored config missing AI instructions - this will cause session creation to fail', {
                hasConfig: !!storedConfig,
                hasAiInstructions: !!storedConfig.aiInstructions,
                instructionLength: storedConfig.aiInstructions?.length || 0,
                scriptId: storedConfig.scriptId
            }, callSid);
        } else {
            websocketLogger.info('[OUTBOUND] Stored config has valid AI instructions', { callSid, instructionLength: storedConfig.aiInstructions.length });
        }
        
        return storedConfig;
    }
    
    websocketLogger.warn('[OUTBOUND] No stored config available after timeout, falling back to script manager', { callSid });
    const fallbackStartTime = Date.now();

    // Fallback: try to get current outbound script
    try {
        const currentScript = deps.scriptManager.getCurrentOutboundScript();
        if (currentScript) {
            const config = await deps.scriptManager.getScriptConfig(currentScript, false);
            if (config) {
                const fallbackLoadTime = Date.now() - fallbackStartTime;
                websocketLogger.info('[OUTBOUND] Using current outbound script', { callSid, scriptId: currentScript, loadTime: fallbackLoadTime });
                return {
                    ...config,
                    voice: config.voice as GeminiVoice,
                    model: config.model as GeminiModel,
                    scriptId: String(config.scriptId),
                    targetName: null,
                    targetPhoneNumber: null
                };
            }
        }
    } catch (error) {
        websocketLogger.warn('Error getting outbound script config', { error: error instanceof Error ? error.message : String(error) }, callSid);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice(deps.GEMINI_DEFAULT_VOICE);
    const validModel = deps.modelManager.getValidGeminiModel(deps.GEMINI_DEFAULT_MODEL);

    // Last resort: default outbound config
    websocketLogger.warn('[OUTBOUND] Using fallback config - no script found', { callSid });
    return {
        aiInstructions: '', // Campaign script should provide all instructions
        voice: validVoice as GeminiVoice,
        model: validModel as GeminiModel,
        targetName: null,
        targetPhoneNumber: null,
        scriptType: 'outbound',
        scriptId: 'default'
    };
}

// Helper function to wait with retry logic for stored configuration
async function waitForStoredConfig(deps: WebSocketDependencies, callSid?: string, maxRetries: number = 3, retryDelayMs: number = 0): Promise<any> {
    let attempts = 0;
    
    while (attempts < maxRetries) {
        if (deps.getNextCallConfig) {
            // ATOMIC RETRIEVAL: Try call-specific config first, then fallback to global
            const storedConfig = await deps.getNextCallConfig();
            if (storedConfig && storedConfig.aiInstructions) {
                const configSource = callSid ? 'call-specific' : 'global';
                websocketLogger.debug('Configuration found', { configSource, attempts }, callSid);
                return storedConfig;
            }
        }
        
        attempts++;
        if (attempts < maxRetries) {
            // Immediate retry without artificial delays - yield to event loop only
            await new Promise(resolve => process.nextTick(resolve));
        }
    }
    
    websocketLogger.warn('Configuration not available after immediate retries', { callSid, maxRetries });
    return null;
}

// 2. INBOUND CALL Configuration
export async function getInboundCallConfig(deps: WebSocketDependencies, callSid?: string): Promise<SessionConfig> {
    const storedConfig = deps.getNextCallConfig ? await deps.getNextCallConfig() : null;
    if (storedConfig) {
        websocketLogger.info('[INBOUND] Using stored config from webhook', { callSid, scriptId: storedConfig.scriptId || 'unknown' });
        
        // Validate AI instructions are present
        if (!storedConfig.aiInstructions || storedConfig.aiInstructions.trim().length === 0) {
            websocketLogger.error('[INBOUND] Stored config missing AI instructions - this will cause session creation to fail', {
                hasConfig: !!storedConfig,
                hasAiInstructions: !!storedConfig.aiInstructions,
                instructionLength: storedConfig.aiInstructions?.length || 0,
                scriptId: storedConfig.scriptId
            }, callSid);
        } else {
            websocketLogger.info('[INBOUND] Stored config has valid AI instructions', { callSid, instructionLength: storedConfig.aiInstructions.length });
        }
        
        // Ensure it's marked as inbound call
        storedConfig.isIncomingCall = true;
        storedConfig.scriptType = storedConfig.scriptType || 'incoming';
        return storedConfig;
    }
    
    websocketLogger.warn('[INBOUND] No stored config available, falling back to script manager', { callSid });
    const fallbackStartTime = Date.now();

    // Fallback: Try to get current incoming script
    try {
        const currentScript = deps.scriptManager.getCurrentIncomingScript();
        if (currentScript) {
            const scriptConfig = await deps.scriptManager.getScriptConfig(currentScript, true);
            if (scriptConfig && scriptConfig.aiInstructions) {
                const fallbackLoadTime = Date.now() - fallbackStartTime;
                websocketLogger.info('[INBOUND] Using current incoming script', { callSid, scriptId: currentScript, loadTime: fallbackLoadTime });
                return {
                    ...scriptConfig,
                    voice: scriptConfig.voice as GeminiVoice,
                    model: scriptConfig.model as GeminiModel,
                    scriptId: String(scriptConfig.scriptId)
                };
            }
        }
    } catch (error) {
        websocketLogger.warn('Error getting inbound script config', { error: error instanceof Error ? error.message : String(error) }, callSid);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice('Kore'); // Default voice for inbound
    const validModel = deps.modelManager.getValidGeminiModel(deps.GEMINI_DEFAULT_MODEL);

    // Last resort: Try to load a default incoming campaign script
    try {
        // Load campaign script 7 (incoming campaign 1) as default
        const defaultScript = await deps.scriptManager.getScriptConfig(7, true); // ID 7 = incoming campaign 1
        if (defaultScript && defaultScript.aiInstructions) {
            websocketLogger.info('[INBOUND] Using default incoming campaign script 1', { callSid, instructionLength: defaultScript.aiInstructions.length });
            websocketLogger.debug('[INBOUND] Default script preview', { callSid, preview: defaultScript.aiInstructions.substring(0, 200) + '...' });
            return {
                ...defaultScript,
                voice: defaultScript.voice as GeminiVoice,
                model: defaultScript.model as GeminiModel,
                scriptId: String(defaultScript.scriptId)
            };
        } else {
            websocketLogger.error('[INBOUND] Default script 7 loaded but has no AI instructions', {
                hasScript: !!defaultScript,
                hasInstructions: !!defaultScript?.aiInstructions,
                instructionLength: defaultScript?.aiInstructions?.length || 0
            }, callSid);
        }
    } catch (scriptError) {
        websocketLogger.error('Error loading default incoming campaign script', scriptError instanceof Error ? scriptError : new Error(String(scriptError)), callSid);
    }

    // Final fallback to minimal config (should not happen in production)
    websocketLogger.warn('[INBOUND] Using minimal fallback config - this should not happen in production!', { callSid });
    return {
        aiInstructions: 'You are a helpful customer service representative. Greet the caller warmly and ask how you can help them today.',
        voice: validVoice as GeminiVoice,
        model: validModel as GeminiModel,
        targetName: null,
        targetPhoneNumber: null,
        scriptType: 'incoming',
        scriptId: 'customer-service'
    };
}

// 3. OUTBOUND TESTING Configuration
export async function getOutboundTestConfig(deps: WebSocketDependencies): Promise<SessionConfig> {
    try {
        websocketLogger.debug('DEBUG: Getting current outbound script', {});
        const currentScript = deps.scriptManager.getCurrentOutboundScript();
        websocketLogger.debug('DEBUG: Current outbound script', { hasScript: !!currentScript, scriptId: currentScript });
        if (currentScript) {
            websocketLogger.debug('DEBUG: Getting script config for outbound ID', { scriptId: currentScript });
            const config = await deps.scriptManager.getScriptConfig(currentScript, false);
            websocketLogger.debug('DEBUG: Outbound script config result', { hasConfig: !!config, instructionLength: config?.aiInstructions?.length });
            if (config && config.aiInstructions) {
                websocketLogger.info('[OUTBOUND TEST] Using real campaign script', { preview: config.aiInstructions.substring(0, 100) + '...' });
                
                // Validate AI instructions before returning
                if (config.aiInstructions.trim().length === 0) {
                    websocketLogger.error('[OUTBOUND TEST] Campaign script has empty AI instructions', {});
                    throw new Error('Outbound test config has empty AI instructions');
                }
                
                return {
                    ...config,
                    voice: config.voice as GeminiVoice,
                    model: config.model as GeminiModel,
                    scriptId: String(config.scriptId),
                    isTestMode: true
                };
            }
        }
    } catch (error) {
        websocketLogger.warn('Error getting outbound test config', { error: error instanceof Error ? error.message : String(error) });
    }

    // CRITICAL FIX: Load real campaign script instead of generic instructions
    websocketLogger.debug('[OUTBOUND TEST] Loading real campaign script as fallback', {});

    try {
        // Load campaign script 1 (outbound) as default for testing
        const scriptStartTime = Date.now();
        const campaignScript = await deps.scriptManager.getScriptConfig(1, false); // ID 1 = outbound campaign 1
        const scriptLoadTime = Date.now() - scriptStartTime;
        websocketLogger.debug('[OUTBOUND TEST] Script loading time', { loadTimeMs: scriptLoadTime });
        
        if (campaignScript && campaignScript.aiInstructions) {
            websocketLogger.info('[OUTBOUND TEST] Using real campaign script 1', { preview: campaignScript.aiInstructions.substring(0, 100) + '...' });
            
            // Validate AI instructions before returning
            if (campaignScript.aiInstructions.trim().length === 0) {
                websocketLogger.error('[OUTBOUND TEST] Campaign script 1 has empty AI instructions', {});
                throw new Error('Outbound test fallback config has empty AI instructions');
            }
            
            return {
                ...campaignScript,
                voice: campaignScript.voice as GeminiVoice,
                model: campaignScript.model as GeminiModel,
                scriptId: String(campaignScript.scriptId),
                isTestMode: true
            };
        }
    } catch (scriptError) {
        websocketLogger.error('Error loading outbound campaign script', scriptError instanceof Error ? scriptError : new Error(String(scriptError)));
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice(deps.GEMINI_DEFAULT_VOICE);
    const validModel = deps.modelManager.getValidGeminiModel(deps.GEMINI_DEFAULT_MODEL);

    // DEBUG: Check what the model manager is actually returning
    websocketLogger.debug('DEBUG: Model configuration', {
        defaultModel: deps.GEMINI_DEFAULT_MODEL,
        validModel,
        modelManagerDefault: deps.modelManager.getDefaultModel(),
        envModel: process.env.GEMINI_DEFAULT_MODEL
    });

    // CRITICAL ERROR: No campaign script available - this should not happen
    websocketLogger.error('[OUTBOUND TEST] CRITICAL: No campaign script could be loaded! Testing requires valid scripts.', {});
    throw new Error('No outbound campaign script available for testing. Please ensure campaign scripts are properly configured.');
}

// 4. INBOUND TESTING Configuration
export async function getInboundTestConfig(deps: WebSocketDependencies): Promise<SessionConfig> {
    websocketLogger.info('🎯 [INBOUND_TEST_CONFIG] Starting inbound test configuration', {});
    
    try {
        websocketLogger.info('🔍 [INBOUND_TEST_CONFIG] Getting current incoming script', {});
        const currentScript = deps.scriptManager.getCurrentIncomingScript();
        websocketLogger.info('📋 [INBOUND_TEST_CONFIG] Current script result', {
            hasScript: !!currentScript,
            scriptId: currentScript,
            scriptType: typeof currentScript
        });
        
        if (currentScript) {
            websocketLogger.info('📋 [INBOUND_TEST_CONFIG] Loading script config', { scriptId: currentScript });
            const config = await deps.scriptManager.getScriptConfig(currentScript, true);
            websocketLogger.info('✅ [INBOUND_TEST_CONFIG] Script config loaded', {
                hasConfig: !!config,
                hasAiInstructions: !!config?.aiInstructions,
                instructionLength: config?.aiInstructions?.length || 0
            });
            
            if (config && config.aiInstructions) {
                websocketLogger.info('🎯 [INBOUND_TEST_CONFIG] Using current campaign script', {
                    scriptId: currentScript,
                    preview: config.aiInstructions.substring(0, 200) + '...'
                });
                
                // Validate AI instructions before returning
                if (config.aiInstructions.trim().length === 0) {
                    websocketLogger.error('❌ [INBOUND_TEST_CONFIG] Campaign script has empty AI instructions', {});
                    throw new Error('Inbound test config has empty AI instructions');
                }
                
                return {
                    ...config,
                    voice: config.voice as GeminiVoice,
                    model: config.model as GeminiModel,
                    scriptId: String(config.scriptId),
                    isTestMode: true
                };
            }
        }
    } catch (error) {
        websocketLogger.error('❌ [INBOUND_TEST_CONFIG] Error getting current script config',
            error instanceof Error ? error : new Error(String(error))
        );
    }

    // CRITICAL FIX: Load real campaign script instead of empty instructions
    websocketLogger.info('🔄 [INBOUND_TEST_CONFIG] Loading fallback campaign script', {});

    try {
        // Load campaign script 1 (incoming) as default for testing
        websocketLogger.info('📋 [INBOUND_TEST_CONFIG] Attempting to load incoming campaign script 1 (ID=7)', {});
        const campaignScript = await deps.scriptManager.getScriptConfig(7, true); // ID 7 = incoming campaign 1
        
        websocketLogger.info('📋 [INBOUND_TEST_CONFIG] Fallback script result', {
            hasScript: !!campaignScript,
            hasAiInstructions: !!campaignScript?.aiInstructions,
            instructionLength: campaignScript?.aiInstructions?.length || 0
        });
        
        if (campaignScript && campaignScript.aiInstructions) {
            websocketLogger.info('✅ [INBOUND_TEST_CONFIG] Using fallback campaign script 1', {
                scriptId: campaignScript.scriptId,
                preview: campaignScript.aiInstructions.substring(0, 200) + '...'
            });
            
            // Validate AI instructions before returning
            if (campaignScript.aiInstructions.trim().length === 0) {
                websocketLogger.error('❌ [INBOUND_TEST_CONFIG] Campaign script 1 has empty AI instructions', {});
                throw new Error('Inbound test fallback config has empty AI instructions');
            }
            
            return {
                ...campaignScript,
                voice: campaignScript.voice as GeminiVoice,
                model: campaignScript.model as GeminiModel,
                scriptId: String(campaignScript.scriptId),
                isTestMode: true
            };
        } else {
            websocketLogger.error('❌ [INBOUND_TEST_CONFIG] Fallback campaign script 1 missing or has no AI instructions', {
                hasScript: !!campaignScript,
                hasAiInstructions: !!campaignScript?.aiInstructions
            });
        }
    } catch (scriptError) {
        websocketLogger.error('❌ [INBOUND_TEST_CONFIG] Error loading fallback campaign script',
            scriptError instanceof Error ? scriptError : new Error(String(scriptError))
        );
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice(getConfigValue<string>('ai.gemini.defaultVoice', 'Kore') || 'Kore');
    const validModel = deps.modelManager.getValidGeminiModel(getConfigValue<string>('ai.gemini.defaultModel') || 'gemini-2.5-flash-preview-native-audio-dialog');

    // CRITICAL ERROR: No campaign script available - this should not happen
    websocketLogger.error('[INBOUND TEST] CRITICAL: No campaign script could be loaded! Testing requires valid scripts.', {});
    throw new Error('No inbound campaign script available for testing. Please ensure campaign scripts are properly configured.');
}