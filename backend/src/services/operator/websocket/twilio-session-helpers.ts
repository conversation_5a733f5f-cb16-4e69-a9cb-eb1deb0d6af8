// Helper functions for Twilio session management to reduce complexity
import { ConnectionData } from './types/global.js';
import { FlowDependencies } from './types/websocket.js';
import { websocketLogger } from '@/utils/logger';
import type { WebSocket } from 'ws';

export interface TwilioStreamInfo {
    streamSid?: string;
    accountSid?: string;
    twilioCallSid?: string;
}

export interface TwilioSessionConfig {
    aiInstructions: string;
    voice: string;
    model: string;
    targetName?: string;
    targetPhoneNumber?: string;
    scriptId?: string;
    [key: string]: unknown;
}

export interface SessionConfigResult {
    config: TwilioSessionConfig | null;
    isValid: boolean;
}

/**
 * Extracts Twilio stream information from start message
 */
export function extractTwilioStreamInfo(data: Record<string, unknown> | { start?: Record<string, unknown> }): TwilioStreamInfo {
    const start = data.start as Record<string, unknown> | undefined;
    return {
        streamSid: start?.streamSid as string | undefined,
        accountSid: start?.accountSid as string | undefined,
        twilioCallSid: start?.callSid as string | undefined
    };
}

/**
 * Validates and gets session configuration with fallback
 */
export async function getValidSessionConfig(
    callSid: string,
    getSessionConfig: (callSid?: string) => any,
    deps: FlowDependencies,
    isIncomingCall: boolean
): Promise<SessionConfigResult> {
    websocketLogger.debug('Starting session config validation', { callSid, incoming: isIncomingCall });

    try {
        let sessionConfig = await getSessionConfig(callSid);

        // DETAILED LOGGING: Log initial config retrieval
        if (sessionConfig) {
            websocketLogger.info(`[${callSid}] Initial session config retrieved`, {
                hasConfig: !!sessionConfig,
                hasAiInstructions: !!sessionConfig.aiInstructions,
                instructionLength: sessionConfig.aiInstructions?.length || 0,
                scriptId: sessionConfig.scriptId,
                scriptType: sessionConfig.scriptType,
                voice: sessionConfig.voice,
                model: sessionConfig.model
            });

            if (sessionConfig.aiInstructions) {
                const preview = sessionConfig.aiInstructions.substring(0, 200);
                websocketLogger.debug('AI Instructions preview', { callSid, preview });
            }
        } else {
            websocketLogger.warn('No initial session config found from getSessionConfig', { callSid });
        }

        // Ensure we have a valid session config
        if (!sessionConfig || !sessionConfig.aiInstructions) {
            websocketLogger.warn('No valid session config found, attempting fallback mechanism', { callSid });

            // Try to get current script as fallback
            try {
                websocketLogger.debug('Attempting fallback script retrieval', { callSid });
                const currentScript = isIncomingCall
                    ? deps.scriptManager.getCurrentIncomingScript()
                    : deps.scriptManager.getCurrentOutboundScript();

                websocketLogger.debug('Current script from manager', { callSid, currentScript });

                if (currentScript) {
                    websocketLogger.debug('Loading script config for fallback script', { callSid, scriptId: currentScript });
                    sessionConfig = await deps.scriptManager.getScriptConfig(currentScript, isIncomingCall);

                    if (sessionConfig) {
                        websocketLogger.info('Fallback script config loaded', {
                            callSid,
                            scriptId: currentScript,
                            hasAiInstructions: !!sessionConfig.aiInstructions,
                            instructionLength: sessionConfig.aiInstructions?.length || 0,
                            scriptType: sessionConfig.scriptType
                        });

                        if (sessionConfig.aiInstructions) {
                            const preview = sessionConfig.aiInstructions.substring(0, 200);
                            websocketLogger.debug('Fallback AI Instructions preview', { callSid, preview });
                        }
                    } else {
                        websocketLogger.error(`Fallback script config is null for script ID: ${currentScript}`, {}, callSid);
                    }
                } else {
                    websocketLogger.error('No current script available from script manager', {}, callSid);
                }
            } catch (error) {
                websocketLogger.error('Error getting fallback script', error instanceof Error ? error : new Error(String(error)), callSid);
                websocketLogger.error('Fallback error details', {
                    message: error instanceof Error ? error.message : String(error),
                    stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined
                }, callSid);
            }
        }

        // Final validation
        const isValid = !!(sessionConfig && sessionConfig.aiInstructions && sessionConfig.aiInstructions.trim().length > 0);

        websocketLogger.debug('Final session config validation', {
            hasConfig: !!sessionConfig,
            hasAiInstructions: !!sessionConfig?.aiInstructions,
            instructionLength: sessionConfig?.aiInstructions?.length || 0,
            isValid: isValid,
            scriptId: sessionConfig?.scriptId,
            configSource: sessionConfig ? 'loaded' : 'none'
        }, callSid);

        if (!isValid) {
            websocketLogger.error('CRITICAL: Session config validation failed - no valid AI instructions found', {}, callSid);
            websocketLogger.error('This will cause Gemini session creation to fail', {}, callSid);
        }

        return {
            config: sessionConfig,
            isValid: isValid
        };
    } catch (error) {
        websocketLogger.error('Error getting session config', error instanceof Error ? error : new Error(String(error)), callSid);
        websocketLogger.error('Config error details', {
            message: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined
        }, callSid);
        return {
            config: null,
            isValid: false
        };
    }
}

/**
 * Creates enhanced connection data for Twilio sessions
 */
export function createTwilioConnectionData(
    callSid: string,
    ws: WebSocket,
    streamInfo: TwilioStreamInfo,
    sessionConfig: TwilioSessionConfig,
    isIncomingCall: boolean,
    flowType: string
): ConnectionData {
    return {
        ws: ws, // Standardized WebSocket property name
        twilioWs: ws, // Also store as twilioWs for audio forwarding compatibility
        callSid,
        sessionId: callSid,
        streamSid: streamInfo.streamSid, // Add streamSid for audio forwarding
        sequenceNumber: 0, // Initialize sequence number for Twilio audio packets
        isSessionActive: false,
        summaryRequested: false,
        summaryReceived: false,
        summaryText: '',
        conversationLog: [],
        fullTranscript: [],
        speechTranscript: [],
        isIncomingCall,
        sessionType: 'twilio_call',
        flowType,
        sessionStartTime: Date.now(),
        lastActivity: Date.now(),
        targetName: sessionConfig.targetName || 'Contact',
        targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
        originalAIInstructions: sessionConfig.aiInstructions,
        scriptId: sessionConfig.scriptId || 'default',
        isTwilioCall: true,
        // Add missing properties to match testing flows
        lastAIResponse: Date.now(), // Track AI responsiveness
        responseTimeouts: 0, // Count consecutive timeouts
        connectionQuality: 'good', // Track connection quality
        lastContextSave: Date.now(), // For periodic context saving
        contextSaveInterval: null, // For periodic context saving
        // Audio forwarding properties
        audioForwardingEnabled: true, // Enable audio forwarding for Twilio calls
        lastAudioSent: 0 // Track last audio packet sent time
    };
}

/**
 * Logs Twilio stream information
 */
export function logTwilioStreamInfo(
    callSid: string,
    streamInfo: TwilioStreamInfo,
    sessionConfig: TwilioSessionConfig
): void {
    websocketLogger.debug('Twilio stream info', {
        callSid,
        streamSid: streamInfo.streamSid,
        accountSid: streamInfo.accountSid,
        twilioCallSid: streamInfo.twilioCallSid,
        configFound: !!sessionConfig,
        hasInstructions: !!sessionConfig?.aiInstructions
    });
}

/**
 * Logs session configuration details
 */
export function logSessionConfiguration(
    callSid: string,
    sessionConfig: TwilioSessionConfig,
    flowType: string
): void {
    websocketLogger.debug('Session config details', {
        callSid,
        flowType,
        hasInstructions: !!sessionConfig.aiInstructions,
        instructionsLength: sessionConfig.aiInstructions?.length || 0,
        scriptId: sessionConfig.scriptId,
        scriptType: sessionConfig.scriptType,
        voice: sessionConfig.voice,
        model: sessionConfig.model,
        isIncomingCall: sessionConfig.isIncomingCall
    });
}

/**
 * Starts lifecycle management for a session
 */
export function startLifecycleManagement(
    callSid: string,
    lifecycleManager: any,
    connectionData: ConnectionData,
    sessionConfig: any
): void {
    if (lifecycleManager) {
        lifecycleManager.initializeSession(callSid, { connectionData, sessionConfig });
        websocketLogger.info('Lifecycle management started', { callSid });
    }
}

/**
 * Starts WebSocket heartbeat monitoring for Twilio
 */
export function startTwilioHeartbeat(
    callSid: string,
    ws: any,
    globalHeartbeatManager: any,
    activeConnections: Map<string, ConnectionData>,
    config: any
): void {
    // Note: Twilio WebSockets may not respond to standard pings, so use longer timeouts
    globalHeartbeatManager.startHeartbeat(
        callSid,
        ws,
        config.websocket.heartbeatInterval,
        config.websocket.heartbeatTimeout,
        (sessionId: string, ws: any) => {
            websocketLogger.warn('Twilio WebSocket heartbeat timeout - connection may be stale', { callSid });
            // Don't immediately end session on heartbeat timeout for Twilio
            // Mark connection as potentially stale but let call status webhook handle cleanup
            const connectionData = activeConnections.get(callSid);
            if (connectionData) {
                connectionData.heartbeatTimeout = true;
                connectionData.lastHeartbeatTimeout = Date.now();
                websocketLogger.info('Marked Twilio connection as having heartbeat timeout', { callSid });
            }
        }
    );
}

/**
 * Sends session started message to WebSocket
 */
export function sendSessionStartedMessage(
    ws: any,
    callSid: string,
    flowType: string,
    scriptId: string
): void {
    ws.send(JSON.stringify({
        type: 'session-started',
        callSid,
        flowType,
        scriptId
    }));
}

/**
 * Handles Gemini session creation failure
 */
export async function handleGeminiSessionFailure(
    callSid: string,
    flowType: string,
    ws: any,
    deps: FlowDependencies,
    endSession: (callSid: string, deps: FlowDependencies, reason: string) => void
): Promise<void> {
    websocketLogger.error(`Failed to create Gemini session for ${flowType} call`, { callSid });
    
    ws.send(JSON.stringify({
        type: 'session-error',
        error: 'Failed to initialize AI session. Please try again later.',
        critical: true
    }));
    
    // For Twilio calls, we should end the call gracefully
    if (deps.twilioHelper) {
        try {
            await deps.twilioHelper.endCallWithMessage(
                callSid, 
                'We apologize, but we are unable to process your call at this time. Please try again later.'
            );
        } catch (err) {
            websocketLogger.error(
                'Failed to end call with error message', 
                err instanceof Error ? err : new Error(String(err))
            );
        }
    }

    // Clean up and close connection
    endSession(callSid, deps, 'gemini_session_failed');
    ws.close();
}

/**
 * Handles session start errors
 */
export function handleSessionStartError(
    error: unknown,
    flowType: string,
    ws: any
): void {
    websocketLogger.error(
        `Error starting Twilio ${flowType} session`, 
        error instanceof Error ? error : new Error(String(error))
    );
    
    ws.send(JSON.stringify({
        type: 'session-error',
        error: `Session start failed: ${(error as Error).message}`
    }));
}
