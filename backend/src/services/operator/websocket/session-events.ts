import { endSession } from './session-utils.js';
import type { ConnectionData, WebSocketDependencies } from './types/websocket.js';

export async function handleEndSession(
    sessionId: string, 
    deps: WebSocketDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    lifecycleManager: any
): Promise<void> {
    const connectionData = activeConnections.get(sessionId);
    if (connectionData) {
        if (connectionData.deepgramConnection) {
            deps.transcriptionManager.closeTranscription(sessionId);
        }
        if (lifecycleManager) {
            await lifecycleManager.endSession(sessionId, connectionData, 'user_end_testing');
        } else {
            await endSession(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
        }
    } else {
        await endSession(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
    }
}

export async function handleRequestSummary(
    sessionId: string, 
    _deps: WebSocketDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    summaryManager: any, 
    contextManager: any
): Promise<void> {
    const summaryConnectionData = activeConnections.get(sessionId);
    if (summaryConnectionData && summaryManager) {
        await summaryManager.requestSummary(sessionId, summaryConnectionData, contextManager);
    }
}