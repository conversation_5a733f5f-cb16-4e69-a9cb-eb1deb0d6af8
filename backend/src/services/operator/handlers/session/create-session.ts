import { EventDrivenComponent } from '@/modules/operator/event-system/event-patterns';
import { sessionLogger } from '@/utils/logger';
import { 
    GeminiSession, 
    GeminiClient,
    ConnectionData,
    ExtendedConnectionData
} from '../../types/global.js';
import { SessionConfig } from '../../types/websocket.js';

export class SessionCreator extends EventDrivenComponent {
    constructor(private geminiClient: GeminiClient) {
        super('SessionCreator');
    }

    async createSession(
        callSid: string,
        config: SessionConfig,
        connectionData: ConnectionData
    ): Promise<GeminiSession | null> {
        const extConnectionData = connectionData as ExtendedConnectionData;
        
        try {
            // Validate AI instructions
            this.validateInstructions(callSid, config);
            
            // Build system instruction
            const systemInstruction = this.buildSystemInstruction(callSid, config);
            
            // Create Gemini session
            const geminiSession = await this.connectToGemini(
                callSid, 
                config, 
                systemInstruction,
                extConnectionData
            );
            
            if (geminiSession) {
                this.emit('session:created', {
                    sessionId: callSid,
                    phoneNumber: connectionData.phoneNumber,
                    timestamp: Date.now()
                });
            }
            
            return geminiSession;
            
        } catch (error) {
            this.emit('session:error', {
                sessionId: callSid,
                error: error as Error,
                context: 'session_creation'
            });
            
            extConnectionData.isSessionActive = false;
            // Store error in a different way since geminiSessionError doesn't exist on type
            (extConnectionData as any).geminiSessionError = error instanceof Error 
                ? error.message 
                : 'Session creation failed';
                
            throw error;
        }
    }

    private validateInstructions(callSid: string, config: SessionConfig): void {
        if (!config.aiInstructions || config.aiInstructions.trim().length === 0) {
            throw new Error('AI instructions are missing or empty');
        }
        
        if (config.aiInstructions.trim().length < 100) {
            throw new Error(
                `AI instructions too short (${config.aiInstructions.length} chars) - minimum 100 required`
            );
        }
        
        sessionLogger.info(`✅ [${callSid}] AI instructions validation passed (${config.aiInstructions.length} chars)`);
    }

    private buildSystemInstruction(callSid: string, config: SessionConfig): string {
        let systemInstruction = config.aiInstructions;
        
        // Add context for Twilio calls
        if (config.sessionType === 'twilio_call' && systemInstruction) {
            if (!config.isIncomingCall) {
                systemInstruction += '\n\nYou are making an outbound call. When the call connects, ' +
                    'introduce yourself and begin the conversation according to the script above. ' +
                    'Start speaking immediately when the call is answered.';
            } else {
                systemInstruction += '\n\nYou are receiving an inbound call. When the customer speaks, ' +
                    'greet them warmly and assist them according to the script above.';
            }
        }
        
        // Fallback for inbound calls without instructions
        if (!systemInstruction && config.isIncomingCall && config.sessionType === 'twilio_call') {
            sessionLogger.warn(`[${callSid}] No AI instructions for inbound call, using fallback`);
            systemInstruction = 'You are a helpful customer service representative. ' +
                'Greet the caller warmly and ask how you can help them today.';
        }
        
        return systemInstruction;
    }

    private async connectToGemini(
        callSid: string,
        config: SessionConfig,
        systemInstruction: string,
        connectionData: ExtendedConnectionData
    ): Promise<GeminiSession> {
        sessionLogger.info(`🚀 [${callSid}] Connecting to Gemini Live API...`);
        
        const geminiSession = await (this.geminiClient as any).live.connect({
            model: config.model,
            systemInstruction: systemInstruction ? {
                parts: [{ text: systemInstruction }]
            } : undefined,
            generationConfig: {
                responseModalities: ['AUDIO'],
                speechConfig: {
                    voiceConfig: {
                        prebuiltVoiceConfig: {
                            voiceName: config.voice
                        }
                    }
                }
            }
        });
        
        // Store session reference
        connectionData.geminiSession = geminiSession;
        // connectionData.sessionConfig = config; // TODO: Add to type definition if needed
        
        return geminiSession;
    }
}