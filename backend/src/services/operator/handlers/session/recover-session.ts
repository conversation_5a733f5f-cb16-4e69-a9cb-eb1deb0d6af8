import { EventDrivenComponent } from '@/modules/operator/event-system/event-patterns';
import { sessionLogger } from '@/utils/logger';
import { BoundedSet } from '../metrics.js';
import { recoverSession as recoveryHandler } from '../recovery.js';

export class SessionRecoveryHandler extends EventDrivenComponent {
    private recoveryInProgress: BoundedSet<string>;
    
    constructor() {
        super('SessionRecoveryHandler');
        this.recoveryInProgress = new BoundedSet<string>(500);
        
        // Subscribe to recovery events
        this.subscribe('session:recovery:started', this.handleRecoveryStarted.bind(this));
        this.subscribe('session:recovery:completed', this.handleRecoveryCompleted.bind(this));
    }
    
    async recoverSession(callSid: string, reason: string): Promise<void> {
        if (this.recoveryInProgress.has(callSid)) {
            sessionLogger.warn(`[${callSid}] Recovery already in progress`);
            return;
        }
        
        this.recoveryInProgress.add(callSid);
        
        this.emit('session:recovery:started', {
            sessionId: callSid,
            attempt: 1
        });
        
        try {
            // Create required dependencies for recovery
            const contextManager = new (await import('../../session/context-manager.js')).ContextManager();
            const sessionMetrics = new Map();
            const recoverySet = new Set<string>();
            
            await recoveryHandler(callSid, reason, contextManager, sessionMetrics, recoverySet);
            
            this.emit('session:recovery:completed', {
                sessionId: callSid,
                success: true
            });
        } catch (error) {
            sessionLogger.error(`[${callSid}] Recovery failed:`, error as Error);
            
            this.emit('session:recovery:completed', {
                sessionId: callSid,
                success: false
            });
            
            this.emit('session:error', {
                sessionId: callSid,
                error: error as Error,
                context: 'session_recovery'
            });
        } finally {
            this.recoveryInProgress.delete(callSid);
        }
    }
    
    private handleRecoveryStarted(payload: { sessionId: string; attempt: number }): void {
        sessionLogger.info(`🔄 Recovery started for session ${payload.sessionId}, attempt ${payload.attempt}`);
    }
    
    private handleRecoveryCompleted(payload: { sessionId: string; success: boolean }): void {
        const status = payload.success ? '✅ succeeded' : '❌ failed';
        sessionLogger.info(`Recovery ${status} for session ${payload.sessionId}`);
    }
    
    isRecoveryInProgress(callSid: string): boolean {
        return this.recoveryInProgress.has(callSid);
    }
}