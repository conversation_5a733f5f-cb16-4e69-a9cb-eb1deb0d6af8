import { EventDrivenComponent } from '@/modules/operator/event-system/event-patterns';
import { sessionLogger } from '@/utils/logger';
import { timerManager } from '../../utils/timer-manager.js';
import { cleanupAudioForwarding } from '../audio/audio-forwarding.js';
import { BoundedMap } from '../metrics.js';

export class Session<PERSON>leanupHandler extends EventDrivenComponent {
    private cleanupLocks: BoundedMap<string, Promise<void>>;
    
    constructor() {
        super('SessionCleanupHandler');
        this.cleanupLocks = new BoundedMap(200);
        
        // Subscribe to session end events
        this.subscribe('session:ended', this.handleSessionEnded.bind(this));
    }
    
    async cleanupSession(callSid: string): Promise<void> {
        // Prevent concurrent cleanup
        if (this.cleanupLocks.has(callSid)) {
            sessionLogger.info(`[${callSid}] Cleanup already in progress`);
            await this.cleanupLocks.get(callSid);
            return;
        }
        
        const cleanupPromise = this.performCleanup(callSid);
        this.cleanupLocks.set(callSid, cleanupPromise);
        
        try {
            await cleanupPromise;
        } finally {
            this.cleanupLocks.delete(callSid);
        }
    }
    
    private async performCleanup(callSid: string): Promise<void> {
        sessionLogger.info(`🧹 [${callSid}] Starting session cleanup`);
        
        try {
            // Clear all timers for this session
            timerManager.clearSessionTimers(callSid);
            
            // Cleanup audio forwarding
            await cleanupAudioForwarding(callSid);
            
            // Log completion
            sessionLogger.debug(`Session cleanup completed for ${callSid}`);
            
            sessionLogger.info(`✅ [${callSid}] Session cleanup completed`);
            
        } catch (error) {
            sessionLogger.error(`❌ [${callSid}] Cleanup failed:`, error instanceof Error ? error : new Error(String(error)));
            
            this.emit('session:error', {
                sessionId: callSid,
                error: error instanceof Error ? error : new Error(String(error)),
                context: 'session_cleanup'
            });
        }
    }
    
    private async handleSessionEnded(payload: { 
        sessionId: string; 
        reason: string; 
        duration: number 
    }): Promise<void> {
        // Auto-cleanup when session ends
        await this.cleanupSession(payload.sessionId);
    }
    
    isCleanupInProgress(callSid: string): boolean {
        return this.cleanupLocks.has(callSid);
    }
}