import { EventDrivenComponent } from '@/modules/operator/event-system/event-patterns';
import { sessionLogger } from '@/utils/logger';
import { 
    sendAudioToGemini, 
    sendBrowserAudioToGemini,
    bufferEarlyAudio,
    processBufferedAudio
} from '../websocket-routing.js';
import { GeminiSession } from '../../types/global.js';
import { BoundedMap } from '../metrics.js';
import { AudioProcessor } from '../audio/audio-processor.js';

export class AudioRouter extends EventDrivenComponent {
    private earlyAudioBuffers: BoundedMap<string, Buffer[]>;
    
    constructor() {
        super('AudioRouter');
        this.earlyAudioBuffers = new BoundedMap(500);
        
        // Subscribe to audio events
        this.subscribe('audio:received', this.handleAudioReceived.bind(this));
        this.subscribe('session:created', this.processEarlyAudio.bind(this));
    }
    
    async routeAudio(
        callSid: string, 
        geminiSession: GeminiSession | undefined,
        audioBuffer: Buffer
    ): Promise<void> {
        if (!geminiSession) {
            // Buffer early audio if session not ready
            bufferEarlyAudio(callSid, audioBuffer, this.earlyAudioBuffers);
            
            this.emit('audio:buffered', {
                sessionId: callSid,
                bufferSize: audioBuffer.length,
                audioSize: audioBuffer.length
            });
            
            return;
        }
        
        try {
            await sendAudioToGemini(callSid, geminiSession, audioBuffer, {
                audioProcessor: new AudioProcessor(),
                sessionMetrics: new Map(),
                earlyAudioBuffers: this.earlyAudioBuffers
            });
            
            this.emit('audio:forwarded', {
                sessionId: callSid,
                destination: 'gemini',
                size: audioBuffer.length
            });
        } catch (error) {
            this.emit('audio:error', {
                sessionId: callSid,
                error: error as Error,
                context: 'route_audio'
            });
            throw error;
        }
    }
    
    async routeBrowserAudio(
        callSid: string,
        geminiSession: GeminiSession,
        base64Audio: string
    ): Promise<void> {
        try {
            await sendBrowserAudioToGemini(callSid, geminiSession, base64Audio, {
                audioProcessor: new AudioProcessor(),
                sessionMetrics: new Map(),
                earlyAudioBuffers: this.earlyAudioBuffers
            });
            
            this.emit('audio:forwarded', {
                sessionId: callSid,
                destination: 'gemini_browser',
                size: base64Audio.length
            });
        } catch (error) {
            this.emit('audio:error', {
                sessionId: callSid,
                error: error as Error,
                context: 'route_browser_audio'
            });
            throw error;
        }
    }
    
    private async handleAudioReceived(payload: { 
        sessionId: string; 
        buffer: Buffer; 
        format: string 
    }): Promise<void> {
        sessionLogger.debug(`🎵 Audio received for routing: ${payload.sessionId}`);
    }
    
    private async processEarlyAudio(payload: { 
        sessionId: string; 
        phoneNumber?: string; 
        timestamp: number 
    }): Promise<void> {
        const { sessionId } = payload;
        
        try {
            await processBufferedAudio(sessionId, {
                earlyAudioBuffers: this.earlyAudioBuffers,
                audioProcessor: new AudioProcessor(),
                sessionMetrics: new Map()
            });
            
            sessionLogger.info(`✅ [${sessionId}] Processed buffered early audio`);
        } catch (error) {
            sessionLogger.error(`❌ [${sessionId}] Failed to process early audio:`, error as Error);
        }
    }
}