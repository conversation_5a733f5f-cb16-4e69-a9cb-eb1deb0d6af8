import { EventDrivenComponent } from '@/modules/operator/event-system/event-patterns';
import { sessionLogger } from '@/utils/logger';
import { GeminiSession } from '../../types/global.js';

export interface IGeminiSessionService {
    sendText(sessionId: string, text: string): Promise<void>;
    sendTurnComplete(sessionId: string): Promise<void>;
    sendAudio(sessionId: string, audioBuffer: Buffer): Promise<void>;
    sendBrowserAudio(sessionId: string, base64Audio: string): Promise<void>;
}

export class GeminiSessionService extends EventDrivenComponent implements IGeminiSessionService {
    constructor() {
        super('GeminiSessionService');
    }
    
    async sendText(sessionId: string, text: string): Promise<void> {
        sessionLogger.info(`📝 [${sessionId}] Sending text to Gemini: ${text}`);
        
        try {
            // Text sending will be handled by the session that owns the Gemini connection
            this.emit('session:updated', {
                sessionId,
                updates: { lastTextSent: text, timestamp: Date.now() }
            });
        } catch (error) {
            this.emit('session:error', {
                sessionId,
                error: error as Error,
                context: 'send_text'
            });
            throw error;
        }
    }
    
    async sendTurnComplete(sessionId: string): Promise<void> {
        sessionLogger.info(`🔄 [${sessionId}] Sending turn complete signal`);
        
        try {
            this.emit('session:updated', {
                sessionId,
                updates: { turnCompleted: true, timestamp: Date.now() }
            });
        } catch (error) {
            this.emit('session:error', {
                sessionId,
                error: error as Error,
                context: 'send_turn_complete'
            });
            throw error;
        }
    }
    
    async sendAudio(sessionId: string, audioBuffer: Buffer): Promise<void> {
        try {
            this.emit('audio:received', {
                sessionId,
                buffer: audioBuffer,
                format: 'pcm16'
            });
            
            // Audio forwarding handled by audio routing components
            sessionLogger.debug(`🎤 [${sessionId}] Audio buffer queued for sending (${audioBuffer.length} bytes)`);
            
        } catch (error) {
            this.emit('audio:error', {
                sessionId,
                error: error as Error,
                context: 'send_audio'
            });
            throw error;
        }
    }
    
    async sendBrowserAudio(sessionId: string, base64Audio: string): Promise<void> {
        try {
            const audioBuffer = Buffer.from(base64Audio, 'base64');
            
            this.emit('audio:received', {
                sessionId,
                buffer: audioBuffer,
                format: 'browser_audio'
            });
            
            sessionLogger.debug(`🌐 [${sessionId}] Browser audio queued (${audioBuffer.length} bytes)`);
            
        } catch (error) {
            this.emit('audio:error', {
                sessionId,
                error: error as Error,  
                context: 'send_browser_audio'
            });
            throw error;
        }
    }
}