import { EventDrivenComponent } from '@/modules/operator/event-system/event-patterns';
import { 
    IGeminiService,
    IGeminiLiveSession,
    GeminiSessionConfig,
    GeneratedContent,
    GeminiModel,
    GenerationConfig,
    GeminiRealtimeInput,
    GeminiEventType,
    GeminiCallbacks
} from './gemini-service.interface';
import { ConnectionPool } from '../../utils/connection-pool.js';
import { sessionLogger } from '@/utils/logger';
import { GeminiClient } from '../../types/global.js';

/**
 * Gemini service implementation with connection pooling
 */
export class GeminiService extends EventDrivenComponent implements IGeminiService {
    private client: GeminiClient;
    private activeSessions: Map<string, GeminiLiveSessionWrapper>;
    private connectionPool: ConnectionPool<any>;
    
    constructor(client: GeminiClient) {
        super('GeminiService');
        this.client = client;
        this.activeSessions = new Map();
        // Initialize connection pool for API requests
        this.connectionPool = new ConnectionPool(
            async () => ({ connected: true }),
            async () => {},
            async () => true
        );
    }
    
    async createLiveSession(config: GeminiSessionConfig): Promise<IGeminiLiveSession> {
        try {
            const connection = await this.connectionPool.acquire();
            
            sessionLogger.info(`Creating Gemini live session with model: ${config.model}`);
            
            // Generate session ID
            const sessionId = `gemini_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            
            // Create actual Gemini session
            const geminiSession = await (this.client as any).live.connect({
                model: config?.model || 'gemini-1.5-flash-001',
                systemInstruction: config.systemInstruction,
                generationConfig: config.generationConfig,
                callbacks: this.wrapCallbacks(sessionId, config.callbacks)
            });
            
            // Wrap in our interface
            const sessionWrapper = new GeminiLiveSessionWrapper(
                geminiSession,
                config.model
            );
            
            this.activeSessions.set(sessionWrapper.sessionId, sessionWrapper);
            
            await this.connectionPool.release(connection);
            
            this.emit('session:created', {
                sessionId: sessionWrapper.sessionId,
                timestamp: Date.now()
            });
            
            return sessionWrapper;
            
        } catch (error) {
            sessionLogger.error('Failed to create Gemini session:', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    
    async endSession(sessionId: string): Promise<void> {
        try {
            const session = this.activeSessions.get(sessionId);
            if (!session) {
                throw new Error(`Session ${sessionId} not found`);
            }
            
            await session.close();
            this.activeSessions.delete(sessionId);
            
            this.emit('session:ended', {
                sessionId,
                reason: 'API request',
                duration: Date.now() - session.createdAt
            });
            
        } catch (error) {
            sessionLogger.error(`Failed to end session ${sessionId}:`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    
    async generateContent(prompt: string, config?: GenerationConfig): Promise<GeneratedContent> {
        try {
            const connection = await this.connectionPool.acquire();
            
            const startTime = Date.now();
            
            // Use the standard Gemini API for content generation
            const modelName = 'gemini-1.5-flash-001';
            const model = (this.client as any).getGenerativeModel({
                model: modelName,
                generationConfig: config
            });
            
            const result = await model.generateContent(prompt);
            const response = await result.response;
            
            await this.connectionPool.release(connection);
            
            const generationTime = Date.now() - startTime;
            
            return {
                text: response.text(),
                metadata: {
                    model: modelName,
                    tokensUsed: response.usageMetadata?.totalTokenCount || 0,
                    generationTime
                }
            };
            
        } catch (error) {
            sessionLogger.error('Failed to generate content:', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    
    async listModels(): Promise<GeminiModel[]> {
        // In a real implementation, this would call the Gemini API
        return [
            {
                name: 'gemini-1.5-flash-001',
                displayName: 'Gemini 1.5 Flash',
                description: 'Fast and efficient model for real-time applications',
                supportedModalities: ['TEXT', 'AUDIO'],
                maxTokens: 32768
            },
            {
                name: 'gemini-1.5-pro-001',
                displayName: 'Gemini 1.5 Pro',
                description: 'Advanced model with enhanced capabilities',
                supportedModalities: ['TEXT', 'AUDIO'],
                maxTokens: 128000
            }
        ];
    }
    
    async getModel(modelName: string): Promise<GeminiModel> {
        const models = await this.listModels();
        const model = models.find(m => m.name === modelName);
        
        if (!model) {
            throw new Error(`Model ${modelName} not found`);
        }
        
        return model;
    }
    
    private wrapCallbacks(sessionId: string, callbacks?: GeminiCallbacks): GeminiCallbacks {
        return {
            onopen: () => {
                callbacks?.onopen?.();
                this.emit('gemini:session:opened', { sessionId });
            },
            onmessage: (message) => {
                callbacks?.onmessage?.(message);
                this.emit('gemini:message:received', { sessionId, message });
            },
            onerror: (error) => {
                callbacks?.onerror?.(error);
                this.emit('gemini:error', { sessionId, error });
            },
            onclose: () => {
                callbacks?.onclose?.();
                this.emit('gemini:session:closed', { sessionId });
            }
        };
    }
    
    getActiveSessionCount(): number {
        return this.activeSessions.size;
    }
    
    cleanup(): void {
        // Close all active sessions
        for (const [sessionId, session] of this.activeSessions) {
            session.close().catch(error => {
                sessionLogger.error(`Error closing session ${sessionId}:`, error instanceof Error ? error : new Error(String(error)));
            });
        }
        
        this.activeSessions.clear();
        super.cleanup();
    }
}

/**
 * Wrapper class for Gemini live sessions
 */
class GeminiLiveSessionWrapper implements IGeminiLiveSession {
    sessionId: string;
    isActive: boolean;
    createdAt: number;
    
    private geminiSession: any;
    private eventHandlers: Map<GeminiEventType, Set<Function>>;
    
    constructor(geminiSession: any, model: string) {
        this.geminiSession = geminiSession;
        this.sessionId = `gemini-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        this.isActive = true;
        this.createdAt = Date.now();
        this.eventHandlers = new Map();
    }
    
    async sendRealtimeInput(input: GeminiRealtimeInput): Promise<void> {
        if (!this.isActive) {
            throw new Error('Session is not active');
        }
        
        await this.geminiSession.sendRealtimeInput(input);
    }
    
    async sendTurnComplete(): Promise<void> {
        if (!this.isActive) {
            throw new Error('Session is not active');
        }
        
        await this.geminiSession.sendTurnComplete();
    }
    
    on(event: GeminiEventType, handler: (data: any) => void): void {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, new Set());
        }
        
        this.eventHandlers.get(event)!.add(handler);
    }
    
    off(event: GeminiEventType, handler: (data: any) => void): void {
        const handlers = this.eventHandlers.get(event);
        if (handlers) {
            handlers.delete(handler);
        }
    }
    
    async close(): Promise<void> {
        if (!this.isActive) {
            return;
        }
        
        this.isActive = false;
        
        if (this.geminiSession && typeof this.geminiSession.close === 'function') {
            await this.geminiSession.close();
        }
        
        this.eventHandlers.clear();
    }
}