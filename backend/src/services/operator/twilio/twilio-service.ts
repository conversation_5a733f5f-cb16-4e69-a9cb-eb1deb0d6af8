import { EventDrivenComponent } from '@/modules/operator/event-system/event-patterns';
import { 
    ITwilioService, 
    TwilioCallResponse, 
    TwilioCallStatus,
    CallUpdateParams,
    TwilioStreamResponse,
    TwiMLAction,
    TwilioConfig
} from './twilio-service.interface';
import { ConnectionPool } from '../../utils/connection-pool.js';
import { sessionLogger } from '@/utils/logger';
import * as crypto from 'crypto';

/**
 * Twilio service implementation with connection pooling
 */
export class TwilioService extends EventDrivenComponent implements ITwilioService {
    private config: TwilioConfig;
    private baseUrl = 'https://api.twilio.com/2010-04-01';
    private connectionPool: ConnectionPool<any>;
    
    constructor(config: TwilioConfig) {
        super('TwilioService');
        this.config = config;
        // Initialize connection pool for API requests
        this.connectionPool = new ConnectionPool(
            async () => ({ connected: true }),
            async () => {},
            async () => true
        );
    }
    
    async makeCall(to: string, from: string, url: string): Promise<TwilioCallResponse> {
        try {
            const connection = await this.connectionPool.acquire();
            
            const response = await this.apiRequest('POST', `/Accounts/${this.config.accountSid}/Calls.json`, {
                To: to,
                From: from || this.config.phoneNumber,
                Url: url,
                StatusCallback: this.config.statusCallbackUrl,
                StatusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed']
            });
            
            await this.connectionPool.release(connection);
            
            this.emit('call:created', {
                callSid: response.sid,
                from: response.from,
                to: response.to,
                sessionId: response.sid // Use callSid as sessionId
            });
            
            return {
                sid: response.sid,
                status: response.status,
                direction: response.direction,
                from: response.from,
                to: response.to,
                dateCreated: new Date(response.date_created)
            };
            
        } catch (error) {
            sessionLogger.error('Failed to make Twilio call:', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    
    async endCall(callSid: string): Promise<void> {
        try {
            await this.updateCall(callSid, { status: 'completed' });
            
            this.emit('call:ended', {
                callSid,
                reason: 'API request',
                duration: 0
            });
            
        } catch (error) {
            sessionLogger.error(`Failed to end call ${callSid}:`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    
    async getCallStatus(callSid: string): Promise<TwilioCallStatus> {
        try {
            const connection = await this.connectionPool.acquire();
            
            const response = await this.apiRequest(
                'GET', 
                `/Accounts/${this.config.accountSid}/Calls/${callSid}.json`
            );
            
            await this.connectionPool.release(connection);
            
            return {
                sid: response.sid,
                status: response.status,
                duration: parseInt(response.duration),
                direction: response.direction,
                from: response.from,
                to: response.to
            };
            
        } catch (error) {
            sessionLogger.error(`Failed to get call status for ${callSid}:`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    
    async updateCall(callSid: string, updates: CallUpdateParams): Promise<void> {
        try {
            const connection = await this.connectionPool.acquire();
            
            const params: any = {};
            if (updates.status) params.Status = updates.status;
            if (updates.twiml) params.Twiml = updates.twiml;
            if (updates.url) params.Url = updates.url;
            if (updates.method) params.Method = updates.method;
            
            await this.apiRequest(
                'POST',
                `/Accounts/${this.config.accountSid}/Calls/${callSid}.json`,
                params
            );
            
            await this.connectionPool.release(connection);
            
        } catch (error) {
            sessionLogger.error(`Failed to update call ${callSid}:`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    
    async createStream(callSid: string, streamUrl: string): Promise<TwilioStreamResponse> {
        try {
            const twiml = this.generateTwiMLResponse([
                { type: 'stream', url: streamUrl, track: 'both' }
            ]);
            
            await this.updateCall(callSid, { twiml });
            
            return {
                sid: `ST${crypto.randomBytes(16).toString('hex')}`,
                accountSid: this.config.accountSid,
                callSid,
                url: streamUrl,
                status: 'in-progress'
            };
            
        } catch (error) {
            sessionLogger.error(`Failed to create stream for ${callSid}:`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    
    validateWebhookSignature(signature: string, url: string, params: any): boolean {
        const authToken = this.config.authToken;
        const data = Object.keys(params)
            .sort()
            .reduce((acc, key) => acc + key + params[key], url);
            
        const expectedSignature = crypto
            .createHmac('sha1', authToken)
            .update(data)
            .digest('base64');
            
        return signature === expectedSignature;
    }
    
    generateTwiMLResponse(actions: TwiMLAction[]): string {
        let twiml = '<?xml version="1.0" encoding="UTF-8"?><Response>';
        
        for (const action of actions) {
            switch (action.type) {
                case 'say':
                    twiml += `<Say${action.voice ? ` voice="${action.voice}"` : ''}>${action.text}</Say>`;
                    break;
                    
                case 'play':
                    twiml += `<Play>${action.url}</Play>`;
                    break;
                    
                case 'pause':
                    twiml += `<Pause length="${action.duration}"/>`;
                    break;
                    
                case 'stream':
                    twiml += `<Start><Stream url="${action.url}"`;
                    if (action.track) twiml += ` track="${action.track}"`;
                    twiml += '/></Start>';
                    break;
                    
                case 'redirect':
                    twiml += `<Redirect>${action.url}</Redirect>`;
                    break;
                    
                case 'hangup':
                    twiml += '<Hangup/>';
                    break;
            }
        }
        
        twiml += '</Response>';
        return twiml;
    }
    
    private async apiRequest(method: string, path: string, data?: any): Promise<any> {
        // Implementation would use actual HTTP client
        // This is a simplified version
        const url = `${this.baseUrl}${path}`;
        const auth = Buffer.from(`${this.config.accountSid}:${this.config.authToken}`).toString('base64');
        
        sessionLogger.debug(`Twilio API ${method} ${path}`);
        
        // Actual implementation would make HTTP request
        // For now, return mock data
        return {
            sid: `CA${crypto.randomBytes(16).toString('hex')}`,
            status: 'queued',
            direction: 'outbound',
            from: data?.From || this.config.phoneNumber,
            to: data?.To,
            date_created: new Date().toISOString(),
            duration: '0'
        };
    }
}