/**
 * Connection pooling for Gemini sessions to improve performance and reduce resource usage
 */

import { logger } from './logger.js';
import { timerManager } from './timer-manager.js';

export interface PooledConnection<T> {
    connection: T;
    created: number;
    lastUsed: number;
    inUse: boolean;
    id: string;
}

export interface PoolConfig {
    minConnections: number;
    maxConnections: number;
    maxIdleTime: number;      // Max idle time before connection is closed (ms)
    maxConnectionAge: number; // Max age of connection before renewal (ms)
    acquireTimeout: number;   // Max time to wait for connection (ms)
    cleanupInterval: number;  // Cleanup interval (ms)
}

export class ConnectionPool<T> {
    private connections: Map<string, PooledConnection<T>> = new Map();
    private waitingQueue: Array<{
        resolve: (connection: PooledConnection<T>) => void;
        reject: (error: Error) => void;
        timestamp: number;
    }> = [];
    
    private config: PoolConfig;
    private createConnection: () => Promise<T>;
    private destroyConnection: (connection: T) => Promise<void>;
    private validateConnection: (connection: T) => Promise<boolean>;
    private isCleanupRunning = false;

    constructor(
        createFn: () => Promise<T>,
        destroyFn: (connection: T) => Promise<void>,
        validateFn: (connection: T) => Promise<boolean>,
        config: Partial<PoolConfig> = {}
    ) {
        this.createConnection = createFn;
        this.destroyConnection = destroyFn;
        this.validateConnection = validateFn;
        
        this.config = {
            minConnections: config.minConnections || 2,
            maxConnections: config.maxConnections || 10,
            maxIdleTime: config.maxIdleTime || 300000, // 5 minutes
            maxConnectionAge: config.maxConnectionAge || 3600000, // 1 hour
            acquireTimeout: config.acquireTimeout || 30000, // 30 seconds
            cleanupInterval: config.cleanupInterval || 60000 // 1 minute
        };

        this.startCleanupTimer();
        this.initializeMinConnections();
    }

    /**
     * Acquire a connection from the pool
     */
    async acquire(): Promise<PooledConnection<T>> {
        // Try to get an available connection
        const available = this.getAvailableConnection();
        if (available) {
            available.inUse = true;
            available.lastUsed = Date.now();
            return available;
        }

        // If we can create more connections, do so
        if (this.connections.size < this.config.maxConnections) {
            try {
                const pooledConnection = await this.createPooledConnection();
                pooledConnection.inUse = true;
                return pooledConnection;
            } catch (error) {
                logger.error('❌ Failed to create new pooled connection:', error as Error);
                throw error;
            }
        }

        // Wait for a connection to become available
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                const index = this.waitingQueue.findIndex(item => item.resolve === resolve);
                if (index !== -1) {
                    this.waitingQueue.splice(index, 1);
                }
                reject(new Error('Connection acquire timeout'));
            }, this.config.acquireTimeout);

            this.waitingQueue.push({
                resolve: (connection) => {
                    clearTimeout(timeout);
                    resolve(connection);
                },
                reject: (error) => {
                    clearTimeout(timeout);
                    reject(error);
                },
                timestamp: Date.now()
            });
        });
    }

    /**
     * Release a connection back to the pool
     */
    async release(pooledConnection: PooledConnection<T>): Promise<void> {
        const connection = this.connections.get(pooledConnection.id);
        if (!connection) {
            logger.warn('⚠️ Attempted to release unknown connection');
            return;
        }

        connection.inUse = false;
        connection.lastUsed = Date.now();

        // Check if connection is still valid
        try {
            const isValid = await this.validateConnection(connection.connection);
            if (!isValid) {
                await this.removeConnection(connection.id);
                return;
            }
        } catch (error) {
            logger.warn('⚠️ Connection validation failed during release:', error as Error);
            await this.removeConnection(connection.id);
            return;
        }

        // Serve waiting requests
        if (this.waitingQueue.length > 0) {
            const waiter = this.waitingQueue.shift();
            if (waiter) {
                connection.inUse = true;
                waiter.resolve(connection);
            }
        }
    }

    /**
     * Get pool statistics
     */
    getStats(): {
        total: number;
        inUse: number;
        available: number;
        waiting: number;
        config: PoolConfig;
    } {
        const inUse = Array.from(this.connections.values()).filter(c => c.inUse).length;
        return {
            total: this.connections.size,
            inUse,
            available: this.connections.size - inUse,
            waiting: this.waitingQueue.length,
            config: this.config
        };
    }

    /**
     * Close all connections and clean up
     */
    async destroy(): Promise<void> {
        logger.info('🧹 Destroying connection pool');
        
        // Stop cleanup timer
        timerManager.clearInterval('connection_pool_cleanup');
        
        // Reject all waiting requests
        while (this.waitingQueue.length > 0) {
            const waiter = this.waitingQueue.shift();
            if (waiter) {
                waiter.reject(new Error('Connection pool is being destroyed'));
            }
        }

        // Close all connections
        const closePromises = Array.from(this.connections.values()).map(async (pooledConnection) => {
            try {
                await this.destroyConnection(pooledConnection.connection);
            } catch (error) {
                logger.error('❌ Error destroying connection:', error as Error);
            }
        });

        await Promise.allSettled(closePromises);
        this.connections.clear();
        
        logger.info('✅ Connection pool destroyed');
    }

    /**
     * Get an available connection from the pool
     */
    private getAvailableConnection(): PooledConnection<T> | null {
        for (const connection of this.connections.values()) {
            if (!connection.inUse) {
                return connection;
            }
        }
        return null;
    }

    /**
     * Create a new pooled connection
     */
    private async createPooledConnection(): Promise<PooledConnection<T>> {
        const connection = await this.createConnection();
        const pooledConnection: PooledConnection<T> = {
            connection,
            created: Date.now(),
            lastUsed: Date.now(),
            inUse: false,
            id: `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };

        this.connections.set(pooledConnection.id, pooledConnection);
        logger.debug(`✅ Created new pooled connection: ${pooledConnection.id}`);
        
        return pooledConnection;
    }

    /**
     * Remove a connection from the pool
     */
    private async removeConnection(connectionId: string): Promise<void> {
        const pooledConnection = this.connections.get(connectionId);
        if (!pooledConnection) {
            return;
        }

        this.connections.delete(connectionId);
        
        try {
            await this.destroyConnection(pooledConnection.connection);
            logger.debug(`🗑️ Removed connection: ${connectionId}`);
        } catch (error) {
            logger.error(`❌ Error destroying connection ${connectionId}:`, error as Error);
        }
    }

    /**
     * Initialize minimum connections
     */
    private async initializeMinConnections(): Promise<void> {
        const promises = [];
        for (let i = 0; i < this.config.minConnections; i++) {
            promises.push(this.createPooledConnection().catch(error => {
                logger.error('❌ Failed to create initial connection:', error as Error);
            }));
        }
        
        await Promise.allSettled(promises);
        logger.info(`✅ Initialized connection pool with ${this.connections.size} connections`);
    }

    /**
     * Start cleanup timer
     */
    private startCleanupTimer(): void {
        timerManager.setInterval('connection_pool_cleanup', () => {
            this.cleanup();
        }, this.config.cleanupInterval);
    }

    /**
     * Clean up old and idle connections
     */
    private async cleanup(): Promise<void> {
        if (this.isCleanupRunning) {
            return;
        }

        this.isCleanupRunning = true;
        const now = Date.now();
        const connectionsToRemove: string[] = [];

        try {
            for (const [id, connection] of this.connections.entries()) {
                // Skip connections in use
                if (connection.inUse) {
                    continue;
                }

                // Remove old connections
                if (now - connection.created > this.config.maxConnectionAge) {
                    connectionsToRemove.push(id);
                    continue;
                }

                // Remove idle connections (but keep minimum)
                if (
                    now - connection.lastUsed > this.config.maxIdleTime &&
                    this.connections.size > this.config.minConnections
                ) {
                    connectionsToRemove.push(id);
                    continue;
                }

                // Validate connection health
                try {
                    const isValid = await this.validateConnection(connection.connection);
                    if (!isValid) {
                        connectionsToRemove.push(id);
                    }
                } catch (error) {
                    logger.warn(`⚠️ Connection validation failed for ${id}:`, error as Error);
                    connectionsToRemove.push(id);
                }
            }

            // Remove identified connections
            for (const id of connectionsToRemove) {
                await this.removeConnection(id);
            }

            if (connectionsToRemove.length > 0) {
                logger.info(`🧹 Cleaned up ${connectionsToRemove.length} connections`);
            }

        } catch (error) {
            logger.error('❌ Error during connection pool cleanup:', error as Error);
        } finally {
            this.isCleanupRunning = false;
        }
    }
}

// Export singleton instance (lowercase) for backward compatibility
export const connectionPool = ConnectionPool;
