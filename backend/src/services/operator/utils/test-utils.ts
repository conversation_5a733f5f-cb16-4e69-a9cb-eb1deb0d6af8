/**
 * Test Utilities for Twilio Gemini Live API
 * 
 * Provides comprehensive testing infrastructure for:
 * - All 4 flows: outbound/inbound × twilio/browser
 * - LLM integration testing with mocks
 * - Audio processing validation
 * - Session management testing
 * - Error scenario simulation
 */

import { EventEmitter } from 'events';
import { performance } from 'perf_hooks';
import { logger } from './logger.js';

interface MockGeminiOptions {
  responses?: Array<{ audioData?: ArrayBuffer }>;
}

interface MockTwilioOptions {
  simulateErrors?: boolean;
}

interface TwilioCall {
  sid: string;
  to: string;
  from: string;
  url: string;
  status: string;
  startTime: Date;
  endTime?: Date;
  duration: number | null;
}

interface WebhookData {
  CallSid: string;
  CallStatus: string;
  From?: string;
  To?: string;
}

interface WebSocketMessage {
  timestamp: number;
  data: any;
}

interface SessionData {
  id: string;
  type: 'twilio' | 'browser';
  status: string;
  startTime: Date;
  callSid?: string;
  from?: string;
  to?: string;
  direction: 'outbound' | 'inbound';
  campaignId: number;
  geminiSession: any;
  websocket: any;
  audioMetrics: {
    packetsReceived?: number;
    packetsProcessed?: number;
    samplesReceived?: number;
    samplesProcessed?: number;
    audioQuality: string;
  };
}

interface PerformanceMeasurement {
  startTime: number;
  endTime: number | null;
  duration: number | null;
}

/**
 * Mock Gemini API responses for deterministic testing
 */
export class MockGeminiClient extends EventEmitter {
  private responses: Array<{ audioData?: ArrayBuffer }>;
  private responseIndex: number;
  public connected: boolean;
  public sessionId: string;

  constructor(options: MockGeminiOptions = {}) {
    super();
    this.responses = options.responses || [];
    this.responseIndex = 0;
    this.connected = false;
    this.sessionId = `mock-session-${Date.now()}`;
  }

  async connect(): Promise<string> {
    this.connected = true;
    this.emit('connected');
    return this.sessionId;
  }

  async disconnect(): Promise<void> {
    this.connected = false;
    this.emit('disconnected');
  }

  sendRealtimeInput(input: any): void {
    if (!this.connected) {
      throw new Error('Not connected to Gemini');
    }

    // Simulate processing delay
    setTimeout(() => {
      const response = this.responses[this.responseIndex % this.responses.length];
      this.responseIndex++;
      
      this.emit('response', {
        type: 'audio',
        data: response.audioData || new ArrayBuffer(1024),
        mimeType: 'audio/pcm16'
      });
    }, 100);
  }
}

/**
 * Mock Twilio webhooks and API calls
 */
export class MockTwilioClient {
  private calls: Map<string, TwilioCall>;
  private webhookCallbacks: Map<string, (data: WebhookData) => void>;
  private simulateErrors: boolean;

  constructor(options: MockTwilioOptions = {}) {
    this.calls = new Map();
    this.webhookCallbacks = new Map();
    this.simulateErrors = options.simulateErrors || false;
  }

  async makeCall(to: string, from: string, url: string): Promise<TwilioCall> {
    if (this.simulateErrors) {
      throw new Error('Twilio API error');
    }

    const callSid = `CA${Math.random().toString(36).substr(2, 32)}`;
    const call: TwilioCall = {
      sid: callSid,
      to,
      from,
      url,
      status: 'initiated',
      startTime: new Date(),
      duration: null
    };

    this.calls.set(callSid, call);

    // Simulate call progression
    setTimeout(() => {
      call.status = 'ringing';
      this.triggerWebhook(callSid, 'ringing');
    }, 100);

    setTimeout(() => {
      call.status = 'in-progress';
      this.triggerWebhook(callSid, 'answered');
    }, 500);

    return call;
  }

  async endCall(callSid: string): Promise<void> {
    const call = this.calls.get(callSid);
    if (call) {
      call.status = 'completed';
      call.endTime = new Date();
      call.duration = Math.round((call.endTime.getTime() - call.startTime.getTime()) / 1000);
      this.triggerWebhook(callSid, 'completed');
    }
  }

  triggerWebhook(callSid: string, status: string): void {
    const callback = this.webhookCallbacks.get(callSid);
    if (callback) {
      callback({
        CallSid: callSid,
        CallStatus: status,
        From: this.calls.get(callSid)?.from,
        To: this.calls.get(callSid)?.to
      });
    }
  }

  onWebhook(callSid: string, callback: (data: WebhookData) => void): void {
    this.webhookCallbacks.set(callSid, callback);
  }

  getCall(callSid: string): TwilioCall | undefined {
    return this.calls.get(callSid);
  }
}

/**
 * Audio test data generator
 */
export class AudioTestData {
  static generatePCM16(durationMs: number = 1000, frequency: number = 440): ArrayBuffer {
    const sampleRate = 16000;
    const samples = Math.floor(sampleRate * durationMs / 1000);
    const buffer = new ArrayBuffer(samples * 2);
    const view = new Int16Array(buffer);

    for (let i = 0; i < samples; i++) {
      const t = i / sampleRate;
      const amplitude = Math.sin(2 * Math.PI * frequency * t) * 0.5;
      view[i] = Math.floor(amplitude * 32767);
    }

    return buffer;
  }

  static generateMuLaw(durationMs: number = 1000): ArrayBuffer {
    const sampleRate = 8000;
    const samples = Math.floor(sampleRate * durationMs / 1000);
    const buffer = new ArrayBuffer(samples);
    const view = new Uint8Array(buffer);

    // Generate mu-law encoded silence with some variation
    for (let i = 0; i < samples; i++) {
      view[i] = 0xFF; // mu-law silence
    }

    return buffer;
  }

  static generateBase64Audio(format: 'pcm16' | 'mulaw' = 'pcm16', durationMs: number = 1000): string {
    let buffer: ArrayBuffer;
    if (format === 'pcm16') {
      buffer = this.generatePCM16(durationMs);
    } else if (format === 'mulaw') {
      buffer = this.generateMuLaw(durationMs);
    } else {
      throw new Error(`Unsupported format: ${format}`);
    }

    return Buffer.from(buffer).toString('base64');
  }
}

/**
 * WebSocket test utilities
 */
export class MockWebSocket extends EventEmitter {
  public url: string;
  public readyState: number;
  private messages: WebSocketMessage[];

  constructor(url: string) {
    super();
    this.url = url;
    this.readyState = 0; // CONNECTING
    this.messages = [];
    
    // Simulate connection
    setTimeout(() => {
      this.readyState = 1; // OPEN
      this.emit('open');
    }, 10);
  }

  send(data: string | Buffer): void {
    if (this.readyState !== 1) {
      throw new Error('WebSocket is not open');
    }

    let parsedData: any;
    try {
      parsedData = JSON.parse(data.toString());
    } catch (e) {
      parsedData = data;
    }

    this.messages.push({
      timestamp: Date.now(),
      data: parsedData
    });

    this.emit('message', { data });
  }

  close(): void {
    this.readyState = 3; // CLOSED
    this.emit('close');
  }

  getMessages(): WebSocketMessage[] {
    return this.messages;
  }

  getLastMessage(): WebSocketMessage | undefined {
    return this.messages[this.messages.length - 1];
  }
}

/**
 * Test session factory
 */
export class TestSessionFactory {
  static createTwilioSession(callSid: string | null = null): SessionData {
    return {
      id: callSid || `CA${Math.random().toString(36).substr(2, 32)}`,
      type: 'twilio',
      status: 'active',
      startTime: new Date(),
      callSid: callSid || `CA${Math.random().toString(36).substr(2, 32)}`,
      from: '+1234567890',
      to: '+1987654321',
      direction: 'outbound',
      campaignId: 1,
      geminiSession: null,
      websocket: null,
      audioMetrics: {
        packetsReceived: 0,
        packetsProcessed: 0,
        audioQuality: 'good'
      }
    };
  }

  static createBrowserSession(sessionId: string | null = null): SessionData {
    return {
      id: sessionId || `browser-${Math.random().toString(36).substr(2, 16)}`,
      type: 'browser',
      status: 'active',
      startTime: new Date(),
      direction: 'outbound',
      campaignId: 1,
      geminiSession: null,
      websocket: null,
      audioMetrics: {
        samplesReceived: 0,
        samplesProcessed: 0,
        audioQuality: 'excellent'
      }
    };
  }

  static createInboundSession(type: 'twilio' | 'browser' = 'twilio'): SessionData {
    const session = type === 'twilio' 
      ? this.createTwilioSession()
      : this.createBrowserSession();
    
    session.direction = 'inbound';
    session.campaignId = 7; // Inbound campaigns start at 7
    return session;
  }
}

/**
 * Performance measurement utilities
 */
export class TestPerformanceMonitor {
  private measurements: Map<string, PerformanceMeasurement>;

  constructor() {
    this.measurements = new Map();
  }

  start(operation: string): void {
    this.measurements.set(operation, {
      startTime: performance.now(),
      endTime: null,
      duration: null
    });
  }

  end(operation: string): number | null {
    const measurement = this.measurements.get(operation);
    if (measurement) {
      measurement.endTime = performance.now();
      measurement.duration = measurement.endTime - measurement.startTime;
    }
    return measurement?.duration || null;
  }

  getDuration(operation: string): number | null {
    return this.measurements.get(operation)?.duration || null;
  }

  getAllMeasurements(): Record<string, PerformanceMeasurement> {
    return Object.fromEntries(this.measurements);
  }

  reset(): void {
    this.measurements.clear();
  }
}

/**
 * Error simulation utilities
 */
export class ErrorSimulator {
  static networkError(): Error {
    const error = new Error('Network error') as Error & { code?: string };
    error.code = 'ECONNRESET';
    return error;
  }

  static geminiApiError(): Error {
    const error = new Error('Gemini API rate limit exceeded') as Error & { status?: number };
    error.status = 429;
    return error;
  }

  static twilioWebhookError(): Error {
    const error = new Error('Twilio webhook validation failed') as Error & { status?: number };
    error.status = 403;
    return error;
  }

  static audioCorruptionError(): Error {
    const error = new Error('Audio data corrupted') as Error & { code?: string };
    error.code = 'AUDIO_CORRUPT';
    return error;
  }

  static sessionTimeoutError(): Error {
    const error = new Error('Session timeout') as Error & { code?: string };
    error.code = 'SESSION_TIMEOUT';
    return error;
  }
}

/**
 * Test data factories
 */
export class TestDataFactory {
  static createCampaignScript(id: number = 1, type: 'outbound' | 'inbound' = 'outbound') {
    return {
      id,
      type,
      campaign: `Test Campaign ${id}`,
      agentPersona: 'Professional AI assistant',
      script: `This is a test campaign script for ${type} calls.`,
      transferData: {
        transferNumber: '+1555000' + String(id).padStart(4, '0'),
        agentName: `Agent ${id}`
      }
    };
  }

  static createCallResult(callSid: string, status: string = 'completed') {
    return {
      callSid,
      status,
      startTime: new Date(Date.now() - 60000),
      endTime: status === 'completed' ? new Date() : null,
      duration: status === 'completed' ? 60 : null,
      transcript: 'Test conversation transcript',
      outcome: status === 'completed' ? 'successful' : 'in_progress',
      campaignId: 1
    };
  }

  static createAudioQualityMetrics() {
    return {
      sampleRate: 16000,
      bitDepth: 16,
      channels: 1,
      latency: Math.random() * 100 + 50, // 50-150ms
      jitter: Math.random() * 10, // 0-10ms
      packetLoss: Math.random() * 0.01, // 0-1%
      snr: Math.random() * 20 + 30 // 30-50dB
    };
  }
}

/**
 * Test assertion helpers
 */
export class TestAssertions {
  static assertSessionActive(session: SessionData | null | undefined): void {
    if (!session || session.status !== 'active') {
      throw new Error(`Session is not active: ${session?.status || 'null'}`);
    }
  }

  static assertAudioDataValid(audioData: ArrayBuffer | null | undefined, format: 'pcm16' | 'mulaw' = 'pcm16'): void {
    if (!audioData || audioData.byteLength === 0) {
      throw new Error('Audio data is empty or null');
    }

    if (format === 'pcm16' && audioData.byteLength % 2 !== 0) {
      throw new Error('PCM16 audio data must have even byte length');
    }
  }

  static assertWebSocketMessage(message: { data?: string } | null, expectedType: string): any {
    if (!message || !message.data) {
      throw new Error('WebSocket message is empty');
    }

    let parsed: any;
    try {
      parsed = JSON.parse(message.data);
    } catch (e) {
      throw new Error('WebSocket message is not valid JSON');
    }

    if (parsed.event !== expectedType) {
      throw new Error(`Expected ${expectedType}, got ${parsed.event}`);
    }

    return parsed;
  }

  static assertPerformanceWithinBounds(duration: number, maxMs: number): void {
    if (duration > maxMs) {
      // Allow 500ms buffer for performance variations in CI/test environments
      const bufferTime = maxMs + 500;
      if (duration > bufferTime) {
        throw new Error(`Performance too slow: ${duration}ms > ${bufferTime}ms (expected: ${maxMs}ms)`);
      }
      console.warn(`⚠️ Performance slightly over threshold: ${duration}ms > ${maxMs}ms (within buffer)`);
    }
  }

  static assertCallSidFormat(callSid: string | null | undefined): void {
    if (!callSid || !callSid.match(/^CA[a-f0-9]{32}$/)) {
      throw new Error(`Invalid call SID format: ${callSid}`);
    }
  }
}

/**
 * Integration test helpers
 */
export class IntegrationTestHelpers {
  static async waitForCondition(
    condition: () => boolean | Promise<boolean>, 
    timeoutMs: number = 5000, 
    intervalMs: number = 100
  ): Promise<boolean> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeoutMs) {
      if (await condition()) {
        return true;
      }
      await new Promise(resolve => setTimeout(resolve, intervalMs));
    }
    
    throw new Error(`Condition not met within ${timeoutMs}ms`);
  }

  static async simulateCall(
    mockTwilio: MockTwilioClient, 
    mockGemini: MockGeminiClient, 
    campaignId: number = 1
  ): Promise<TwilioCall> {
    const callResult = await mockTwilio.makeCall(
      '+1987654321',
      '+1234567890',
      'http://localhost:3101/media-stream'
    );

    // Wait for call to be answered
    await this.waitForCondition(() => 
      mockTwilio.getCall(callResult.sid)?.status === 'in-progress'
    );

    // Simulate some conversation
    await mockGemini.connect();
    mockGemini.sendRealtimeInput({
      media: {
        data: AudioTestData.generateBase64Audio('pcm16', 2000),
        mimeType: 'audio/pcm16'
      }
    });

    return callResult;
  }

  static createTestServer(port: number = 0) {
    // This would create a test instance of the Fastify server
    // Implementation would depend on the actual server setup
    return {
      start: async () => ({ port }),
      stop: async () => {},
      getUrl: () => `http://localhost:${port}`
    };
  }
}