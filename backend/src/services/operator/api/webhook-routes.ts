// Webhook routes for handling Twilio webhook callbacks
import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { config } from './config/config.js';
import { validateTwilioWebhook } from '@/utils/twilio-validation';
import {
    IncomingCallBody,
    validateIncomingCall,
    isOutboundCall,
    getOutboundScriptConfig,
    getInboundScriptConfig,
    createCallConfig,
    constructWebSocketUrl,
    escapeXmlUrl,
    generateTwiML,
    generateErrorTwiML,
    logCallConfiguration
} from './call-handlers.js';
import {
    Dependencies,
    CallConfig,
    CallStatusBody
} from './types/api-types.js';
import { EventEmitter } from 'events';

/**
 * Registers Twilio webhook routes for handling incoming calls and call status updates
 */
export function registerWebhookRoutes(fastify: FastifyInstance, dependencies: Dependencies): void {
    console.log('🔗 Starting webhook route registration...');
    
    const {
        contextManager,
        activeConnections,
        lifecycleManager,
        scriptManager
    } = dependencies;

    // Configuration values
    const PUBLIC_URL = config.server.publicUrl;

    // ATOMIC CONFIGURATION STORAGE: Store configuration per CallSid to prevent race conditions
    const callConfigurations = new Map<string, CallConfig>();
    const callConfigEmitter = new EventEmitter();

    // Expose emitter for other modules
    fastify.decorate('callConfigEmitter', callConfigEmitter);
    
    // Default configuration template - no hardcoded instructions
    const defaultConfig: CallConfig = {
        aiInstructions: '', // Campaign script should provide all instructions
        voice: config.ai.gemini.defaultVoice,
        model: config.ai.gemini.defaultModel,
        targetName: null,
        targetPhoneNumber: null,
        phoneNumber: '',
        mode: 'outbound'
    };
    
    // Global configuration for backward compatibility (legacy outbound calls)
    let nextCallConfig: CallConfig = { ...defaultConfig };

    // INBOUND CALL WEBHOOK - Handle incoming calls from Twilio
    fastify.post<{ Body: IncomingCallBody }>('/incoming-call', async (request, reply) => {
        try {
            // Validate Twilio webhook signature for security
            if (!validateTwilioWebhook(request)) {
                console.error('❌ Invalid Twilio webhook signature');
                reply.status(403);
                return { error: 'Forbidden' };
            }
            console.log('✅ Twilio webhook signature validated successfully');

            console.log('📞 [INBOUND] Incoming call webhook received');
            console.log('📋 [INBOUND] Call data:', request.body);

            // Validate incoming call data
            const validation = validateIncomingCall(request.body);
            if (!validation.isValid) {
                reply.status(validation.statusCode || 400);
                return { error: validation.error };
            }

            const { CallSid, From, To, CallStatus, Direction } = request.body;
            const isOutbound = isOutboundCall(Direction);
            console.log(`📞 [${isOutbound ? 'OUTBOUND' : 'INBOUND'}] Call direction: ${Direction}`);

            // Get appropriate script configuration
            let scriptConfig: CallConfig;
            if (isOutbound) {
                scriptConfig = getOutboundScriptConfig(nextCallConfig);
                console.log(`✅ [OUTBOUND] Using nextCallConfig for outbound call`);
            } else {
                console.log(`🔍 [INBOUND] Loading script configuration for incoming call`);
                const result = getInboundScriptConfig(scriptManager, From);
                scriptConfig = result.config!;
            }

            // Create call configuration
            const callConfig = createCallConfig(scriptConfig, request.body, isOutbound);

            // ATOMIC STORAGE: Store configuration by CallSid to prevent race conditions
            console.log(`🔒 [${CallSid}] Storing call configuration atomically`);
            
            // Store call-specific configuration with timestamp for cleanup
            const configWithMetadata = {
                ...callConfig,
                callSid: CallSid,
                timestamp: new Date().toISOString(),
                createdAt: Date.now()
            };
            
            callConfigurations.set(CallSid, configWithMetadata);

            // Notify any waiting listeners that configuration is ready
            callConfigEmitter.emit(CallSid, configWithMetadata);
            
            // Also store in global state for backward compatibility (legacy outbound calls)
            nextCallConfig = callConfig;
            if ((fastify as any).setNextCallConfig) {
                (fastify as any).setNextCallConfig(callConfig);
            }
            
            console.log(`✅ [${CallSid}] Configuration stored successfully. Active configs: ${callConfigurations.size}`);

            // Log configuration details
            logCallConfiguration(callConfig, scriptConfig, isOutbound);

            // Construct WebSocket URL
            const wsUrlResult = constructWebSocketUrl(request, isOutbound, PUBLIC_URL);
            if (wsUrlResult.error) {
                console.error(`❌ [${CallSid}] ${wsUrlResult.error}`);
                throw new Error(wsUrlResult.error);
            }

            console.log(`🔗 [${isOutbound ? 'OUTBOUND' : 'INBOUND'}] WebSocket URL: ${wsUrlResult.url}`);
            console.log(`🔗 [${CallSid}] PUBLIC_URL: ${PUBLIC_URL || 'NOT SET'}`);

            // Generate TwiML response
            const escapedWsUrl = escapeXmlUrl(wsUrlResult.url);
            const twiml = generateTwiML(escapedWsUrl);

            console.log(`📄 [${isOutbound ? 'OUTBOUND' : 'INBOUND'}] Returning TwiML:`, twiml);
            reply.type('text/xml');
            return twiml;

        } catch (error) {
            console.error('❌ [INBOUND] Error handling incoming call:', error);
            reply.status(500);

            const errorTwiml = generateErrorTwiML();
            reply.type('text/xml');
            return errorTwiml;
        }
    });

    // CALL STATUS WEBHOOK - Handle call status updates from Twilio
    fastify.post<{ Body: CallStatusBody }>('/call-status', async (request, reply) => {
        try {
            // Validate Twilio webhook signature for security
            if (!validateTwilioWebhook(request)) {
                console.error('❌ Invalid Twilio webhook signature');
                reply.status(403);
                return { error: 'Forbidden' };
            }
            console.log('✅ Call status webhook signature validated successfully');
            
            // Validate request body exists
            if (!request.body) {
                reply.status(400);
                return { error: 'Request body is required' };
            }
            
            const { CallSid, CallStatus, Duration, From, To } = request.body;

            if (!CallSid) {
                reply.status(400);
                return { error: 'Missing CallSid' };
            }

            console.log(`📊 [${CallSid}] Call status update: ${CallStatus}`);

            // Log detailed call information
            if (Duration) {
                console.log(`⏱️ [${CallSid}] Call duration: ${Duration} seconds`);
            }

            // Handle call status logging
            logCallStatus(CallSid, CallStatus, Duration, From, To);
            
            // Handle session cleanup for completed/failed calls
            if (['completed', 'failed', 'canceled', 'no-answer', 'busy'].includes(CallStatus)) {
                await cleanupSession(CallSid, contextManager, activeConnections, lifecycleManager);
                
                // ATOMIC CLEANUP: Remove call-specific configuration when call ends
                if (callConfigurations.has(CallSid)) {
                    callConfigurations.delete(CallSid);
                    console.log(`🧹 [${CallSid}] Configuration cleaned up on call ${CallStatus}. Remaining: ${callConfigurations.size}`);
                }
            }

            // Store call status for analytics
            updateCallStatusInContext(CallSid, CallStatus, Duration, contextManager);

            return { success: true, callSid: CallSid, status: CallStatus };

        } catch (error) {
            console.error('❌ Error handling call status webhook:', error);
            reply.status(500);
            return { error: 'Internal server error', message: (error as Error).message };
        }
    });

    // ATOMIC CONFIGURATION ACCESS: Support both per-call and legacy global configuration
    fastify.decorate('getNextCallConfig', async (callSid?: string): Promise<CallConfig | null> => {
        // If callSid provided, try to get call-specific configuration first
        if (callSid && callConfigurations.has(callSid)) {
            const config = callConfigurations.get(callSid);
            console.log(`🔍 Retrieved call-specific config for ${callSid}`);
            return config || null;
        }
        
        // Fallback to global configuration for backward compatibility
        console.log(`🔍 Using global configuration (no callSid or call-specific config not found)`);
        return nextCallConfig;
    });
    
    fastify.decorate('setNextCallConfig', (config: any, callSid?: string) => {
        // If callSid provided, store call-specific configuration
        if (callSid) {
            const configWithMetadata = {
                ...config,
                callSid,
                timestamp: new Date().toISOString(),
                createdAt: Date.now()
            };
            callConfigurations.set(callSid, configWithMetadata);
            console.log(`🔒 Stored call-specific config for ${callSid}`);
        }
        
        // Always update global config for backward compatibility
        nextCallConfig = { ...nextCallConfig, ...config };
    });
    
    // Configuration cleanup helper
    fastify.decorate('cleanupCallConfig', (callSid: string) => {
        if (callConfigurations.has(callSid)) {
            callConfigurations.delete(callSid);
            console.log(`🧹 Cleaned up configuration for ${callSid}. Remaining: ${callConfigurations.size}`);
        }
    });
    
    // Periodic cleanup of old configurations (prevent memory leaks)
    const CONFIG_CLEANUP_INTERVAL = 30 * 60 * 1000; // 30 minutes
    const CONFIG_MAX_AGE = 60 * 60 * 1000; // 1 hour
    
    setInterval(() => {
        const now = Date.now();
        let cleanedCount = 0;
        
        for (const [callSid, config] of callConfigurations.entries()) {
            if (config.createdAt && (now - config.createdAt) > CONFIG_MAX_AGE) {
                callConfigurations.delete(callSid);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            console.log(`🧹 Periodic cleanup: removed ${cleanedCount} old configurations. Remaining: ${callConfigurations.size}`);
        }
    }, CONFIG_CLEANUP_INTERVAL);

    console.log('✅ Webhook routes registered successfully!');
}

// Helper functions for call status handling
function logCallStatus(callSid: string, status: string, duration?: string, from?: string, to?: string): void {
    switch (status) {
    case 'initiated':
        console.log(`🚀 [${callSid}] Call initiated from ${from} to ${to}`);
        break;
    case 'ringing':
        console.log(`📞 [${callSid}] Call ringing`);
        break;
    case 'answered':
        console.log(`✅ [${callSid}] Call answered`);
        break;
    case 'completed':
        console.log(`🏁 [${callSid}] Call completed (Duration: ${duration}s)`);
        break;
    case 'failed':
    case 'canceled':
    case 'no-answer':
    case 'busy':
        console.log(`❌ [${callSid}] Call ${status}`);
        break;
    default:
        console.log(`ℹ️ [${callSid}] Unknown call status: ${status}`);
    }
}

async function cleanupSession(
    callSid: string,
    contextMgr: any,
    activeConnections: Map<string, any>,
    lifecycleManager: any
): Promise<void> {
    console.log(`🧹 [${callSid}] Cleaning up session due to call completion`);

    try {
        // Check if we have an active session to clean up
        const connectionData = activeConnections?.get(callSid);
        if (connectionData && lifecycleManager) {
            // Properly end session with summary generation
            console.log(`🔚 [${callSid}] Requesting proper session end with summary`);
            await lifecycleManager.endSession(callSid, connectionData, 'call_completed');
        } else if (connectionData) {
            // Fallback to basic cleanup if no lifecycle manager
            console.log(`🔚 [${callSid}] Basic session cleanup (no lifecycle manager)`);
            if (contextMgr) {
                contextMgr.clearSessionContext(callSid);
            }
            activeConnections.delete(callSid);
        } else {
            // No active session found - just clear context
            console.log(`ℹ️ [${callSid}] No active session found, clearing context only`);
            if (contextMgr) {
                contextMgr.clearSessionContext(callSid);
            }
        }
    } catch (error) {
        console.error(`❌ [${callSid}] Error during session cleanup:`, error);
        // Fallback cleanup
        if (contextMgr) {
            contextMgr.clearSessionContext(callSid);
        }
        if (activeConnections) {
            activeConnections.delete(callSid);
        }
    }
}

function updateCallStatusInContext(
    callSid: string,
    status: string,
    duration: string | undefined,
    contextMgr: any
): void {
    try {
        if (contextMgr) {
            const context = contextMgr.getSessionContext(callSid) || {};
            context.callStatus = status;
            context.duration = duration;
            context.lastStatusUpdate = new Date().toISOString();
            contextMgr.saveSessionContext(callSid, context);
        }
    } catch (error) {
        console.warn(`⚠️ [${callSid}] Error updating call status in context:`, error);
    }
}