// Analytics and metrics routes
import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { Dependencies } from './types/api-types.js';
import { AudioProcessor } from '../audio/audio-processor.js';
import { TranscriptionManager } from './transcription-manager.js';

export function registerAnalyticsRoutes(fastify: FastifyInstance, dependencies: Dependencies): void {
    console.log('📊 Registering analytics routes...');
    
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        recoveryManager,
        lifecycleManager,
        voiceManager,
        modelManager
    } = dependencies;

    // Get system analytics
    fastify.get('/api/analytics/system', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const analytics = {
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                activeConnections: activeConnections.size,
                sessionStats: {
                    total: contextManager.getContextStats().totalContexts || 0,
                    active: lifecycleManager ? lifecycleManager.getActiveSessions().length : 0,
                    recovered: recoveryManager ? recoveryManager.getRecoveryMetrics().successfulRecoveries : 0
                },
                audioStats: {
                    // Get audio processing stats from AudioProcessor
                    qualityMonitor: (AudioProcessor as any).audioQualityMonitor
                        ? (AudioProcessor as any).audioQualityMonitor.getSummary()
                        : null
                },
                voiceModelStats: {
                    availableVoices: Object.keys(voiceManager.getAvailableVoices()).length,
                    currentVoice: voiceManager.getDefaultVoice(),
                    availableModels: Object.keys(modelManager.getAvailableModels()).length,
                    currentModel: modelManager.getCurrentModel()
                },
                timestamp: new Date().toISOString()
            };

            return {
                success: true,
                analytics,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting system analytics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Get audio processing metrics
    fastify.get('/api/analytics/audio', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const audioAnalytics = {
                qualityMonitor: (AudioProcessor as any).audioQualityMonitor
                    ? (AudioProcessor as any).audioQualityMonitor.getSummary()
                    : null,
                enhancerStats: (AudioProcessor as any).audioEnhancer
                    ? (AudioProcessor as any).audioEnhancer.getProcessingStats()
                    : null,
                transcriptionHealth: null as any
            };

            // Get transcription health if available
            try {
                const transcriptionManager = new TranscriptionManager();
                audioAnalytics.transcriptionHealth = await transcriptionManager.healthCheck();
            } catch (error) {
                console.warn('⚠️ Could not get transcription health:', (error as Error).message);
            }

            return {
                success: true,
                audioAnalytics,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting audio analytics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Get session analytics
    fastify.get('/api/analytics/sessions', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const sessionAnalytics = {
                contextStats: contextManager.getContextStats(),
                lifecycleStats: lifecycleManager ? {
                    activeSessions: lifecycleManager.getActiveSessions()
                } : null,
                recoveryStats: recoveryManager ? recoveryManager.getRecoveryMetrics() : null,
                healthScore: healthMonitor ? healthMonitor.getHealthScore() : 100,
                activeConnections: Array.from(activeConnections.keys())
            };

            return {
                success: true,
                sessionAnalytics,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting session analytics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Reset audio processing statistics
    fastify.post('/api/analytics/audio/reset', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            // Reset audio quality monitor
            if ((AudioProcessor as any).audioQualityMonitor && (AudioProcessor as any).audioQualityMonitor.reset) {
                (AudioProcessor as any).audioQualityMonitor.reset();
            }

            // Reset audio enhancer stats
            if ((AudioProcessor as any).audioEnhancer && (AudioProcessor as any).audioEnhancer.resetProcessingStats) {
                (AudioProcessor as any).audioEnhancer.resetProcessingStats();
            }

            return {
                success: true,
                message: 'Audio processing statistics reset',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error resetting audio statistics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Get comprehensive system metrics (for monitoring/alerting)
    fastify.get('/api/metrics', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const metrics = {
                system: {
                    uptime: process.uptime(),
                    memory: process.memoryUsage(),
                    cpu: process.cpuUsage(),
                    version: process.version,
                    platform: process.platform
                },
                application: {
                    activeConnections: activeConnections.size,
                    healthScore: healthMonitor ? healthMonitor.getHealthScore() : 100,
                    contextStats: contextManager.getContextStats(),
                    nextCallConfig: await (async () => {
                        const config = await (fastify as any).getNextCallConfig();
                        return config ? {
                            voice: config.voice,
                            model: config.model,
                            hasInstructions: !!config.aiInstructions,
                            hasTarget: !!config.targetPhoneNumber
                        } : null;
                    })()
                },
                services: {
                    sessionManager: sessionManager ? 'available' : 'unavailable',
                    contextManager: contextManager ? 'available' : 'unavailable',
                    healthMonitor: healthMonitor ? 'available' : 'unavailable',
                    recoveryManager: recoveryManager ? 'available' : 'unavailable',
                    lifecycleManager: lifecycleManager ? 'available' : 'unavailable'
                },
                timestamp: new Date().toISOString()
            };

            return metrics;
        } catch (error) {
            console.error('❌ Error getting system metrics:', error);
            reply.status(500);
            return { error: 'Failed to get system metrics', message: (error as Error).message };
        }
    });

    // Audio quality metrics endpoint
    fastify.get('/api/audio-quality', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            let qualityMetrics;
            try {
                qualityMetrics = (AudioProcessor as any).audioQualityMonitor?.getSummary();
            } catch (e) {
                qualityMetrics = null;
            }
            
            const audioQuality = qualityMetrics || {
                sampleRate: 16000,
                bitDepth: 16,
                channels: 1,
                format: 'PCM',
                averageLatency: 50,
                packetsProcessed: 0,
                errors: 0
            };
            
            return {
                success: true,
                audioQuality: audioQuality,
                debugMode: process.env.AUDIO_DEBUG === 'true',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting audio quality metrics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Reset audio quality metrics endpoint
    fastify.post('/api/audio-quality/reset', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            // Check if audioQualityMonitor exists before calling reset
            const audioQualityMonitor = (AudioProcessor as any).audioQualityMonitor;
            if (audioQualityMonitor && typeof audioQualityMonitor.reset === 'function') {
                audioQualityMonitor.reset();
            }
            return {
                success: true,
                message: 'Audio quality metrics reset successfully',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error resetting audio quality metrics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Provider health check
    fastify.get('/api/provider-health', async (_request: FastifyRequest, _reply: FastifyReply) => {
        // This would check Gemini API health, Twilio connectivity, etc.
        return {
            success: true,
            providers: {
                gemini: {
                    status: 'healthy',
                    lastCheck: new Date().toISOString()
                },
                twilio: {
                    status: 'healthy',
                    lastCheck: new Date().toISOString()
                }
            }
        };
    });

    // Analytics endpoint
    fastify.get('/analytics', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            return {
                success: true,
                totalOutboundCalls: 0,
                totalInboundCalls: 0,
                callHistory: [] as any[],
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting analytics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    console.log('✅ Analytics routes registered successfully');
}