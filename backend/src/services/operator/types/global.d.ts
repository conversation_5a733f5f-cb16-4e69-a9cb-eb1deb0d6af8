// Global type definitions for the Twilio Gemini Live API project

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { WebSocket } from 'ws';

// Configuration types
export interface ServerConfig {
  port: number;
  host: string;
  publicUrl: string;
  environment: 'development' | 'production' | 'test';
}

export interface AuthConfig {
  twilio: {
    accountSid: string;
    authToken: string;
  };
  gemini: {
    apiKey: string;
  };
  deepgram: {
    apiKey: string;
  };
  supabase: {
    url: string;
    anonKey: string;
  };
}

export interface AIConfig {
  gemini: {
    defaultModel: string;
    availableModels: string[];
    modelSelectionEnabled: boolean;
    defaultVoice: string;
    voiceSelectionEnabled: boolean;
    outputRate: number;
    maxTokens: number;
    temperature: number;
    topP: number;
    topK: number;
    voices: {
      [voiceName: string]: string;
    };
  };
}

export interface PromptsConfig {
  summaryGeneration: string;
  aiPrepareOutbound: string;
  incomingCallGreeting: string;
}

export interface AppConfig {
  environment: {
    nodeEnv: string;
    isDevelopment: boolean;
    isProduction: boolean;
    debugLevel: string;
    enableDetailedLogging: boolean;
  };
  server: ServerConfig & {
    corsOrigin: string;
    frontendUrl: string;
  };
  auth: AuthConfig & {
    openai: {
      apiKey?: string;
      apiUrl: string;
    };
    ngrok: {
      authToken?: string;
    };
  };
  twilio: {
    phoneNumbers: {
      default?: string;
      us?: string;
      cz?: string;
      es?: string;
    };
    defaultPhoneNumber?: string;
    webhooks: {
      voiceUrl?: string;
      statusCallbackUrl?: string;
      recordingStatusCallbackUrl?: string;
    };
  };
  ai: AIConfig & {
    openai: {
      model: string;
      chatModel: string;
      voice: string;
      temperature: number;
    };
  };
  audio: {
    inputFormat: string;
    outputFormat: string;
    sampleRate: number;
    twilioSampleRate: number;
    greetingAudioUrl?: string;
  };
  websocket: {
    protocol: string;
    url?: string;
    heartbeatInterval: number;
    heartbeatTimeout: number;
  };
  transcription: {
    enabled: boolean;
    model: string;
    language: string;
    responseFormat: string;
  };
  campaigns: {
    scriptsPath: string;
    totalCampaigns: number;
    defaultCampaignId: number;
    enableCustomScripts: boolean;
    scriptCacheTimeout: number;
  };
  localization: {
    defaultLanguage: string;
    supportedLanguages: string[];
    enableMultiLanguage: boolean;
    fallbackLanguage: string;
  };
  voices: {
    defaultVoiceMapping: {
      [language: string]: {
        incoming: string;
        outbound: string;
      };
    };
    enableVoiceSelection: boolean;
  };
  business: {
    callTimeouts: {
      default: number;
      intro: number;
      response: number;
    };
    validation: {
      maxVehicles: number;
      maxClaims: number;
      minVehicleYear: number;
      maxVehicleYear: number;
    };
    transfer: {
      defaultTransferNumber: string;
      defaultAgentName: string;
      transferTimeout: number;
    };
  };
  prompts: PromptsConfig & {
    aiPrepareIncoming: string;
    systemMessageBio: string;
    systemMessageVoicePersonality: string;
    systemMessageVoiceSpeed: string;
  };
  security: {
    vocabularyRestrictions: string[];
    enableRecordingConfirmation: boolean;
    recordingConfirmationMessage: string;
  };
  performance: {
    enableCaching: boolean;
    cacheTimeout: number;
    maxConcurrentCalls: number;
    enableMetrics: boolean;
  };
  timeouts: {
    sessionCreation: number;
    sessionRecovery: number;
    sessionSummary: number;
    sessionSummaryWait: number;
    recoveryLock: number;
    recoveryBaseDelay: number;
    recoveryMaxDelay: number;
    recoveryErrorDelay: number;
    recoveryErrorMaxDelay: number;
    healthCheckInterval: number;
    maxConnectionAge: number;
    staleConnectionThreshold: number;
    scriptCacheTimeout: number;
    configCacheTimeout: number;
    testCallDuration: number;
    nonceMaxAge: number;
    rateLimitWindow: number;
    rateLimitCleanupInterval: number;
    parallelInitTimeout: number;
    slowOperationThreshold: number;
    performanceMonitorInterval: number;
  };
  limits: {
    audioClippingThreshold: number;
    audioSilenceThreshold: number;
    maxPayloadSize: number;
    maxRateLimitRequests: number;
    maxScriptLength: number;
    maxRetryDelay: number;
    maxRecoveryAttempts: number;
    recoveryJitterPercent: number;
    performanceErrorThreshold: number;
  };
}

// Session types
export interface SessionData {
  sessionId: string;
  callSid?: string;
  phoneNumber?: string;
  mode: 'outbound' | 'inbound' | 'outbound-testing' | 'inbound-testing';
  status: 'active' | 'ended' | 'error';
  startTime: Date;
  endTime?: Date;
  metadata?: Record<string, any>;
}

export interface ExtendedConnectionData extends ConnectionData {
  aiInstructions?: string;
  voice?: string;
  model?: string;
  sessionConfig?: any;
  geminiSessionError?: string;
}

export interface ConnectionData {
  ws?: WebSocket;
  twilioWs?: WebSocket;
  localWs?: WebSocket;
  sessionId: string;
  callSid?: string;
  streamSid?: string;
  sequenceNumber?: number;
  isSessionActive: boolean;
  summaryRequested: boolean;
  summaryReceived: boolean;
  summaryText: string;
  conversationLog: ConversationEntry[];
  fullTranscript: TranscriptEntry[];
  speechTranscript: SpeechTranscriptEntry[];
  isIncomingCall?: boolean;
  sessionType?: 'local_test' | 'twilio_call';
  flowType: string;
  sessionStartTime: number;
  lastActivity: number;
  targetName: string;
  targetPhoneNumber: string;
  originalAIInstructions: string;
  scriptId: string;
  isTestMode?: boolean;
  isTwilioCall?: boolean;
  lastAIResponse: number;
  responseTimeouts: number;
  connectionQuality: string;
  lastContextSave: number;
  contextSaveInterval: NodeJS.Timeout | null;
  audioForwardingEnabled?: boolean;
  lastAudioSent?: number;
  geminiSession?: GeminiSession;
  deepgramConnection?: DeepgramConnection;
  twilioConnected?: boolean;
  wsDisconnected?: boolean;
  lastDisconnectTime?: number;
  wsError?: boolean;
  lastErrorTime?: number;
  callCompleted?: boolean;
  stopReceived?: boolean;
  heartbeatTimeout?: boolean;
  lastHeartbeatTimeout?: number;
  lastMediaError?: number;
  mediaErrorCount?: number;
  // Legacy properties for backward compatibility
  mode?: string;
  phoneNumber?: string;
  isActive?: boolean;
  startTime?: Date;
  // Memory management properties
  maxConversationLogSize?: number;
  maxTranscriptSize?: number;
  maxSpeechTranscriptSize?: number;
  metadata?: Record<string, any>;
  geminiWs?: WebSocket;
  // Recovery properties
  lastRecoveryTime?: number;
}

// Conversation and transcript types
export interface ConversationEntry {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  messageId?: string;
  confidence?: number;
}

export interface TranscriptEntry {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  confidence?: number;
  source?: 'speech' | 'text' | 'ai';
}

export interface SpeechTranscriptEntry {
  text: string;
  timestamp: number;
  confidence: number;
  isFinal?: boolean;
  speaker?: 'user' | 'assistant';
}

// Audio types
export interface AudioChunk {
  data: Buffer;
  timestamp: number;
  sequenceNumber?: number;
}

export interface AudioConfig {
  sampleRate: number;
  channels: number;
  bitDepth: number;
  encoding: string;
}

// Deepgram types
export interface DeepgramConnection {
  send(data: Buffer | string): void;
  close(): void;
  on(event: string, callback: (...args: any[]) => void): void;
  removeAllListeners(): void;
}

// WebSocket message types
export interface WebSocketMessage {
  type: string;
  data?: string | Buffer | object;
  sessionId?: string;
  timestamp?: number;
}

// Re-export Twilio message definition from the websocket types module
export { TwilioMediaMessage } from './websocket.js';

// Gemini API types
export interface GeminiMessage {
  role: 'user' | 'model';
  parts: Array<{
    text?: string;
    inlineData?: {
      mimeType: string;
      data: string;
    };
  }>;
}

export interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
      role: string;
    };
    finishReason: string;
    index: number;
  }>;
}

// Enhanced Gemini Live API types
export interface GeminiLiveMessage {
  serverContent?: {
    modelTurn?: {
      parts?: Array<{
        text?: string;
        inlineData?: {
          mimeType: string;
          data: string;
        };
      }>;
    };
    turnComplete?: boolean;
  };
  clientContent?: {
    turns?: Array<{
      role: 'user' | 'model';
      parts: Array<{
        text?: string;
        inlineData?: {
          mimeType: string;
          data: string;
        };
      }>;
    }>;
    turnComplete?: boolean;
  };
  setupComplete?: boolean;
  interrupted?: boolean;
}

export interface GeminiRealtimeInput {
  media?: {
    data: string;
    mimeType: string;
  };
}

export interface GeminiSession {
  send(message: GeminiLiveMessage): Promise<void>;
  sendRealtimeInput(input: GeminiRealtimeInput): Promise<void>;
  sendClientContent?(content: GeminiLiveMessage): Promise<void>;
  close(): void;
}

export interface GeminiClient {
  live?: {
    connect(config: {
      model: string;
      systemInstruction?: {
        parts: Array<{ text: string }>;
      };
      generationConfig?: any;
      voice?: string;
      callbacks?: {
        onopen?: () => void;
        onmessage?: (message: GeminiLiveMessage) => void;
        onerror?: (error: GeminiError) => void;
        onclose?: (event: CloseEvent) => void;
      };
    }): Promise<GeminiSession>;
  };
}

export interface GeminiError {
  message: string;
  code?: string | number;
  details?: string;
  stack?: string;
}

// Script and campaign types
export interface CampaignScript {
  id: string;
  name: string;
  content: string;
  language: string;
  voice?: string;
  model?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CallConfig {
  phoneNumber: string;
  script?: CampaignScript;
  voice?: string;
  model?: string;
  mode: 'outbound' | 'inbound' | 'outbound-testing' | 'inbound-testing';
  metadata?: Record<string, any>;
  createdAt?: number;
}

// Manager interfaces
export interface SessionManager {
  createSession(sessionId: string, connectionData: ConnectionData): Promise<void>;
  getSession(sessionId: string): SessionData | undefined;
  updateSession(sessionId: string, updates: Partial<SessionData>): Promise<void>;
  endSession(sessionId: string): Promise<void>;
  cleanupSession(sessionId: string): void;
  getActiveSessions(): SessionData[];
}

export interface SessionContext {
  sessionId: string;
  conversationLog: ConversationEntry[];
  fullTranscript: TranscriptEntry[];
  speechTranscript: SpeechTranscriptEntry[];
  aiInstructions?: string;
  sessionType?: string;
  flowType?: string;
  startTime: number;
  lastActivity: number;
  metadata?: Record<string, unknown>;
  maxConversationLogSize?: number;
  maxTranscriptSize?: number;
  maxSpeechTranscriptSize?: number;
}

export interface ContextManager {
  setContext(sessionId: string, context: SessionContext): void;
  getContext(sessionId: string): SessionContext | null;
  updateContext(sessionId: string, updates: Partial<SessionContext>): void;
  clearContext(sessionId: string): void;
  clearAllContexts(): void;
  cleanupOldContexts(): void;
}

// Fastify extensions
declare module 'fastify' {
  interface FastifyInstance {
    getNextCallConfig?: () => Promise<CallConfig | null>;
    callConfigEmitter?: import('events').EventEmitter;
  }
}

// Environment variables
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: 'development' | 'production' | 'test';
      PORT: string;
      PUBLIC_URL: string;
      TWILIO_ACCOUNT_SID: string;
      TWILIO_AUTH_TOKEN: string;
      GEMINI_API_KEY: string;
      DEEPGRAM_API_KEY: string;
      SUPABASE_URL: string;
      SUPABASE_ANON_KEY: string;
    }
  }
}

export {};
