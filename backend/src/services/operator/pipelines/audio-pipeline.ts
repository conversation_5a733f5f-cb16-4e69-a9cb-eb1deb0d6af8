import { EventDrivenPipeline } from '@/modules/operator/event-system/event-patterns';
import { typedEventBus } from '@/modules/operator/event-system/event-patterns';
import { AudioConversionOrchestrator } from './converters/audio-conversion-orchestrator.js';
import { AudioQualityOrchestrator } from './quality/audio-quality-orchestrator.js';
import { audioLogger } from '@/utils/logger';

export interface AudioPipelineInput {
    sessionId: string;
    audioBuffer: Buffer;
    format: 'ulaw' | 'pcm16' | 'browser';
    sourceType: 'twilio' | 'gemini' | 'browser';
    metadata?: Record<string, any>;
}

export interface AudioPipelineOutput {
    sessionId: string;
    processedBuffer: Buffer | string;
    format: string;
    quality: {
        score: number;
        warnings: string[];
    };
    duration: number;
}

/**
 * Audio processing pipeline
 * Orchestrates audio flow through validation, conversion, quality analysis, and routing
 */
export class AudioPipeline {
    private conversionOrchestrator: AudioConversionOrchestrator;
    private qualityOrchestrator: AudioQualityOrchestrator;
    private pipeline: EventDrivenPipeline<AudioPipelineInput, AudioPipelineOutput>;
    
    constructor() {
        this.conversionOrchestrator = new AudioConversionOrchestrator();
        this.qualityOrchestrator = new AudioQualityOrchestrator();
        
        // Define pipeline stages
        this.pipeline = new EventDrivenPipeline('audio-pipeline', [
            this.validateAudio.bind(this),
            this.analyzeQuality.bind(this),
            this.convertFormat.bind(this),
            this.routeAudio.bind(this)
        ]);
        
        // Subscribe to pipeline events
        this.setupEventHandlers();
    }
    
    private setupEventHandlers(): void {
        typedEventBus.on('audio:received', async (payload) => {
            try {
                await this.processAudio({
                    sessionId: payload.sessionId,
                    audioBuffer: payload.buffer,
                    format: payload.format as 'ulaw' | 'pcm16' | 'browser',
                    sourceType: 'twilio'
                });
            } catch (error) {
                audioLogger.error('Error processing audio in pipeline:', error instanceof Error ? error : new Error(String(error)));
            }
        });
    }
    
    async processAudio(input: AudioPipelineInput): Promise<AudioPipelineOutput> {
        return this.pipeline.execute(input);
    }
    
    private async validateAudio(input: AudioPipelineInput): Promise<AudioPipelineInput> {
        const validation = await this.qualityOrchestrator.validateAndAnalyze(
            input.audioBuffer,
            input.format,
            input.sessionId
        );
        
        if (!validation.isValid) {
            throw new Error(`Audio validation failed: ${validation.error}`);
        }
        
        typedEventBus.emit('audio:validation:passed', {
            sessionId: input.sessionId,
            format: input.format,
            size: input.audioBuffer.length
        });
        
        return input;
    }
    
    private async analyzeQuality(input: AudioPipelineInput): Promise<AudioPipelineInput & { quality: any }> {
        const metrics = this.qualityOrchestrator.analyzeAudioQuality(
            input.audioBuffer,
            input.format,
            input.sessionId
        );
        
        const qualityScore = metrics ? 
            this.calculateQualityScore(metrics) : 0.5;
        
        return {
            ...input,
            quality: {
                score: qualityScore,
                metrics,
                warnings: this.getQualityWarnings(metrics)
            }
        };
    }
    
    private async convertFormat(input: AudioPipelineInput & { quality: any }): Promise<AudioPipelineInput & { quality: any; convertedBuffer?: Buffer | string }> {
        let convertedBuffer: Buffer | string = input.audioBuffer;
        
        try {
            if (input.sourceType === 'twilio' && input.format === 'ulaw') {
                // Convert Twilio μ-law to Gemini PCM
                convertedBuffer = await this.conversionOrchestrator.twilioToGemini(input.audioBuffer);
            } else if (input.sourceType === 'gemini') {
                // Convert Gemini PCM to Twilio μ-law
                convertedBuffer = await this.conversionOrchestrator.geminiToTwilio(
                    input.audioBuffer.toString('base64')
                );
            } else if (input.sourceType === 'browser') {
                // Convert browser audio to Gemini format
                convertedBuffer = await this.conversionOrchestrator.browserToGemini(
                    input.audioBuffer.toString('base64')
                );
            }
            
            typedEventBus.emit('audio:conversion:completed', {
                sessionId: input.sessionId,
                from: input.format,
                to: input.sourceType === 'twilio' ? 'pcm16_24khz' : 'ulaw_8khz'
            });
            
        } catch (error) {
            audioLogger.error(`Format conversion failed for ${input.sessionId}:`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
        
        return {
            ...input,
            convertedBuffer
        };
    }
    
    private async routeAudio(input: AudioPipelineInput & { quality: any; convertedBuffer?: Buffer | string }): Promise<AudioPipelineOutput> {
        const outputBuffer = input.convertedBuffer || input.audioBuffer;
        
        // Emit routing event based on source
        if (input.sourceType === 'twilio') {
            typedEventBus.emit('audio:forwarded', {
                sessionId: input.sessionId,
                destination: 'gemini',
                size: outputBuffer.length
            });
        } else if (input.sourceType === 'gemini') {
            typedEventBus.emit('audio:forwarded', {
                sessionId: input.sessionId,
                destination: 'twilio',
                size: outputBuffer.length
            });
        }
        
        return {
            sessionId: input.sessionId,
            processedBuffer: outputBuffer,
            format: input.sourceType === 'twilio' ? 'pcm16_24khz' : 'ulaw_8khz',
            quality: {
                score: input.quality.score,
                warnings: input.quality.warnings
            },
            duration: this.calculateDuration(outputBuffer, input.format)
        };
    }
    
    private calculateQualityScore(metrics: any): number {
        if (!metrics) return 0.5;
        
        let score = 1.0;
        
        if (metrics.silencePercentage > 80) score *= 0.7;
        if (metrics.clippingPercentage > 5) score *= 0.8;
        if (metrics.snr < 20) score *= 0.9;
        
        return Math.max(0, Math.min(1, score));
    }
    
    private getQualityWarnings(metrics: any): string[] {
        const warnings: string[] = [];
        
        if (!metrics) return warnings;
        
        if (metrics.silencePercentage > 80) {
            warnings.push('Excessive silence detected');
        }
        
        if (metrics.clippingPercentage > 5) {
            warnings.push('Audio clipping detected');
        }
        
        if (metrics.snr < 20) {
            warnings.push('Poor signal quality');
        }
        
        return warnings;
    }
    
    private calculateDuration(buffer: Buffer | string, format: string): number {
        const bufferLength = typeof buffer === 'string' ? 
            Buffer.from(buffer, 'base64').length : buffer.length;
        
        switch (format) {
            case 'ulaw':
                return bufferLength / 8000; // 8kHz, 1 byte per sample
            case 'pcm16':
                return bufferLength / (24000 * 2); // 24kHz, 2 bytes per sample
            default:
                return 0;
        }
    }
    
    cleanup(): void {
        this.conversionOrchestrator.cleanup();
        this.qualityOrchestrator.cleanup();
    }
}