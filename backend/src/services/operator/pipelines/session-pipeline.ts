import { EventDrivenPipeline } from '@/modules/operator/event-system/event-patterns';
import { typedEventBus } from '@/modules/operator/event-system/event-patterns';
import { SessionCreator } from '../handlers/session/create-session.js';
import { SessionRecoveryHandler } from '../handlers/session/recover-session.js';
import { SessionCleanupHandler } from '../handlers/session/cleanup-session.js';
import { SessionMetricsService } from '../session/metrics-service.js';
import { sessionLogger } from '@/utils/logger';
import { SessionConfig } from './types/websocket.js';
import { ConnectionData, GeminiClient } from './types/global.js';

export interface SessionPipelineInput {
    callSid: string;
    config: SessionConfig;
    connectionData: ConnectionData;
    action: 'create' | 'recover' | 'end';
    reason?: string;
}

export interface SessionPipelineOutput {
    sessionId: string;
    status: 'created' | 'recovered' | 'ended' | 'failed';
    duration?: number;
    metrics?: any;
    error?: string;
}

/**
 * Session lifecycle pipeline
 * Orchestrates session creation, recovery, and cleanup
 */
export class SessionPipeline {
    private sessionCreator: SessionCreator;
    private recoveryHandler: SessionRecoveryHandler;
    private cleanupHandler: SessionCleanupHandler;
    private metricsService: SessionMetricsService;
    private pipeline: EventDrivenPipeline<SessionPipelineInput, SessionPipelineOutput>;
    
    constructor(private geminiClient: GeminiClient) {
        this.sessionCreator = new SessionCreator(geminiClient);
        this.recoveryHandler = new SessionRecoveryHandler();
        this.cleanupHandler = new SessionCleanupHandler();
        this.metricsService = new SessionMetricsService();
        
        // Define pipeline stages
        this.pipeline = new EventDrivenPipeline('session-pipeline', [
            this.validateInput.bind(this),
            this.executeAction.bind(this),
            this.collectMetrics.bind(this),
            this.finalizeSession.bind(this)
        ]);
        
        // Subscribe to session events
        this.setupEventHandlers();
    }
    
    private setupEventHandlers(): void {
        // Handle call events that trigger session actions
        typedEventBus.on('call:incoming', async (payload) => {
            sessionLogger.info(`Processing incoming call: ${payload.callSid}`);
        });
        
        typedEventBus.on('call:ended', async (payload) => {
            await this.processSession({
                callSid: payload.callSid,
                action: 'end',
                reason: payload.reason,
                config: {} as SessionConfig,
                connectionData: {} as ConnectionData
            });
        });
    }
    
    async processSession(input: SessionPipelineInput): Promise<SessionPipelineOutput> {
        try {
            return await this.pipeline.execute(input);
        } catch (error) {
            sessionLogger.error(`Session pipeline error for ${input.callSid}:`, error instanceof Error ? error : new Error(String(error)));
            
            typedEventBus.emit('session:error', {
                sessionId: input.callSid,
                error: error instanceof Error ? error : new Error(String(error)),
                context: 'session_pipeline'
            });
            
            return {
                sessionId: input.callSid,
                status: 'failed',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    
    private async validateInput(input: SessionPipelineInput): Promise<SessionPipelineInput> {
        // Validate required fields based on action
        if (input.action === 'create') {
            if (!input.config || !input.connectionData) {
                throw new Error('Missing config or connection data for session creation');
            }
        }
        
        if (input.action === 'recover' && !input.reason) {
            input.reason = 'Unknown recovery reason';
        }
        
        return input;
    }
    
    private async executeAction(input: SessionPipelineInput): Promise<SessionPipelineInput & { result: any }> {
        let result: any = {};
        
        switch (input.action) {
            case 'create':
                sessionLogger.info(`Creating session for ${input.callSid}`);
                const session = await this.sessionCreator.createSession(
                    input.callSid,
                    input.config,
                    input.connectionData
                );
                
                result = {
                    success: !!session,
                    session
                };
                break;
                
            case 'recover':
                sessionLogger.info(`Recovering session ${input.callSid}: ${input.reason}`);
                await this.recoveryHandler.recoverSession(
                    input.callSid,
                    input.reason || 'Unknown'
                );
                
                result = {
                    success: true,
                    recovered: true
                };
                break;
                
            case 'end':
                sessionLogger.info(`Ending session ${input.callSid}`);
                await this.cleanupHandler.cleanupSession(input.callSid);
                
                result = {
                    success: true,
                    cleaned: true
                };
                break;
                
            default:
                throw new Error(`Unknown session action: ${input.action}`);
        }
        
        return {
            ...input,
            result
        };
    }
    
    private async collectMetrics(input: SessionPipelineInput & { result: any }): Promise<SessionPipelineInput & { result: any; metrics: any }> {
        const metrics = this.metricsService.getSessionMetrics(input.callSid);
        
        return {
            ...input,
            metrics: metrics || {
                startTime: Date.now(),
                messagesReceived: 0,
                messagesSent: 0,
                recoveryCount: 0,
                lastActivity: Date.now()
            }
        };
    }
    
    private async finalizeSession(input: SessionPipelineInput & { result: any; metrics: any }): Promise<SessionPipelineOutput> {
        const output: SessionPipelineOutput = {
            sessionId: input.callSid,
            status: 'failed'
        };
        
        // Determine final status
        if (input.result.success) {
            switch (input.action) {
                case 'create':
                    output.status = 'created';
                    break;
                case 'recover':
                    output.status = 'recovered';
                    break;
                case 'end':
                    output.status = 'ended';
                    break;
            }
        }
        
        // Add metrics
        if (input.metrics) {
            output.metrics = input.metrics;
            
            if (input.action === 'end' && input.metrics.startTime) {
                output.duration = Date.now() - input.metrics.startTime;
            }
        }
        
        // Emit final event
        if (output.status === 'created') {
            typedEventBus.emit('session:created', {
                sessionId: input.callSid,
                phoneNumber: input.connectionData?.phoneNumber,
                timestamp: Date.now()
            });
        } else if (output.status === 'ended') {
            typedEventBus.emit('session:ended', {
                sessionId: input.callSid,
                reason: input.reason || 'Normal termination',
                duration: output.duration || 0
            });
        }
        
        return output;
    }
    
    cleanup(): void {
        this.sessionCreator.cleanup();
        this.recoveryHandler.cleanup();
        this.cleanupHandler.cleanup();
        this.metricsService.cleanup();
    }
}