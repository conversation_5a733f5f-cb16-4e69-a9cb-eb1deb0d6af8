import { EventDrivenComponent } from '@/modules/operator/event-system/event-patterns';
import { AudioPipeline } from './audio-pipeline.js';
import { SessionPipeline } from './session-pipeline.js';
import { CallPipeline } from './call-pipeline.js';
import { GeminiClient } from './types/global.js';
import { sessionLogger } from '@/utils/logger';

/**
 * Master orchestration pipeline
 * Coordinates all sub-pipelines and manages event flow
 */
export class OrchestrationPipeline extends EventDrivenComponent {
    private audioPipeline: AudioPipeline;
    private sessionPipeline: SessionPipeline;
    private callPipeline: CallPipeline;
    
    constructor(geminiClient: GeminiClient) {
        super('OrchestrationPipeline');
        
        // Initialize sub-pipelines
        this.audioPipeline = new AudioPipeline();
        this.sessionPipeline = new SessionPipeline(geminiClient);
        this.callPipeline = new CallPipeline();
        
        // Set up cross-pipeline event coordination
        this.setupCrossPipelineEvents();
    }
    
    private setupCrossPipelineEvents(): void {
        // Listen for call creation events
        this.subscribe('call:created', async (payload) => {
            sessionLogger.info(`Creating session for call ${payload.callSid}`);
            
            await this.sessionPipeline.processSession({
                callSid: payload.callSid,
                action: 'create',
                config: {
                    // Session config would be populated from actual data
                    model: process.env.GEMINI_DEFAULT_MODEL || 'gemini-1.5-flash-001',
                    voice: 'Puck',
                    sessionType: 'twilio_call',
                    isIncomingCall: false,
                    aiInstructions: 'Default AI instructions for the session'
                } as any,
                connectionData: {
                    phoneNumber: payload.from,
                    callSid: payload.callSid
                } as any
            });
        });
        
        // Session events trigger audio processing
        this.subscribe('session:created', (payload) => {
            sessionLogger.info(`Session created, ready for audio: ${payload.sessionId}`);
            
            // Log that audio processing can begin
            sessionLogger.info(`Audio processing enabled for session: ${payload.sessionId}`);
        });
        
        // Audio quality issues trigger session recovery
        this.subscribe('audio:quality:warning', async (payload) => {
            if (payload.warnings.includes('Poor signal quality')) {
                sessionLogger.warn(`Poor audio quality detected for ${payload.sessionId}, considering recovery`);
                
                // Could trigger session recovery if quality is too poor
                await this.sessionPipeline.processSession({
                    callSid: payload.sessionId,
                    action: 'recover',
                    reason: 'Poor audio quality',
                    config: {} as any,
                    connectionData: {} as any
                });
            }
        });
        
        // Session errors might affect call status
        this.subscribe('session:error', (payload) => {
            if (payload.context === 'session_creation') {
                sessionLogger.error(`Session creation failed for ${payload.sessionId}`);
                
                // Emit call failure event
                this.emit('call:failed', {
                    callSid: payload.sessionId,
                    error: 'Session creation failed'
                });
            }
        });
        
        // Call ending triggers session cleanup
        this.subscribe('call:ended', async (payload) => {
            sessionLogger.info(`Call ended, cleaning up session: ${payload.callSid}`);
            
            await this.sessionPipeline.processSession({
                callSid: payload.callSid,
                action: 'end',
                reason: payload.reason,
                config: {} as any,
                connectionData: {} as any
            });
        });
    }
    
    /**
     * Process incoming webhook from Twilio
     */
    async processWebhook(webhookData: any): Promise<void> {
        try {
            // Determine webhook type and route accordingly
            if (webhookData.CallSid) {
                await this.callPipeline.processCall({
                    callSid: webhookData.CallSid,
                    from: webhookData.From,
                    to: webhookData.To,
                    direction: webhookData.Direction === 'inbound' ? 'inbound' : 'outbound',
                    status: 'incoming',
                    metadata: webhookData
                });
            }
        } catch (error) {
            sessionLogger.error('Error processing webhook:', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    
    /**
     * Process audio data
     */
    async processAudio(sessionId: string, audioBuffer: Buffer, format: 'ulaw' | 'pcm16' | 'browser', sourceType: 'twilio' | 'gemini' | 'browser'): Promise<void> {
        try {
            await this.audioPipeline.processAudio({
                sessionId,
                audioBuffer,
                format,
                sourceType
            });
        } catch (error) {
            sessionLogger.error(`Error processing audio for ${sessionId}:`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    
    /**
     * Get pipeline health status
     */
    getHealthStatus(): {
        audio: boolean;
        session: boolean;
        call: boolean;
        activeCalls: number;
    } {
        const activeCalls = this.callPipeline.getAllActiveCalls();
        
        return {
            audio: true, // Would check actual audio pipeline health
            session: true, // Would check session pipeline health
            call: true, // Would check call pipeline health
            activeCalls: activeCalls.size
        };
    }
    
    cleanup(): void {
        this.audioPipeline.cleanup();
        this.sessionPipeline.cleanup();
        this.callPipeline.cleanup();
        super.cleanup();
    }
}