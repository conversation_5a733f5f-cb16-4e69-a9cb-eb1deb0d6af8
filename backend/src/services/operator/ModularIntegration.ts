/**
 * Modular Integration Bridge
 * 
 * Connects the Twilio-Gemini demo with the modular provider architecture
 * Allows using both the demo's advanced features and the clean provider separation
 */

import { EventBus } from '@/modules/operator/event-system/event-bus';
import { GeminiLiveProvider } from '@/modules/live-inference/providers/GeminiLiveProvider';
import { OpenAIRealtimeProvider } from '@/modules/live-inference/providers/OpenAIRealtimeProvider';
import { TwilioProvider } from '@/modules/telephony/providers/TwilioProvider';
import { TelnyxProvider } from '@/modules/telephony/providers/TelnyxProvider';
import { AudioProcessor as ModularAudioProcessor } from '@/modules/live-inference/audio/AudioProcessor';
import { logger } from '@/utils/logger';

// Import demo components that we want to use
import { AudioProcessor as DemoAudioProcessor } from './audio/audio-processor.js';
import { SessionManager as DemoSessionManager } from './session/session-manager.js';
import { ContextManager } from './session/context-manager.js';
import { ConnectionHealthMonitor } from './session/health-monitor.js';
import { SessionLifecycleManager } from './session/lifecycle-manager.js';
import { initializeGeminiClient } from './gemini/client.js';

export interface ModularConfig {
  // LLM Provider Selection
  llmProvider: 'gemini' | 'openai';
  llmConfig: {
    apiKey: string;
    model?: string;
    voice?: string;
    systemPrompt?: string;
  };

  // Telephony Provider Selection
  telephonyProvider: 'twilio' | 'telnyx';
  telephonyConfig: {
    accountId: string;
    authToken: string;
    phoneNumber: string;
    webhookUrl: string;
    statusCallbackUrl?: string;
  };

  // Server Configuration
  serverConfig: {
    port: number;
    publicUrl: string;
  };
}

export class ModularIntegration {
  private eventBus: EventBus;
  private llmProvider: GeminiLiveProvider | OpenAIRealtimeProvider;
  private telephonyProvider: TwilioProvider | TelnyxProvider;
  private audioProcessor: ModularAudioProcessor;
  private demoAudioProcessor: DemoAudioProcessor;
  private sessionManager: DemoSessionManager;
  private contextManager: ContextManager;
  private healthMonitor: ConnectionHealthMonitor;
  private lifecycleManager: SessionLifecycleManager;

  constructor(private config: ModularConfig) {
    this.eventBus = EventBus.getInstance();
    this.audioProcessor = new ModularAudioProcessor();
    this.demoAudioProcessor = new DemoAudioProcessor();
    this.contextManager = new ContextManager();
    this.healthMonitor = new ConnectionHealthMonitor();

    // Initialize LLM Provider
    this.llmProvider = this.initializeLLMProvider();
    
    // Initialize Telephony Provider
    this.telephonyProvider = this.initializeTelephonyProvider();

    // Initialize demo components with modular backing
    this.initializeDemoComponents();
  }

  private initializeLLMProvider(): GeminiLiveProvider | OpenAIRealtimeProvider {
    const { llmProvider, llmConfig } = this.config;

    if (llmProvider === 'gemini') {
      return new GeminiLiveProvider({
        apiKey: llmConfig.apiKey,
        model: llmConfig.model || 'models/gemini-2.0-flash-exp',
        systemPrompt: llmConfig.systemPrompt,
        voice: llmConfig.voice || 'Helix'
      });
    } else {
      return new OpenAIRealtimeProvider({
        apiKey: llmConfig.apiKey,
        model: llmConfig.model || 'gpt-4o-mini-realtime-preview',
        systemPrompt: llmConfig.systemPrompt,
        voice: llmConfig.voice || 'shimmer'
      });
    }
  }

  private initializeTelephonyProvider(): TwilioProvider | TelnyxProvider {
    const { telephonyProvider, telephonyConfig } = this.config;

    if (telephonyProvider === 'twilio') {
      return new TwilioProvider({
        accountId: telephonyConfig.accountId,
        authToken: telephonyConfig.authToken,
        webhookUrl: telephonyConfig.webhookUrl,
        statusCallbackUrl: telephonyConfig.statusCallbackUrl
      });
    } else {
      return new TelnyxProvider({
        apiKey: telephonyConfig.authToken,
        webhookUrl: telephonyConfig.webhookUrl,
        statusCallbackUrl: telephonyConfig.statusCallbackUrl
      });
    }
  }

  private initializeDemoComponents(): void {
    // Initialize Gemini client for demo
    const geminiClient = initializeGeminiClient(this.config.llmConfig.apiKey);
    
    // Create active connections map for demo
    const activeConnections = new Map();

    // Initialize session manager with demo components
    this.sessionManager = new DemoSessionManager(
      this.contextManager,
      geminiClient,
      activeConnections
    );

    // Initialize lifecycle manager
    this.lifecycleManager = new SessionLifecycleManager(
      this.contextManager,
      this.healthMonitor,
      null // summary manager can be added later
    );
  }

  /**
   * Bridge method to handle incoming calls using both demo and modular components
   */
  async handleIncomingCall(callSid: string, from: string, to: string): Promise<void> {
    logger.info(`[ModularIntegration] Handling incoming call ${callSid}`);

    // Use modular telephony provider to accept the call
    const callSession = await this.telephonyProvider.acceptIncomingCall({
      callSid,
      from,
      to,
      streamUrl: `wss://${this.config.serverConfig.publicUrl}/media-stream`
    });

    // Create LLM session using modular provider
    const llmSession = await this.llmProvider.createSession({
      systemPrompt: this.config.llmConfig.systemPrompt,
      voice: this.config.llmConfig.voice,
      metadata: {
        callSid,
        from,
        to
      }
    });

    // Use demo session manager for advanced features
    await this.sessionManager.createSession(callSid, {
      sessionId: llmSession.id,
      twilioStreamSid: callSession.streamSid,
      phoneNumber: from,
      sessionType: 'inbound'
    });

    // Set up audio bridging between telephony and LLM
    this.bridgeAudioStreams(callSid, callSession, llmSession);
  }

  /**
   * Bridge audio between telephony and LLM providers
   */
  private bridgeAudioStreams(
    callSid: string,
    callSession: any,
    llmSession: any
  ): void {
    // Forward audio from telephony to LLM
    this.eventBus.on(`telephony:audio:${callSid}`, async (audioData: Buffer) => {
      // Convert using demo audio processor (has μ-law conversion)
      const pcmAudio = this.demoAudioProcessor.convertUlawToPCM(audioData);
      
      // Send to LLM provider
      await this.llmProvider.sendAudio(llmSession.id, pcmAudio);
    });

    // Forward audio from LLM to telephony
    this.eventBus.on(`llm:audio:${llmSession.id}`, async (audioData: Buffer) => {
      // Convert PCM to μ-law for telephony
      const ulawAudio = this.demoAudioProcessor.convertPCMToUlaw(audioData);
      
      // Send to telephony provider
      await this.telephonyProvider.sendAudio(callSid, ulawAudio);
    });

    // Handle transcriptions
    this.eventBus.on(`llm:transcription:${llmSession.id}`, (transcription: any) => {
      logger.info(`[ModularIntegration] Transcription: ${transcription.text}`);
      this.contextManager.addToContext(callSid, {
        role: transcription.role,
        content: transcription.text,
        timestamp: Date.now()
      });
    });
  }

  /**
   * Start the integrated server
   */
  async start(): Promise<void> {
    logger.info('[ModularIntegration] Starting integrated operator service...');
    
    // The demo server can be started separately or we can create a new server here
    // that combines both approaches
    
    logger.info('[ModularIntegration] Service started successfully');
  }

  /**
   * Stop the integrated server
   */
  async stop(): Promise<void> {
    logger.info('[ModularIntegration] Stopping integrated operator service...');
    
    // Cleanup all resources
    await this.llmProvider.cleanup();
    await this.telephonyProvider.disconnect();
    
    logger.info('[ModularIntegration] Service stopped successfully');
  }
}

// Factory function to create integration from environment variables
export function createModularIntegration(): ModularIntegration {
  const config: ModularConfig = {
    llmProvider: process.env.LLM_PROVIDER as 'gemini' | 'openai' || 'gemini',
    llmConfig: {
      apiKey: process.env.GEMINI_API_KEY || process.env.OPENAI_API_KEY || '',
      model: process.env.GEMINI_DEFAULT_MODEL || process.env.OPENAI_MODEL,
      voice: process.env.GEMINI_DEFAULT_VOICE || process.env.OPENAI_VOICE,
      systemPrompt: process.env.SYSTEM_INSTRUCTION
    },
    telephonyProvider: process.env.TELEPHONY_PROVIDER as 'twilio' | 'telnyx' || 'twilio',
    telephonyConfig: {
      accountId: process.env.TWILIO_ACCOUNT_SID || '',
      authToken: process.env.TWILIO_AUTH_TOKEN || process.env.TELNYX_API_KEY || '',
      phoneNumber: process.env.TWILIO_PHONE_NUMBER || '',
      webhookUrl: process.env.PUBLIC_URL || '',
      statusCallbackUrl: process.env.TWILIO_STATUS_CALLBACK_URL
    },
    serverConfig: {
      port: parseInt(process.env.OPERATOR_DEMO_PORT || '3101'),
      publicUrl: process.env.PUBLIC_URL || 'http://localhost:3101'
    }
  };

  return new ModularIntegration(config);
}