/**
 * CC-V1 Integration Module
 * 
 * Connects the operator demo with CC-V1's existing systems:
 * - tRPC routes
 * - Prisma database
 * - Authentication
 * - Event system
 */

import { prisma } from '@/lib/prisma';
import { EventBus } from '@/modules/operator/event-system/event-bus';
import { logger } from '@/utils/logger';
import { createModularIntegration, ModularIntegration } from './ModularIntegration.js';

export class CC1Integration {
  private modularIntegration: ModularIntegration;
  private eventBus: EventBus;

  constructor() {
    this.eventBus = EventBus.getInstance();
    this.modularIntegration = createModularIntegration();
    this.setupEventHandlers();
  }

  /**
   * Setup event handlers to sync with CC-V1 database
   */
  private setupEventHandlers(): void {
    // Log call start
    this.eventBus.on('call:started', async (data: any) => {
      try {
        await prisma.call.create({
          data: {
            external_id: data.callSid,
            phone_number: data.from,
            direction: data.direction || 'inbound',
            status: 'in-progress',
            started_at: new Date(),
            metadata: data
          }
        });
        logger.info(`[CC1Integration] Call ${data.callSid} logged to database`);
      } catch (error) {
        logger.error('[CC1Integration] Failed to log call start:', error);
      }
    });

    // Log call end
    this.eventBus.on('call:ended', async (data: any) => {
      try {
        await prisma.call.update({
          where: { external_id: data.callSid },
          data: {
            status: 'completed',
            ended_at: new Date(),
            duration: data.duration,
            recording_url: data.recordingUrl,
            metadata: data
          }
        });
        logger.info(`[CC1Integration] Call ${data.callSid} marked as completed`);
      } catch (error) {
        logger.error('[CC1Integration] Failed to update call end:', error);
      }
    });

    // Log transcriptions
    this.eventBus.on('transcription:received', async (data: any) => {
      try {
        const call = await prisma.call.findUnique({
          where: { external_id: data.callSid }
        });

        if (call) {
          await prisma.transcript.create({
            data: {
              call_id: call.id,
              speaker: data.role === 'user' ? 'customer' : 'agent',
              text: data.text,
              timestamp: new Date(data.timestamp),
              confidence: data.confidence || 1.0
            }
          });
        }
      } catch (error) {
        logger.error('[CC1Integration] Failed to save transcription:', error);
      }
    });
  }

  /**
   * Create operator session linked to CC-V1 user
   */
  async createOperatorSession(userId: string, config: any): Promise<any> {
    try {
      // Get user from database
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: { operator_settings: true }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Create session record
      const session = await prisma.operator_session.create({
        data: {
          user_id: userId,
          status: 'active',
          started_at: new Date(),
          config: config
        }
      });

      logger.info(`[CC1Integration] Created operator session ${session.id} for user ${userId}`);
      
      return {
        sessionId: session.id,
        userId: userId,
        config: {
          ...config,
          operatorName: user.name,
          operatorSettings: user.operator_settings
        }
      };
    } catch (error) {
      logger.error('[CC1Integration] Failed to create operator session:', error);
      throw error;
    }
  }

  /**
   * Handle incoming webhook from Twilio/Telnyx
   */
  async handleIncomingWebhook(provider: string, data: any): Promise<any> {
    logger.info(`[CC1Integration] Received ${provider} webhook:`, data);

    if (provider === 'twilio' && data.CallSid) {
      // Handle Twilio webhook
      await this.modularIntegration.handleIncomingCall(
        data.CallSid,
        data.From,
        data.To
      );

      // Return TwiML response
      return {
        statusCode: 200,
        headers: { 'Content-Type': 'text/xml' },
        body: `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Play>${process.env.GREETING_AUDIO_URL || 'https://cc-api.verduona.com/static/intro.mp3'}</Play>
  <Stream url="wss://${process.env.PUBLIC_URL}/media-stream">
    <Parameter name="callSid" value="${data.CallSid}" />
  </Stream>
</Response>`
      };
    }

    // Handle other providers...
    return { statusCode: 200, body: 'OK' };
  }

  /**
   * Get operator statistics for dashboard
   */
  async getOperatorStats(userId: string): Promise<any> {
    try {
      const stats = await prisma.call.aggregate({
        where: {
          operator_sessions: {
            some: { user_id: userId }
          }
        },
        _count: { id: true },
        _avg: { duration: true },
        _sum: { duration: true }
      });

      const recentCalls = await prisma.call.findMany({
        where: {
          operator_sessions: {
            some: { user_id: userId }
          }
        },
        orderBy: { started_at: 'desc' },
        take: 10,
        include: {
          transcripts: {
            orderBy: { timestamp: 'asc' }
          }
        }
      });

      return {
        totalCalls: stats._count.id,
        avgDuration: Math.round(stats._avg.duration || 0),
        totalDuration: stats._sum.duration || 0,
        recentCalls: recentCalls.map(call => ({
          id: call.id,
          phoneNumber: call.phone_number,
          direction: call.direction,
          duration: call.duration,
          startedAt: call.started_at,
          status: call.status,
          transcriptCount: call.transcripts.length
        }))
      };
    } catch (error) {
      logger.error('[CC1Integration] Failed to get operator stats:', error);
      throw error;
    }
  }

  /**
   * Start the integration
   */
  async start(): Promise<void> {
    await this.modularIntegration.start();
    logger.info('[CC1Integration] CC-V1 integration started');
  }

  /**
   * Stop the integration
   */
  async stop(): Promise<void> {
    await this.modularIntegration.stop();
    logger.info('[CC1Integration] CC-V1 integration stopped');
  }
}

// Singleton instance
let instance: CC1Integration | null = null;

export function getCC1Integration(): CC1Integration {
  if (!instance) {
    instance = new CC1Integration();
  }
  return instance;
}