// Simple authentication middleware for Supabase integration
import { authLogger } from '@/utils/logger';
import { timingSafeEqual } from 'crypto';
import { FastifyRequest, FastifyReply } from 'fastify';

import type { AuthRequest } from './types/shared-types.js';

export async function validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(request: AuthRequest, reply: FastifyReply): Promise<void> {
  // Skip auth for WebSocket connections
  if (request.headers && request.headers.upgrade === 'websocket') {
    return;
  }

  // Skip auth for CORS preflight OPTIONS requests
  if (request.method === 'OPTIONS') {
    return;
  }

  // Only skip auth for essential Twilio webhooks, health checks, and frontend API endpoints
  const publicPaths = [
    '/incoming-call',    // Twilio webhook (verified by Twi<PERSON> signature)
    '/call-status',      // Twilio webhook (verified by Twilio signature)
    '/recording-status', // Twilio webhook (verified by <PERSON><PERSON><PERSON> signature)
    '/health',           // Health check endpoint
    '/available-voices', // Frontend needs access to voice configuration
    '/available-models', // Frontend needs access to model configuration
    '/audio-settings',   // Frontend needs access to audio settings
    '/api/voice-config', // Frontend needs access to voice configuration
    '/api/campaign-scripts', // Frontend needs access to campaign script list
    '/api/outbound-scripts', // Frontend needs access to outbound scripts
    '/api/incoming-scripts', // Frontend needs access to incoming scripts
    '/api/validate-token',   // Frontend needs access to token validation
    '/get-campaign-script', // Frontend needs access to campaign scripts
    '/ready',            // Kubernetes readiness check
    '/live'              // Kubernetes liveness check
  ];

  // WebSocket paths that need special handling
  const websocketPaths = [
    '/media-stream'      // Twilio WebSocket (verified by Twilio)
  ];

  if (publicPaths.some(path => request.url.startsWith(path))) {
    return; // Continue without validation for essential webhooks and public endpoints
  }

  if (websocketPaths.some(path => request.url.startsWith(path))) {
    return; // WebSocket auth handled separately
  }
  
  const authHeader = request.headers?.authorization;
  
  if (!authHeader) {
    // Always enforce authentication in production for non-public paths
    const FORCE_AUTH = process.env.NODE_ENV === 'production' ? true :
      (process.env.FORCE_AUTH === 'true');
    
    if (FORCE_AUTH) {
      authLogger.error('No authorization header for protected endpoint', {
        url: request.url,
        method: request.method,
        ip: request.ip
      });
      await reply.code(401).send({ error: 'Authorization required' });
      return;
    }
    // Allow in development and test environments with warning
    if (process.env.NODE_ENV === 'test') {
      authLogger.debug('No authorization header - allowing for test environment');
    } else {
      authLogger.warn('No authorization header - allowing for development only');
    }
    return;
  }

  try {
    // Extract token from "Bearer <token>" format
    const token = authHeader.replace('Bearer ', '');
    
    if (!token || token === 'undefined' || token === 'null') {
      throw new Error('Invalid token format');
    }

    // Enhanced token validation for production security
    const FORCE_AUTH = process.env.NODE_ENV === 'production' ? true : 
      (process.env.FORCE_AUTH === 'true');
    
    if (FORCE_AUTH) {
      // Validate token format (basic check)
      if (token.length < 32) {
        throw new Error('Token too short');
      }

      // Determine the appropriate API key based on the endpoint - STRICT separation per service
      let validApiKey: string | undefined;
      let serviceType: string;
      
      if (request.url.startsWith('/api/gemini') || request.url.includes('gemini')) {
        validApiKey = process.env.GEMINI_API_KEY;
        serviceType = 'GEMINI';
      } else if (request.url.startsWith('/api/twilio') || request.url.includes('twilio')) {
        validApiKey = process.env.TWILIO_AUTH_TOKEN;
        serviceType = 'TWILIO';
      } else if (request.url.startsWith('/api/supabase') || request.url.includes('supabase')) {
        validApiKey = process.env.SUPABASE_SERVICE_KEY;
        serviceType = 'SUPABASE';
      } else if (request.url.startsWith('/incoming') || request.url.startsWith('/outbound-scripts')) {
        // Management interfaces - require dedicated admin API key
        validApiKey = process.env.ADMIN_API_KEY || process.env.API_KEY;
        serviceType = 'ADMIN';
      } else {
        // General API access - require dedicated API key, no fallback to service keys
        validApiKey = process.env.API_KEY;
        serviceType = 'GENERAL';
      }

      if (!validApiKey) {
        authLogger.error(`❌ No valid API key configured for ${serviceType} service endpoint: ${request.url}`);
        throw new Error(`No valid API key configured for ${serviceType} service`);
      }

      // Use timing-safe comparison first (fixes timing attack vulnerability)
      const maxLength = Math.max(token.length, validApiKey.length);
      const paddedToken = token.padEnd(maxLength, '\0');
      const paddedValidKey = validApiKey.padEnd(maxLength, '\0');
      
      if (!timingSafeEqual(Buffer.from(paddedToken), Buffer.from(paddedValidKey))) {
        authLogger.warn(`⚠️ Invalid API key attempt for ${serviceType} service from ${request.ip}`);
        throw new Error(`Invalid API key for ${serviceType} service`);
      }

      // Additional length check after timing-safe comparison
      if (token.length !== validApiKey.length) {
        authLogger.warn(`⚠️ Invalid token length for ${serviceType} service from ${request.ip}`);
        throw new Error(`Invalid API key format for ${serviceType} service`);
      }

      authLogger.info(`✅ Authenticated ${serviceType} service access from ${request.ip}`);
    }
    
    authLogger.info('Auth validation passed', {
      tokenLength: token.length,
      url: request.url
    });
    
    // Attach user info to request for downstream use
    request.auth = {
      token,
      authenticated: true
    };
    
  } catch (error) {
    authLogger.error('Auth validation failed', error instanceof Error ? error : new Error(String(error)));
    await reply.code(401).send({ error: 'Unauthorized' });
    return;
  }
}

// Middleware to require auth for specific routes
export function requireAuth(
  request: AuthRequest,
  reply: FastifyReply,
  done: (err?: Error) => void
): void {
  validateSupabaseAuth(request, reply)
    .then(() => done())
    .catch(() => {
      if (!reply.sent) {
        reply.code(401).send({ error: 'Authentication required' });
      }
      done();
    });
}