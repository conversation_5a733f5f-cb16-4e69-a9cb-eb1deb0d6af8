import { EventDrivenComponent } from '@/modules/operator/event-system/event-patterns';
import { AudioMetrics } from './audio-analyzer.js';
import { audioLogger } from '@/utils/logger';
import { AudioQualityMetrics } from '@/modules/operator/event-system/event-types';

export interface AudioQualitySummary {
    totalSamples: number;
    averageQuality: number;
    metrics: {
        avgRms: number;
        avgPeak: number;
        avgSnr: number;
        silencePercentage: number;
        clippingPercentage: number;
    };
    warnings: string[];
}

/**
 * Audio quality monitor - tracks quality metrics over time
 */
export class AudioQualityMonitor extends EventDrivenComponent {
    private metricsHistory: Map<string, AudioMetrics[]>;
    private globalMetrics: {
        totalSamples: number;
        sumRms: number;
        sumPeak: number;
        sumSnr: number;
        sumSilence: number;
        sumClipping: number;
    };
    
    constructor() {
        super('AudioQualityMonitor');
        
        this.metricsHistory = new Map();
        this.globalMetrics = {
            totalSamples: 0,
            sumRms: 0,
            sumPeak: 0,
            sumSnr: 0,
            sumSilence: 0,
            sumClipping: 0
        };
        
        // Subscribe to quality measurements
        this.subscribe('audio:quality:measured', this.handleQualityMeasured.bind(this));
    }
    
    private handleQualityMeasured(payload: { sessionId: string; metrics: AudioQualityMetrics }): void {
        const { sessionId, metrics } = payload;
        
        // Convert AudioQualityMetrics to AudioMetrics for internal use
        const audioMetrics: AudioMetrics = {
            rms: 0, // Not available in AudioQualityMetrics
            peak: 0, // Not available in AudioQualityMetrics
            averageVolume: metrics.volume,
            dynamicRange: 0, // Not available in AudioQualityMetrics
            silencePercentage: 0, // Not available in AudioQualityMetrics
            clippingPercentage: 0, // Not available in AudioQualityMetrics
            noiseFloor: metrics.noiseLevel,
            snr: 0 // Not available in AudioQualityMetrics
        };
        
        // Store metrics history per session
        if (!this.metricsHistory.has(sessionId)) {
            this.metricsHistory.set(sessionId, []);
        }
        
        const history = this.metricsHistory.get(sessionId)!;
        history.push(audioMetrics);
        
        // Keep only last 100 measurements per session
        if (history.length > 100) {
            history.shift();
        }
        
        // Update global metrics
        this.updateGlobalMetrics(audioMetrics);
        
        // Check for quality issues
        this.checkQualityIssues(sessionId, audioMetrics);
    }
    
    private updateGlobalMetrics(metrics: AudioMetrics): void {
        this.globalMetrics.totalSamples++;
        this.globalMetrics.sumRms += metrics.rms;
        this.globalMetrics.sumPeak += metrics.peak;
        this.globalMetrics.sumSnr += metrics.snr;
        this.globalMetrics.sumSilence += metrics.silencePercentage;
        this.globalMetrics.sumClipping += metrics.clippingPercentage;
    }
    
    private checkQualityIssues(sessionId: string, metrics: AudioMetrics): void {
        const warnings: string[] = [];
        
        if (metrics.silencePercentage > 90) {
            warnings.push('Excessive silence detected');
        }
        
        if (metrics.clippingPercentage > 5) {
            warnings.push('Audio clipping detected');
        }
        
        if (metrics.snr < 20) {
            warnings.push('Poor signal-to-noise ratio');
        }
        
        if (warnings.length > 0) {
            this.emit('audio:quality:warning', {
                sessionId,
                warnings,
                metrics
            });
            
            audioLogger.warn(`[${sessionId}] Audio quality issues:`, warnings);
        }
    }
    
    getSessionSummary(sessionId: string): AudioQualitySummary | null {
        const history = this.metricsHistory.get(sessionId);
        if (!history || history.length === 0) {
            return null;
        }
        
        const summary: AudioQualitySummary = {
            totalSamples: history.length,
            averageQuality: 0,
            metrics: {
                avgRms: 0,
                avgPeak: 0,
                avgSnr: 0,
                silencePercentage: 0,
                clippingPercentage: 0
            },
            warnings: []
        };
        
        // Calculate averages
        for (const metrics of history) {
            summary.metrics.avgRms += metrics.rms;
            summary.metrics.avgPeak += metrics.peak;
            summary.metrics.avgSnr += metrics.snr;
            summary.metrics.silencePercentage += metrics.silencePercentage;
            summary.metrics.clippingPercentage += metrics.clippingPercentage;
        }
        
        // Normalize
        summary.metrics.avgRms /= history.length;
        summary.metrics.avgPeak /= history.length;
        summary.metrics.avgSnr /= history.length;
        summary.metrics.silencePercentage /= history.length;
        summary.metrics.clippingPercentage /= history.length;
        
        // Calculate overall quality
        summary.averageQuality = this.calculateOverallQuality(summary.metrics);
        
        // Add warnings
        if (summary.metrics.silencePercentage > 50) {
            summary.warnings.push('High average silence');
        }
        
        if (summary.metrics.clippingPercentage > 1) {
            summary.warnings.push('Frequent clipping');
        }
        
        return summary;
    }
    
    private calculateOverallQuality(metrics: {
        avgRms: number;
        avgPeak: number;
        avgSnr: number;
        silencePercentage: number;
        clippingPercentage: number;
    }): number {
        let quality = 1.0;
        
        // Factor in various metrics
        if (metrics.silencePercentage > 50) quality *= 0.8;
        if (metrics.clippingPercentage > 1) quality *= 0.9;
        if (metrics.avgSnr < 30) quality *= 0.85;
        
        return Math.max(0, Math.min(1, quality));
    }
    
    reset(): void {
        this.metricsHistory.clear();
        this.globalMetrics = {
            totalSamples: 0,
            sumRms: 0,
            sumPeak: 0,
            sumSnr: 0,
            sumSilence: 0,
            sumClipping: 0
        };
    }
}