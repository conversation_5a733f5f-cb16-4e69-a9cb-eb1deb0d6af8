import { EventDrivenComponent } from '@/modules/operator/event-system/event-patterns';
import { AudioValidator, AudioValidationResult } from './audio-validator.js';
import { AudioAnalyzer, AudioMetrics } from './audio-analyzer.js';
import { AudioQualityMonitor, AudioQualitySummary } from './quality-monitor.js';
import { AudioFormat } from '../audio-types.js';
import { audioLogger } from '@/utils/logger';

export interface AudioQualityMetrics {
    volume: number;
    silenceRatio: number;
    clippingRatio: number;
    noiseLevel: number;
    isValid: boolean;
    warnings: string[];
}

/**
 * Audio quality orchestrator - coordinates validation, analysis, and monitoring
 */
export class AudioQualityOrchestrator extends EventDrivenComponent {
    private validator: AudioValidator;
    private analyzer: AudioAnalyzer;
    private monitor: AudioQualityMonitor;
    private validationStats: Map<string, number>;
    
    constructor() {
        super('AudioQualityOrchestrator');
        
        this.validator = new AudioValidator();
        this.analyzer = new AudioAnalyzer();
        this.monitor = new AudioQualityMonitor();
        this.validationStats = new Map();
        
        // Subscribe to audio events
        this.subscribe('audio:received', this.handleAudioReceived.bind(this));
    }
    
    private async handleAudioReceived(payload: {
        sessionId: string;
        buffer: Buffer;
        format: string;
    }): Promise<void> {
        const { sessionId, buffer, format } = payload;
        
        try {
            // Step 1: Validate
            const validation = await this.validateAndAnalyze(buffer, format, sessionId);
            
            if (!validation.isValid) {
                audioLogger.warn(`[${sessionId}] Audio validation failed:`, { error: validation.error });
                return;
            }
            
            // Step 2: Analyze if valid
            if (validation.metadata) {
                const metrics = this.analyzer.analyzeBuffer(buffer, format);
                const qualityScore = this.analyzer.calculateQualityScore(metrics);
                
                // Update metadata with quality score
                validation.metadata.qualityScore = qualityScore;
                
                // Emit quality metrics
                this.emit('audio:quality:measured', {
                    sessionId,
                    metrics: {
                        ...metrics,
                        volume: metrics.averageVolume,
                        clarity: qualityScore,
                        noiseLevel: metrics.noiseFloor,
                        sampleRate: format === 'ulaw' ? 8000 : 24000,
                        bitDepth: 16
                    }
                });
            }
            
        } catch (error) {
            audioLogger.error(`[${sessionId}] Error in quality orchestration:`, error as Error);
            this.emit('audio:error', {
                sessionId,
                error: error as Error,
                context: 'quality_orchestration'
            });
        }
    }
    
    async validateAndAnalyze(
        buffer: unknown,
        format: string,
        sessionId: string
    ): Promise<AudioValidationResult> {
        // Validate buffer
        const validation = this.validator.validateBuffer(buffer, format, sessionId);
        
        // Track validation stats
        if (validation.isValid) {
            this.incrementStat('valid_buffers');
        } else {
            this.incrementStat('invalid_buffers');
            this.incrementStat(`error_${validation.error?.split(':')[0] || 'unknown'}`);
        }
        
        return validation;
    }
    
    analyzeAudioQuality(
        buffer: Buffer,
        format: string,
        sessionId: string
    ): AudioMetrics | null {
        try {
            const metrics = this.analyzer.analyzeBuffer(buffer, format);
            
            // Quality score calculation
            const qualityScore = this.analyzer.calculateQualityScore(metrics);
            
            audioLogger.debug(`[${sessionId}] Audio quality score: ${qualityScore.toFixed(2)}`);
            
            return metrics;
            
        } catch (error) {
            audioLogger.error(`[${sessionId}] Error analyzing audio quality:`, error as Error);
            return null;
        }
    }
    
    getSessionQualitySummary(sessionId: string): AudioQualitySummary | null {
        return this.monitor.getSessionSummary(sessionId);
    }
    
    getValidationStats(): Record<string, number> {
        const stats: Record<string, number> = {};
        this.validationStats.forEach((value, key) => {
            stats[key] = value;
        });
        return stats;
    }
    
    private incrementStat(stat: string): void {
        this.validationStats.set(stat, (this.validationStats.get(stat) || 0) + 1);
    }
    
    resetStats(): void {
        this.validationStats.clear();
        this.monitor.reset();
    }
    
    /**
     * Analyze audio quality with the expected interface
     */
    async analyzeQuality(
        audioData: Buffer | string,
        format: AudioFormat,
        sampleRate: number
    ): Promise<AudioQualityMetrics> {
        try {
            // Convert to buffer if needed
            const buffer = typeof audioData === 'string' 
                ? Buffer.from(audioData, 'base64')
                : audioData;
            
            // Validate
            const validation = await this.validateAndAnalyze(buffer, format.toString(), 'system');
            
            // Analyze
            const metrics = this.analyzeAudioQuality(buffer, format.toString(), 'system');
            
            if (!metrics) {
                throw new Error('Failed to analyze audio quality');
            }
            
            return {
                volume: metrics.averageVolume,
                silenceRatio: metrics.silencePercentage / 100,
                clippingRatio: metrics.clippingPercentage / 100,
                noiseLevel: metrics.noiseFloor,
                isValid: validation.isValid,
                warnings: validation.error ? [validation.error] : []
            };
        } catch (error) {
            audioLogger.error('Error in analyzeQuality:', error as Error);
            throw error;
        }
    }
    
    /**
     * Update session metrics
     */
    updateSessionMetrics(sessionId: string, metrics: AudioQualityMetrics): void {
        // Monitor doesn't have updateMetrics method - would need to be implemented
        // For now, just emit an event with the metrics
        this.emit('audio:quality:measured', {
            sessionId,
            metrics: {
                volume: metrics.volume,
                clarity: metrics.isValid ? 1.0 : 0.0,
                noiseLevel: metrics.noiseLevel,
                sampleRate: 16000, // Default
                bitDepth: 16 // Default
            }
        });
    }
    
    /**
     * Get session report
     */
    getSessionReport(sessionId: string): any {
        const summary = this.monitor.getSessionSummary(sessionId);
        if (!summary) return null;
        
        return {
            sessionId,
            ...summary,
            validationStats: this.getValidationStats()
        };
    }
    
    /**
     * Clear session data
     */
    clearSessionData(sessionId: string): void {
        this.monitor.reset();
        this.emit('session:quality:cleared', { sessionId });
    }
    
    cleanup(): void {
        this.validator.cleanup();
        this.analyzer.cleanup();
        this.monitor.cleanup();
        super.cleanup();
    }
}