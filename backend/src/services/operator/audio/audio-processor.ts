import { getAudioConversion } from './audio-conversion.js';
import { AudioEnhancer } from './audio-enhancer.js';
import { getAudioQuality } from './audio-quality.js';
import { AudioSettings, MockAudioBuffer, AudioFormat } from './audio-types.js';
import fs from 'fs';
import path from 'path';

export class AudioProcessor {
    static audioEnhancer = new AudioEnhancer();
    static audioQualityMonitor = getAudioQuality();

    private converter = getAudioConversion();
    private audioQuality = getAudioQuality();
    private audioSettings: AudioSettings;

    constructor() {
        this.audioSettings = {
            enableDeEssing: false,
            enableNoiseReduction: false,
            enableCompression: false,
            enableAGC: false,
            compressionRatio: 2.0,
            noiseThreshold: 0.01,
            agcTargetLevel: 0.7
        };
    }

    convertUlawToPCM(audioBuffer: Buffer, skipEnhancement = false): Buffer {
        // Direct μ-law to PCM conversion
        const pcm = Buffer.alloc(audioBuffer.length * 2);
        for (let i = 0; i < audioBuffer.length; i++) {
            const sample = this.ulawToLinear(audioBuffer[i]);
            pcm.writeInt16LE(sample, i * 2);
        }
        // Skip enhancement in production for lower latency
        if (skipEnhancement || process.env.NODE_ENV === 'production') {
            return pcm;
        }
        const float = this.pcmToFloat32Array(pcm);
        const enhanced = AudioProcessor.audioEnhancer.enhance(float, { noiseReduction: true });
        const out = Buffer.alloc(enhanced.length * 2);
        for (let i = 0; i < enhanced.length; i++) {
            const s = Math.max(-1, Math.min(1, enhanced[i]));
            out.writeInt16LE(Math.round(s * 32767), i * 2);
        }
        return out;
    }

    setSettings(settings: Partial<AudioSettings>): void {
        this.audioSettings = {
            ...this.audioSettings,
            ...settings
        };
    }

    pcmToFloat32Array(pcmBuffer: Buffer): Float32Array {
        // Convert PCM to float32 array
        const float32 = new Float32Array(pcmBuffer.length / 2);
        for (let i = 0; i < float32.length; i++) {
            const sample = pcmBuffer.readInt16LE(i * 2);
            float32[i] = sample / 32768.0;
        }
        return float32;
    }

    upsample8kTo16k(data: Float32Array): Float32Array {
        // Simple 2x upsampling
        const upsampled = new Float32Array(data.length * 2);
        for (let i = 0; i < data.length; i++) {
            upsampled[i * 2] = data[i];
            upsampled[i * 2 + 1] = i < data.length - 1 ? (data[i] + data[i + 1]) / 2 : data[i];
        }
        return upsampled;
    }

    downsample24kTo8k(data: Float32Array): Float32Array {
        // Simple 3x downsampling
        const downsampled = new Float32Array(Math.floor(data.length / 3));
        for (let i = 0; i < downsampled.length; i++) {
            downsampled[i] = data[i * 3];
        }
        return downsampled;
    }

    createGeminiAudioBlob(data: Float32Array): { data: string; mimeType: string } {
        // Convert float32 to PCM16 buffer
        const pcmBuffer = Buffer.alloc(data.length * 2);
        for (let i = 0; i < data.length; i++) {
            const sample = Math.max(-1, Math.min(1, data[i]));
            const int16 = Math.round(sample * 32767);
            pcmBuffer.writeInt16LE(int16, i * 2);
        }
        return {
            data: pcmBuffer.toString('base64'),
            mimeType: 'audio/L16;rate=16000;channels=1'
        };
    }

    convertPCMToUlaw(audio: string | Buffer): string | Buffer {
        if (typeof audio === 'string') {
            const buffer = Buffer.from(audio, 'base64');
            const ulawBuffer = this.pcmToUlaw(buffer);
            return ulawBuffer.toString('base64');
        }
        return this.pcmToUlaw(audio);
    }

    pcmToUlaw(pcm: Buffer): Buffer {
        const ulaw = Buffer.alloc(pcm.length / 2);
        for (let i = 0; i < ulaw.length; i++) {
            const sample = pcm.readInt16LE(i * 2);
            ulaw[i] = this.linearToUlaw(sample);
        }
        return ulaw;
    }

    ulawToLinear(ulawByte: number): number {
        const BIAS = 0x84;
        let sign = 0;
        let exponent = 0;
        let mantissa = 0;
        let sample = 0;
        
        ulawByte = ~ulawByte;
        sign = (ulawByte & 0x80);
        exponent = (ulawByte >> 4) & 0x07;
        mantissa = ulawByte & 0x0F;
        sample = mantissa << (exponent + 3);
        sample = sample + BIAS;
        sample = (sign !== 0) ? -sample : sample;
        
        return sample;
    }

    linearToUlaw(sample: number): number {
        const BIAS = 0x84;
        const CLIP = 32635;
        const encodeTable = [
            0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,
            4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,
            5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
            5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
            6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,
            6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,
            6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,
            6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,
            7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
            7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
            7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
            7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
            7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
            7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
            7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
            7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7
        ];
        
        let sign = 0;
        let exponent = 0;
        let mantissa = 0;
        let ulawByte = 0;
        
        sign = (sample >> 8) & 0x80;
        if (sign !== 0) sample = -sample;
        if (sample > CLIP) sample = CLIP;
        sample = sample + BIAS;
        exponent = encodeTable[(sample >> 7) & 0xFF];
        mantissa = (sample >> (exponent + 3)) & 0x0F;
        ulawByte = ~(sign | (exponent << 4) | mantissa);
        
        return ulawByte & 0xFF;
    }

    static async convertWebmToPCM16(webmData: Buffer, targetSampleRate = 16000): Promise<Buffer> {
        const converter = getAudioConversion();
        // Convert webm to PCM16 - webm is typically base64 encoded
        const result = await converter.convert(
            webmData,
            AudioFormat.WEBM,
            AudioFormat.PCM_16,
            { fromSampleRate: 48000, toSampleRate: targetSampleRate }
        );
        
        if (typeof result === 'string') {
            throw new Error('Expected Buffer but got string from audio conversion');
        }
        
        return result;
    }

    static decodeAudioData(webmData: Buffer): MockAudioBuffer {
        // Simple mock implementation
        const sampleRate = 48000;
        const numberOfChannels = 1;
        const length = webmData.length / 2;
        const duration = length / sampleRate;
        
        return {
            sampleRate,
            numberOfChannels,
            length,
            duration,
            getChannelData: (channel: number) => {
                const data = new Float32Array(length);
                for (let i = 0; i < length; i++) {
                    const sample = webmData.readInt16LE(i * 2);
                    data[i] = sample / 32768.0;
                }
                return data;
            }
        };
    }

    static convertToPCM16(audioBuffer: MockAudioBuffer, targetSampleRate: number): Buffer {
        const channelData = audioBuffer.getChannelData(0);
        const pcmBuffer = Buffer.alloc(channelData.length * 2);
        
        for (let i = 0; i < channelData.length; i++) {
            const sample = Math.max(-1, Math.min(1, channelData[i]));
            const int16 = Math.round(sample * 32767);
            pcmBuffer.writeInt16LE(int16, i * 2);
        }
        
        return pcmBuffer;
    }

    async saveAudioDebug(samples: Float32Array, filename: string, sampleRate = 8000): Promise<void> {
        if (process.env.AUDIO_DEBUG !== 'true') {
            return;
        }
        
        try {
            const pcmBuffer = Buffer.alloc(samples.length * 2);
            for (let i = 0; i < samples.length; i++) {
                const s = Math.max(-1, Math.min(1, samples[i]));
                pcmBuffer.writeInt16LE(Math.round(s * 32767), i * 2);
            }
            const dir = path.join(process.cwd(), 'audio-debug');
            
            try {
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }
            } catch (mkdirError) {
                console.error('Failed to create audio debug directory:', mkdirError);
                return; // Don't crash if we can't create debug directory
            }
            
            const filepath = path.join(dir, `${filename}_${Date.now()}.pcm`);
            
            try {
                // Use async write to avoid blocking the event loop
                fs.writeFile(filepath, pcmBuffer, (writeError) => {
                    if (writeError) {
                        console.error('Failed to write audio debug file:', writeError);
                        // Don't crash if we can't write debug file
                    }
                });
            } catch (writeError) {
                console.error('Failed to write audio debug file:', writeError);
                // Don't crash if we can't write debug file
            }
        } catch (error) {
            console.error('Error in saveAudioDebug:', error);
            // Don't let debug functionality crash the application
        }
    }

    updateAudioSettings(newSettings: Partial<AudioSettings>): void {
        this.audioSettings = { ...this.audioSettings, ...newSettings };
    }

    getAudioSettings(): AudioSettings {
        return { ...this.audioSettings };
    }

    getValidationStats(): Record<string, number> {
        // Return empty stats for now - audioValidator not implemented
        return {};
    }

    resetValidationStats(): void {
        // No-op - audioValidator not implemented
    }

    validateAudioBuffer(buffer: Buffer, format: string = 'ulaw', sessionId?: string): any {
        // Return basic validation result - audioValidator not implemented
        return {
            isValid: true,
            format,
            size: buffer.length,
            sessionId
        };
    }

    getAudioHealthReport(): {
        validationStats: Record<string, number>;
        enhancerStats: any;
        qualityMetrics: any;
        lastUpdate: string;
    } {
        return {
            validationStats: this.getValidationStats(),
            enhancerStats: AudioProcessor.audioEnhancer.getProcessingStats(),
            qualityMetrics: {}, // getSummary method not available on AudioQuality
            lastUpdate: new Date().toISOString()
        };
    }
}

// Export a standalone function for backward compatibility
export async function convertWebmToPCM16(webmData: Buffer, targetSampleRate = 16000): Promise<Buffer> {
    return AudioProcessor.convertWebmToPCM16(webmData, targetSampleRate);
}
