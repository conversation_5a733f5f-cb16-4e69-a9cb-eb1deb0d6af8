import { eventBus } from '@/modules/operator/event-system/event-bus';
import { getTwilioWebSocket, safeSendWebSocket, getWebSocketState } from '../../utils/websocket-utils.js';
import { audioLogger } from '@/utils/logger';
import { ConnectionData } from '../../types/global.js';
import { AudioData } from './audio-buffer-manager.js';
import type { TwilioMediaMessage } from '../../types/websocket.js';

export class TwilioForwarder {
    private static instance: TwilioForwarder;
    private lastAudioSentTime = new Map<string, number>();

    private constructor() {
        this.setupEventListeners();
    }

    static getInstance(): TwilioForwarder {
        if (!TwilioForwarder.instance) {
            TwilioForwarder.instance = new TwilioForwarder();
        }
        return TwilioForwarder.instance;
    }

    private setupEventListeners(): void {
        eventBus.on('audio:forward:twilio', async (data) => {
            await this.forward(
                data.sessionId,
                data.audio,
                data.connectionData,
                data.audioProcessor
            );
        });

        eventBus.on('session:cleanup', (data) => {
            this.lastAudioSentTime.delete(data.sessionId);
        });
    }

    async forward(
        sessionId: string,
        audio: AudioData,
        connectionData: ConnectionData,
        audioProcessor: any
    ): Promise<boolean> {
        try {
            if (!audio || !audio.data) {
                audioLogger.warn('🚫 No audio data to forward to Twilio', {
                    hasAudio: !!audio,
                    hasAudioData: !!(audio?.data)
                }, sessionId);
                return false;
            }

            const twilioWs = getTwilioWebSocket(connectionData);
            const wsState = twilioWs ? getWebSocketState(twilioWs) : 'null';
            
            audioLogger.info('📞 Attempting Twilio audio forwarding', {
                hasTwilioWs: !!twilioWs,
                wsState: wsState,
                hasStreamSid: !!connectionData.streamSid,
                sequenceNumber: connectionData.sequenceNumber,
                audioDataLength: audio.data.length
            }, sessionId);

            if (!twilioWs) {
                audioLogger.error('❌ No Twilio WebSocket available for audio forwarding', {
                    wsConnections: {
                        twilioWs: !!connectionData.twilioWs,
                        generalWs: !!connectionData.ws,
                        hasAnyWs: !!(connectionData.twilioWs || connectionData.ws)
                    }
                }, sessionId);
                eventBus.emit('audio:forward:twilio:failed', {
                    sessionId,
                    reason: 'no_websocket'
                });
                return false;
            }

            if (wsState !== 'OPEN') {
                audioLogger.error('❌ Twilio WebSocket not in OPEN state', {
                    currentState: wsState,
                    readyState: twilioWs.readyState
                }, sessionId);
                eventBus.emit('audio:forward:twilio:failed', {
                    sessionId,
                    reason: 'websocket_not_open'
                });
                return false;
            }

            if (!connectionData.streamSid) {
                audioLogger.error('❌ No streamSid available for Twilio audio forwarding', {
                    hasStreamSid: !!connectionData.streamSid,
                    connectionKeys: Object.keys(connectionData)
                }, sessionId);
                eventBus.emit('audio:forward:twilio:failed', {
                    sessionId,
                    reason: 'no_stream_sid'
                });
                return false;
            }

            // Ensure proper sequencing
            if (connectionData.sequenceNumber === undefined || connectionData.sequenceNumber === null) {
                connectionData.sequenceNumber = 1;
                audioLogger.warn('⚠️ Sequence number was undefined, initializing to 1', {}, sessionId);
            }

            // Create media message with mimeType support
            const mediaMessage: TwilioMediaMessage = {
                event: 'media',
                streamSid: connectionData.streamSid,
                media: {
                    payload: audio.data,
                    chunk: String(connectionData.sequenceNumber),
                    timestamp: String(Date.now()),
                    ...(audio.mimeType && { mimeType: audio.mimeType })
                }
            };

            // Track current sequence number for diagnostics
            const currentSeqNum = connectionData.sequenceNumber;

            // Send to Twilio
            const success = safeSendWebSocket(twilioWs, mediaMessage, sessionId);
            
            if (success) {
                // Increment sequence number after successful send
                connectionData.sequenceNumber++;
                this.lastAudioSentTime.set(sessionId, Date.now());
                
                // Process audio data for monitoring
                if (audioProcessor?.processOutgoingAudioData) {
                    audioProcessor.processOutgoingAudioData(Buffer.from(audio.data, 'base64'));
                }

                audioLogger.info('✅ Audio forwarded to Twilio successfully', {
                    sequenceNumber: currentSeqNum,
                    nextSequenceNumber: connectionData.sequenceNumber,
                    audioLength: audio.data.length,
                    streamSid: connectionData.streamSid,
                    hasMimeType: !!audio.mimeType
                }, sessionId);

                eventBus.emit('audio:forward:twilio:success', {
                    sessionId,
                    sequenceNumber: currentSeqNum,
                    audioSize: audio.data.length,
                    timestamp: Date.now()
                });

                return true;
            } else {
                audioLogger.error('❌ Failed to send audio to Twilio WebSocket', {
                    sequenceNumber: connectionData.sequenceNumber,
                    streamSid: connectionData.streamSid
                }, sessionId);

                eventBus.emit('audio:forward:twilio:failed', {
                    sessionId,
                    reason: 'send_failed',
                    sequenceNumber: connectionData.sequenceNumber
                });

                return false;
            }
        } catch (error) {
            audioLogger.error('❌ Error forwarding audio to Twilio', {
                error: error as Error,
                stack: error instanceof Error ? error.stack : undefined
            }, sessionId);

            eventBus.emit('audio:forward:twilio:error', {
                sessionId,
                error: error instanceof Error ? error.message : 'Unknown error'
            });

            return false;
        }
    }

    getLastAudioSentTime(sessionId: string): number | undefined {
        return this.lastAudioSentTime.get(sessionId);
    }

    getTimeSinceLastAudio(sessionId: string): number | null {
        const lastTime = this.lastAudioSentTime.get(sessionId);
        return lastTime ? Date.now() - lastTime : null;
    }
}