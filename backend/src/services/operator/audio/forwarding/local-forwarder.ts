import { eventBus } from '@/modules/operator/event-system/event-bus';
import { getLocalWebSocket, safeSendWebSocket, getWebSocketState } from '../../utils/websocket-utils.js';
import { audioLogger } from '@/utils/logger';
import { ConnectionData } from '../../types/global.js';
import { AudioData } from './audio-buffer-manager.js';

interface AudioMessage {
    type: string;
    audio: string;
    mimeType: string;
    timestamp: number;
    sessionId: string;
}

export class LocalForwarder {
    private static instance: LocalForwarder;
    private lastAudioSentTime = new Map<string, number>();

    private constructor() {
        this.setupEventListeners();
    }

    static getInstance(): LocalForwarder {
        if (!LocalForwarder.instance) {
            LocalForwarder.instance = new LocalForwarder();
        }
        return LocalForwarder.instance;
    }

    private setupEventListeners(): void {
        eventBus.on('audio:forward:local', async (data) => {
            await this.forward(
                data.sessionId,
                data.audio,
                data.connectionData,
                data.audioProcessor
            );
        });

        eventBus.on('session:cleanup', (data) => {
            this.lastAudioSentTime.delete(data.sessionId);
        });
    }

    async forward(
        sessionId: string,
        audio: AudioData,
        connectionData: ConnectionData,
        audioProcessor: any
    ): Promise<boolean> {
        try {
            if (!audio || !audio.data) {
                audioLogger.warn('🚫 No audio data to forward to local', {
                    hasAudio: !!audio,
                    hasAudioData: !!(audio?.data)
                }, sessionId);
                return false;
            }

            const localWs = getLocalWebSocket(connectionData);
            const wsState = localWs ? getWebSocketState(localWs) : 'null';
            
            audioLogger.info('💻 Attempting local audio forwarding', {
                hasLocalWs: !!localWs,
                wsState: wsState,
                audioDataLength: audio.data.length
            }, sessionId);

            if (!localWs) {
                audioLogger.error('❌ No local WebSocket available for audio forwarding', {
                    wsConnections: {
                        localWs: !!connectionData.localWs,
                        generalWs: !!connectionData.ws,
                        hasAnyWs: !!(connectionData.localWs || connectionData.ws)
                    }
                }, sessionId);
                eventBus.emit('audio:forward:local:failed', {
                    sessionId,
                    reason: 'no_websocket'
                });
                return false;
            }

            if (wsState !== 'OPEN') {
                audioLogger.error('❌ Local WebSocket not in OPEN state', {
                    currentState: wsState,
                    readyState: localWs.readyState
                }, sessionId);
                eventBus.emit('audio:forward:local:failed', {
                    sessionId,
                    reason: 'websocket_not_open'
                });
                return false;
            }

            const audioMessage: AudioMessage = {
                type: 'audio',
                audio: audio.data,
                mimeType: audio.mimeType || 'audio/L16;rate=16000;channels=1',
                timestamp: Date.now(),
                sessionId
            };

            const success = safeSendWebSocket(localWs, audioMessage, sessionId);
            
            if (success) {
                this.lastAudioSentTime.set(sessionId, Date.now());
                
                // Process audio data for monitoring
                if (audioProcessor?.processOutgoingAudioData) {
                    audioProcessor.processOutgoingAudioData(Buffer.from(audio.data, 'base64'));
                }

                audioLogger.info('✅ Audio forwarded to local successfully', {
                    audioLength: audio.data.length,
                    hasMimeType: !!audio.mimeType
                }, sessionId);

                eventBus.emit('audio:forward:local:success', {
                    sessionId,
                    audioSize: audio.data.length,
                    timestamp: Date.now()
                });

                return true;
            } else {
                audioLogger.error('❌ Failed to send audio to local WebSocket', {}, sessionId);

                eventBus.emit('audio:forward:local:failed', {
                    sessionId,
                    reason: 'send_failed'
                });

                return false;
            }
        } catch (error) {
            audioLogger.error('❌ Error forwarding audio to local', {
                error: error as Error,
                stack: error instanceof Error ? error.stack : undefined
            }, sessionId);

            eventBus.emit('audio:forward:local:error', {
                sessionId,
                error: error instanceof Error ? error.message : 'Unknown error'
            });

            return false;
        }
    }

    getLastAudioSentTime(sessionId: string): number | undefined {
        return this.lastAudioSentTime.get(sessionId);
    }

    getTimeSinceLastAudio(sessionId: string): number | null {
        const lastTime = this.lastAudioSentTime.get(sessionId);
        return lastTime ? Date.now() - lastTime : null;
    }
}