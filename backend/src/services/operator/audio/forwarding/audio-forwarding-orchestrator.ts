import { eventBus } from '@/modules/operator/event-system/event-bus';
import { AudioBufferManager, AudioData } from './audio-buffer-manager.js';
import { TwilioForwarder } from './twilio-forwarder.js';
import { LocalForwarder } from './local-forwarder.js';
import { ConnectionData } from '../../types/global.js';
import { AudioProcessor } from '../audio-processor.js';
import { audioLogger } from '@/utils/logger';

export interface AudioForwardingStats {
    sessionId: string;
    sessionType?: string;
    audioForwardingEnabled: boolean;
    lastAudioSent: number;
    timeSinceLastAudio: number | null;
    sequenceNumber?: number;
    streamSid?: string | null;
    hasTwilioWs?: boolean;
    hasLocalWs?: boolean;
}

export class AudioForwardingOrchestrator {
    private static instance: AudioForwardingOrchestrator;
    private bufferManager: AudioBufferManager;
    private twilioForwarder: TwilioForwarder;
    private localForwarder: LocalForwarder;
    private audioForwardingEnabled = new Map<string, boolean>();
    private sessionInitialized = new Map<string, boolean>();

    private constructor() {
        this.bufferManager = AudioBufferManager.getInstance();
        this.twilioForwarder = TwilioForwarder.getInstance();
        this.localForwarder = LocalForwarder.getInstance();
        this.setupEventListeners();
    }

    static getInstance(): AudioForwardingOrchestrator {
        if (!AudioForwardingOrchestrator.instance) {
            AudioForwardingOrchestrator.instance = new AudioForwardingOrchestrator();
        }
        return AudioForwardingOrchestrator.instance;
    }

    private setupEventListeners(): void {
        eventBus.on('audio:forward:request', async (data) => {
            await this.forwardAudio(
                data.sessionId,
                data.audio,
                data.connectionData,
                data.audioProcessor
            );
        });

        eventBus.on('audio:forward:retry', async (data) => {
            await this.processBufferedAudio(
                data.sessionId,
                data.connectionData,
                data.audioProcessor
            );
        });

        eventBus.on('session:cleanup', (data) => {
            this.cleanup(data.sessionId);
        });
    }

    initializeAudioForwarding(sessionId: string, connectionData: ConnectionData): void {
        this.audioForwardingEnabled.set(sessionId, true);
        this.sessionInitialized.set(sessionId, true);
        
        audioLogger.info('🎵 Audio forwarding initialized', {
            hasStreamSid: !!connectionData.streamSid,
            hasTwilioWs: !!connectionData.twilioWs,
            hasLocalWs: !!connectionData.localWs,
            sessionType: connectionData.sessionType
        }, sessionId);

        eventBus.emit('audio:forwarding:initialized', {
            sessionId,
            connectionData: {
                streamSid: connectionData.streamSid,
                sessionType: connectionData.sessionType
            }
        });
    }

    async forwardAudio(
        sessionId: string,
        audio: AudioData,
        connectionData: ConnectionData,
        audioProcessor: AudioProcessor
    ): Promise<boolean> {
        if (!this.audioForwardingEnabled.get(sessionId)) {
            audioLogger.debug('Audio forwarding disabled for session', {}, sessionId);
            return false;
        }

        let forwarded = false;

        // Determine forwarding type based on session
        if (connectionData.sessionType === 'twilio_call' && connectionData.twilioWs) {
            forwarded = await this.twilioForwarder.forward(
                sessionId,
                audio,
                connectionData,
                audioProcessor
            );
        } else if (connectionData.sessionType === 'local_test' && connectionData.localWs) {
            forwarded = await this.localForwarder.forward(
                sessionId,
                audio,
                connectionData,
                audioProcessor
            );
        } else {
            audioLogger.warn('⚠️ Unknown session type or missing WebSocket', {
                sessionType: connectionData.sessionType,
                hasTwilioWs: !!connectionData.twilioWs,
                hasLocalWs: !!connectionData.localWs
            }, sessionId);
        }

        // Buffer audio if forwarding failed
        if (!forwarded) {
            await this.bufferManager.bufferAudio(sessionId, audio);
            
            // Schedule retry
            setTimeout(() => {
                eventBus.emit('audio:forward:retry', {
                    sessionId,
                    connectionData,
                    audioProcessor
                });
            }, this.bufferManager.getRetryDelayMs());
        }

        return forwarded;
    }

    async processBufferedAudio(
        sessionId: string,
        connectionData: ConnectionData,
        audioProcessor: AudioProcessor
    ): Promise<void> {
        const bufferedAudio = await this.bufferManager.getBufferedAudio(sessionId);
        
        if (bufferedAudio.length === 0) {
            return;
        }

        audioLogger.info(`Processing ${bufferedAudio.length} buffered audio items`, {}, sessionId);

        for (const item of bufferedAudio) {
            let success = false;

            if (connectionData.sessionType === 'twilio_call') {
                success = await this.twilioForwarder.forward(
                    sessionId,
                    item.audio,
                    connectionData,
                    audioProcessor
                );
            } else if (connectionData.sessionType === 'local_test') {
                success = await this.localForwarder.forward(
                    sessionId,
                    item.audio,
                    connectionData,
                    audioProcessor
                );
            }

            if (success) {
                await this.bufferManager.removeFromBuffer(sessionId, item.audio);
            } else {
                const shouldRetry = await this.bufferManager.incrementRetryCount(sessionId, item.audio);
                if (!shouldRetry) {
                    audioLogger.error('Failed to forward buffered audio after max retries', {
                        sessionId,
                        audioAge: Date.now() - item.timestamp
                    });
                }
            }
        }
    }

    getAudioForwardingStats(sessionId: string, connectionData: ConnectionData): AudioForwardingStats {
        const isEnabled = this.audioForwardingEnabled.get(sessionId) || false;
        let lastAudioSent = 0;
        let timeSinceLastAudio: number | null = null;

        if (connectionData.sessionType === 'twilio_call') {
            const lastTime = this.twilioForwarder.getLastAudioSentTime(sessionId);
            if (lastTime) {
                lastAudioSent = lastTime;
                timeSinceLastAudio = Date.now() - lastTime;
            }
        } else if (connectionData.sessionType === 'local_test') {
            const lastTime = this.localForwarder.getLastAudioSentTime(sessionId);
            if (lastTime) {
                lastAudioSent = lastTime;
                timeSinceLastAudio = Date.now() - lastTime;
            }
        }

        return {
            sessionId,
            sessionType: connectionData.sessionType,
            audioForwardingEnabled: isEnabled,
            lastAudioSent,
            timeSinceLastAudio,
            sequenceNumber: connectionData.sequenceNumber,
            streamSid: connectionData.streamSid,
            hasTwilioWs: !!connectionData.twilioWs,
            hasLocalWs: !!connectionData.localWs
        };
    }

    cleanup(sessionId: string): void {
        this.audioForwardingEnabled.delete(sessionId);
        this.sessionInitialized.delete(sessionId);
        this.bufferManager.cleanup(sessionId);
        
        audioLogger.info('🧹 Audio forwarding cleanup completed', {}, sessionId);
    }

    isInitialized(sessionId: string): boolean {
        return this.sessionInitialized.get(sessionId) || false;
    }

    setAudioForwardingEnabled(sessionId: string, enabled: boolean): void {
        this.audioForwardingEnabled.set(sessionId, enabled);
        
        eventBus.emit('audio:forwarding:status', {
            sessionId,
            enabled,
            timestamp: new Date().toISOString()
        });
    }
}