import { EventDrivenComponent } from '@/modules/operator/event-system/event-patterns';
import { UlawConverter } from './ulaw-converter.js';
import { PCMConverter } from './pcm-converter.js';
import { AudioResampler } from './resampler.js';
import { Base64Converter } from './base64-converter.js';
import { AudioFormat } from '../audio-types.js';
import { audioLogger } from '@/utils/logger';

/**
 * Audio conversion orchestrator
 * Coordinates format conversion between Twilio and Gemini
 */
export class AudioConversionOrchestrator extends EventDrivenComponent {
    private ulawConverter: UlawConverter;
    private pcmConverter: PCMConverter;
    private resampler: AudioResampler;
    private base64Converter: Base64Converter;
    
    constructor() {
        super('AudioConversionOrchestrator');
        
        // Initialize converters
        this.ulawConverter = new UlawConverter();
        this.pcmConverter = new PCMConverter();
        this.resampler = new AudioResampler();
        this.base64Converter = new Base64Converter();
    }
    
    /**
     * Convert Twilio μ-law (8kHz) to Gemini PCM (24kHz)
     */
    async twilioToGemini(ulawBuffer: Buffer): Promise<Buffer> {
        try {
            // Step 1: Convert μ-law to PCM16
            const pcm16Buffer = this.ulawConverter.ulawToPCM(ulawBuffer);
            
            // Step 2: Convert to Float32 for resampling
            const float32Audio = this.pcmConverter.pcmToFloat32(pcm16Buffer, 16);
            
            // Step 3: Resample from 8kHz to 24kHz
            const resampled = this.resampler.resample(float32Audio, 8000, 24000);
            
            // Step 4: Convert back to PCM16
            const outputBuffer = this.pcmConverter.float32ToPCM(resampled, 16);
            
            this.emit('audio:processed', {
                sessionId: 'system',
                duration: outputBuffer.length / (24000 * 2), // 16-bit = 2 bytes per sample
                format: 'pcm16_24khz'
            });
            
            return outputBuffer;
            
        } catch (error) {
            audioLogger.error('Error converting Twilio to Gemini format:', error as Error);
            throw error;
        }
    }
    
    /**
     * Convert Gemini PCM (24kHz) to Twilio μ-law (8kHz)
     */
    async geminiToTwilio(base64Audio: string): Promise<string> {
        try {
            // Step 1: Decode base64
            const pcmBuffer = this.base64Converter.decode(base64Audio);
            
            // Step 2: Convert to Float32 for resampling
            const float32Audio = this.pcmConverter.pcmToFloat32(pcmBuffer, 16);
            
            // Step 3: Resample from 24kHz to 8kHz
            const resampled = this.resampler.downsampleWithFilter(float32Audio, 24000, 8000);
            
            // Step 4: Convert to PCM16
            const pcm16Buffer = this.pcmConverter.float32ToPCM(resampled, 16);
            
            // Step 5: Convert to μ-law
            const ulawBuffer = this.ulawConverter.pcmToUlaw(pcm16Buffer);
            
            // Step 6: Encode to base64
            const outputBase64 = this.base64Converter.encode(ulawBuffer);
            
            this.emit('audio:processed', {
                sessionId: 'system',
                duration: ulawBuffer.length / 8000, // 8kHz, 1 byte per sample
                format: 'ulaw_8khz'
            });
            
            return outputBase64;
            
        } catch (error) {
            audioLogger.error('Error converting Gemini to Twilio format:', error as Error);
            throw error;
        }
    }
    
    /**
     * Convert browser audio to Gemini format
     */
    async browserToGemini(base64Audio: string, sampleRate: number = 48000): Promise<Buffer> {
        try {
            // Step 1: Decode base64
            const pcmBuffer = this.base64Converter.decode(base64Audio);
            
            // Step 2: Convert to Float32
            const float32Audio = this.pcmConverter.pcmToFloat32(pcmBuffer, 16);
            
            // Step 3: Resample to 24kHz if needed
            const resampled = sampleRate !== 24000 
                ? this.resampler.resample(float32Audio, sampleRate, 24000)
                : float32Audio;
            
            // Step 4: Convert to PCM16
            const outputBuffer = this.pcmConverter.float32ToPCM(resampled, 16);
            
            return outputBuffer;
            
        } catch (error) {
            audioLogger.error('Error converting browser to Gemini format:', error as Error);
            throw error;
        }
    }
    
    /**
     * Generic convert method for any format conversion
     */
    async convert(
        audioData: Buffer | string,
        fromFormat: AudioFormat,
        toFormat: AudioFormat,
        options?: {
            fromSampleRate?: number;
            toSampleRate?: number;
            fromChannels?: number;
            toChannels?: number;
        }
    ): Promise<Buffer | string> {
        // Handle string to buffer conversions
        if (fromFormat === AudioFormat.ULAW_BASE64 && toFormat === AudioFormat.PCM_16) {
            const ulawBuffer = Buffer.from(audioData as string, 'base64');
            return this.twilioToGemini(ulawBuffer);
        }
        
        if (fromFormat === AudioFormat.PCM_16 && toFormat === AudioFormat.ULAW_BASE64) {
            // Convert PCM buffer to base64 first
            const base64Audio = (audioData as Buffer).toString('base64');
            return await this.geminiToTwilio(base64Audio);
        }
        
        if (fromFormat === AudioFormat.BASE64 && toFormat === AudioFormat.PCM_16) {
            return this.browserToGemini(
                audioData as string, 
                options?.fromSampleRate || 48000
            );
        }
        
        // Add more format conversions as needed
        throw new Error(`Unsupported conversion from ${fromFormat} to ${toFormat}`);
    }
    
    cleanup(): void {
        this.ulawConverter.cleanup();
        this.pcmConverter.cleanup();
        this.resampler.cleanup();
        this.base64Converter.cleanup();
        super.cleanup();
    }
}