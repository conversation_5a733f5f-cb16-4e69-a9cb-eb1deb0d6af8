import { eventBus } from '@/modules/operator/event-system/event-bus';
import { AudioConversionOrchestrator } from './converters/audio-conversion-orchestrator.js';
import { AudioFormat } from './audio-types.js';
import { logger } from '@/utils/logger';

export class AudioConversion {
  private static instance: AudioConversion;
  private orchestrator: AudioConversionOrchestrator;

  private constructor() {
    this.orchestrator = new AudioConversionOrchestrator();
    this.setupEventListeners();
  }

  static getInstance(): AudioConversion {
    if (!AudioConversion.instance) {
      AudioConversion.instance = new AudioConversion();
    }
    return AudioConversion.instance;
  }

  private setupEventListeners(): void {
    eventBus.on('audio:convert:request', async (data: any) => {
      try {
        const result = await this.convert(
          data.audioData,
          data.fromFormat,
          data.toFormat,
          data.options
        );
        eventBus.emit('audio:convert:success', {
          sessionId: data.sessionId,
          result,
          fromFormat: data.fromFormat,
          toFormat: data.toFormat
        });
      } catch (error) {
        eventBus.emit('audio:convert:error', {
          sessionId: data.sessionId,
          error: error instanceof Error ? error.message : 'Unknown conversion error',
          fromFormat: data.fromFormat,
          toFormat: data.toFormat
        });
      }
    });
  }

  async convert(
    audioData: Buffer | string,
    fromFormat: AudioFormat,
    toFormat: AudioFormat,
    options?: {
      fromSampleRate?: number;
      toSampleRate?: number;
      fromChannels?: number;
      toChannels?: number;
    }
  ): Promise<Buffer | string> {
    try {
      logger.debug(`Converting audio from ${fromFormat} to ${toFormat}`, {
        inputSize: typeof audioData === 'string' ? audioData.length : audioData.length,
        options
      });

      const result = await this.orchestrator.convert(
        audioData,
        fromFormat,
        toFormat,
        options
      );

      logger.debug(`Audio conversion completed`, {
        outputSize: typeof result === 'string' ? result.length : result.length
      });

      return result;
    } catch (error) {
      logger.error('Audio conversion failed', {
        error: error as Error,
        fromFormat,
        toFormat
      });
      throw error;
    }
  }

  async convertTwilioToGemini(ulawBase64: string): Promise<Buffer> {
    return this.convert(
      ulawBase64,
      AudioFormat.ULAW_BASE64,
      AudioFormat.PCM_16,
      {
        fromSampleRate: 8000,
        toSampleRate: 16000,
        fromChannels: 1,
        toChannels: 1
      }
    ) as Promise<Buffer>;
  }

  async convertGeminiToTwilio(pcm16Data: Buffer): Promise<string> {
    return this.convert(
      pcm16Data,
      AudioFormat.PCM_16,
      AudioFormat.ULAW_BASE64,
      {
        fromSampleRate: 16000,
        toSampleRate: 8000,
        fromChannels: 1,
        toChannels: 1
      }
    ) as Promise<string>;
  }

  destroy(): void {
    // Clean up if needed
  }
}

// Export singleton instance getter
export const getAudioConversion = () => AudioConversion.getInstance();