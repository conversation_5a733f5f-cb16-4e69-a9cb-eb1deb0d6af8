import { eventBus } from '@/modules/operator/event-system/event-bus';
import { AudioQualityOrchestrator } from './quality/audio-quality-orchestrator.js';
import { AudioFormat } from './audio-types.js';
import { logger } from '@/utils/logger';

export interface AudioQualityMetrics {
  volume: number;
  silenceRatio: number;
  clippingRatio: number;
  noiseLevel: number;
  isValid: boolean;
  warnings: string[];
}

export class AudioQuality {
  private static instance: AudioQuality;
  private orchestrator: AudioQualityOrchestrator;

  private constructor() {
    this.orchestrator = new AudioQualityOrchestrator();
    this.setupEventListeners();
  }

  static getInstance(): AudioQuality {
    if (!AudioQuality.instance) {
      AudioQuality.instance = new AudioQuality();
    }
    return AudioQuality.instance;
  }

  private setupEventListeners(): void {
    eventBus.on('audio:quality:check', async (data: any) => {
      try {
        const metrics = await this.analyze(
          data.audioData,
          data.format,
          data.sampleRate,
          data.sessionId
        );
        
        eventBus.emit('audio:quality:result', {
          sessionId: data.sessionId,
          metrics,
          timestamp: new Date().toISOString()
        });

        if (!metrics.isValid) {
          eventBus.emit('audio:quality:warning', {
            sessionId: data.sessionId,
            warnings: metrics.warnings,
            metrics
          });
        }
      } catch (error) {
        eventBus.emit('audio:quality:error', {
          sessionId: data.sessionId,
          error: error instanceof Error ? error.message : 'Unknown quality check error'
        });
      }
    });
  }

  async analyze(
    audioData: Buffer | string,
    format: AudioFormat,
    sampleRate: number,
    sessionId?: string
  ): Promise<AudioQualityMetrics> {
    try {
      logger.debug('Analyzing audio quality', {
        format,
        sampleRate,
        dataSize: typeof audioData === 'string' ? audioData.length : audioData.length,
        sessionId
      });

      const metrics = await this.orchestrator.analyzeQuality(
        audioData,
        format,
        sampleRate
      );

      if (sessionId) {
        this.orchestrator.updateSessionMetrics(sessionId, metrics);
      }

      logger.debug('Audio quality analysis completed', {
        metrics,
        sessionId
      });

      return metrics;
    } catch (error) {
      logger.error('Audio quality analysis failed', {
        error: error as Error,
        format,
        sampleRate,
        sessionId
      });
      throw error;
    }
  }

  async validateAudio(
    audioData: Buffer | string,
    format: AudioFormat,
    requirements?: {
      minVolume?: number;
      maxSilenceRatio?: number;
      maxClippingRatio?: number;
      maxNoiseLevel?: number;
    }
  ): Promise<{ valid: boolean; issues: string[] }> {
    const metrics = await this.analyze(audioData, format, 16000);
    const issues: string[] = [];

    if (requirements) {
      if (requirements.minVolume !== undefined && metrics.volume < requirements.minVolume) {
        issues.push(`Volume too low: ${metrics.volume.toFixed(2)} < ${requirements.minVolume}`);
      }
      if (requirements.maxSilenceRatio !== undefined && metrics.silenceRatio > requirements.maxSilenceRatio) {
        issues.push(`Too much silence: ${(metrics.silenceRatio * 100).toFixed(1)}%`);
      }
      if (requirements.maxClippingRatio !== undefined && metrics.clippingRatio > requirements.maxClippingRatio) {
        issues.push(`Audio clipping detected: ${(metrics.clippingRatio * 100).toFixed(1)}%`);
      }
      if (requirements.maxNoiseLevel !== undefined && metrics.noiseLevel > requirements.maxNoiseLevel) {
        issues.push(`Noise level too high: ${metrics.noiseLevel.toFixed(2)}`);
      }
    }

    return {
      valid: metrics.isValid && issues.length === 0,
      issues: [...metrics.warnings, ...issues]
    };
  }

  getSessionReport(sessionId: string): any {
    return this.orchestrator.getSessionReport(sessionId);
  }

  clearSessionData(sessionId: string): void {
    this.orchestrator.clearSessionData(sessionId);
  }

  destroy(): void {
    // Clean up if needed
  }
}

// Export singleton instance getter
export const getAudioQuality = () => AudioQuality.getInstance();