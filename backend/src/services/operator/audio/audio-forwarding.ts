/**
 * Audio Forwarding Module
 * Simplified interface for audio forwarding functionality
 */

import { eventBus } from '@/modules/operator/event-system/event-bus';
import { AudioForwardingOrchestrator } from './forwarding/audio-forwarding-orchestrator.js';
import { AudioData } from './forwarding/audio-buffer-manager.js';
import { ConnectionData } from './types/global.js';
import { AudioProcessor } from '../audio/audio-processor.js';

// Re-export types
export type { AudioData } from './forwarding/audio-buffer-manager.js';
export type { AudioForwardingStats } from './forwarding/audio-forwarding-orchestrator.js';

const orchestrator = AudioForwardingOrchestrator.getInstance();

/**
 * Forward audio to Twilio WebSocket with proper sequence number handling
 */
export async function forwardAudioToTwilio(
    sessionId: string,
    audio: AudioData,
    connectionData: ConnectionData,
    audioProcessor: AudioProcessor
): Promise<boolean> {
    connectionData.sessionType = 'twilio_call';
    return orchestrator.forwardAudio(sessionId, audio, connectionData, audioProcessor);
}

/**
 * Forward audio to local WebSocket for testing
 */
export async function forwardAudioToLocal(
    sessionId: string,
    audio: AudioData,
    connectionData: ConnectionData,
    audioProcessor: AudioProcessor
): Promise<boolean> {
    connectionData.sessionType = 'local_test';
    return orchestrator.forwardAudio(sessionId, audio, connectionData, audioProcessor);
}

/**
 * Forward audio based on session type
 */
export async function forwardAudio(
    sessionId: string,
    audio: AudioData,
    connectionData: ConnectionData,
    audioProcessor: AudioProcessor
): Promise<boolean> {
    return orchestrator.forwardAudio(sessionId, audio, connectionData, audioProcessor);
}

/**
 * Initialize audio forwarding for a session
 */
export function initializeAudioForwarding(
    sessionId: string,
    connectionData: ConnectionData
): void {
    orchestrator.initializeAudioForwarding(sessionId, connectionData);
}

/**
 * Get audio forwarding statistics
 */
export function getAudioForwardingStats(
    sessionId: string,
    connectionData: ConnectionData
) {
    return orchestrator.getAudioForwardingStats(sessionId, connectionData);
}

/**
 * Clean up audio forwarding resources
 */
export function cleanupAudioForwarding(sessionId: string): void {
    orchestrator.cleanup(sessionId);
}

/**
 * Clean up audio buffer for a session
 */
export function cleanupAudioBuffer(sessionId: string): void {
    eventBus.emit('session:cleanup', { sessionId });
}