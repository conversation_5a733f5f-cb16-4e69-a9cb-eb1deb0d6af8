#!/usr/bin/env node
/**
 * Operator Service Startup Script
 * 
 * Can run in two modes:
 * 1. DEMO_MODE=true - Runs the full Twilio-Gemini demo with all advanced features
 * 2. DEMO_MODE=false - Runs the modular architecture with provider separation
 */

import 'dotenv/config';
import { spawn } from 'child_process';
import { createModularIntegration } from './ModularIntegration.js';
import { logger } from '@/utils/logger';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DEMO_MODE = process.env.DEMO_MODE === 'true';
const USE_MODULAR = process.env.USE_MODULAR === 'true';

async function startOperatorService() {
  logger.info('🚀 Starting Operator Service...');
  logger.info(`Mode: ${DEMO_MODE ? 'Full Demo' : 'Modular Architecture'}`);

  if (DEMO_MODE && !USE_MODULAR) {
    // Run the full demo server
    logger.info('Starting Twilio-Gemini Demo Server...');
    
    const demoServer = spawn('node', ['demo-server.js'], {
      cwd: __dirname,
      env: { ...process.env },
      stdio: 'inherit'
    });

    demoServer.on('error', (error) => {
      logger.error('Failed to start demo server:', error);
      process.exit(1);
    });

    demoServer.on('exit', (code) => {
      logger.info(`Demo server exited with code ${code}`);
      process.exit(code || 0);
    });

  } else if (USE_MODULAR) {
    // Run with modular architecture
    logger.info('Starting Modular Operator Service...');
    
    try {
      const integration = createModularIntegration();
      await integration.start();

      // Also start the demo server for its endpoints
      if (DEMO_MODE) {
        logger.info('Starting demo server alongside modular integration...');
        const demoServer = spawn('node', ['demo-server.js'], {
          cwd: __dirname,
          env: { 
            ...process.env,
            PORT: String(parseInt(process.env.OPERATOR_DEMO_PORT || '3101') + 1)
          },
          stdio: 'inherit'
        });
      }

      // Handle graceful shutdown
      process.on('SIGTERM', async () => {
        logger.info('Received SIGTERM, shutting down gracefully...');
        await integration.stop();
        process.exit(0);
      });

      process.on('SIGINT', async () => {
        logger.info('Received SIGINT, shutting down gracefully...');
        await integration.stop();
        process.exit(0);
      });

    } catch (error) {
      logger.error('Failed to start modular integration:', error);
      process.exit(1);
    }

  } else {
    // Default: Run demo server
    logger.info('Starting default demo server...');
    require('./demo-server');
  }
}

// Validate environment before starting
function validateEnvironment(): boolean {
  const required = [
    'GEMINI_API_KEY',
    'TWILIO_ACCOUNT_SID', 
    'TWILIO_AUTH_TOKEN',
    'PUBLIC_URL'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    logger.error(`Missing required environment variables: ${missing.join(', ')}`);
    logger.error('Please check your .env file');
    return false;
  }

  return true;
}

// Main entry point
if (require.main === module) {
  if (!validateEnvironment()) {
    process.exit(1);
  }

  startOperatorService().catch(error => {
    logger.error('Fatal error:', error);
    process.exit(1);
  });
}

export { startOperatorService };