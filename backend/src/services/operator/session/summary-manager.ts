// Session Summary Manager - handles session summary generation and storage

import { promises as fs } from 'fs';
import path from 'path';
import { sessionLogger } from '@/utils/logger.js';
import { ConnectionData } from './types/global.js';
import { ContextManager } from './context-manager.js';

interface SummaryStatus {
    inProgress: boolean;
    completed: boolean;
    failed: boolean;
    timestamp: number;
}

interface ExtendedConnectionData extends ConnectionData {
    summaryFlowType?: string;
    summaryTimeoutId?: NodeJS.Timeout;
}

export class SessionSummaryManager {
    private summaryTimeouts: Map<string, NodeJS.Timeout>;
    private summaryInProgress: Set<string>;
    private defaultSummaryTimeout: number;
    private summaryPrompts: Record<string, string>;

    constructor() {
        this.summaryTimeouts = new Map(); // Track summary timeouts
        this.summaryInProgress = new Set(); // Track sessions currently generating summaries
        this.defaultSummaryTimeout = 30000; // 30 seconds timeout for summary generation (increased from 15s)

        // Summary prompts - use environment variable only, no hardcoded fallbacks
        this.summaryPrompts = {
            outbound_call: process.env.SUMMARY_GENERATION_PROMPT || '',
            inbound_call: process.env.SUMMARY_GENERATION_PROMPT || '',
            outbound_test: process.env.SUMMARY_GENERATION_PROMPT || '',
            inbound_test: process.env.SUMMARY_GENERATION_PROMPT || '',
            default: process.env.SUMMARY_GENERATION_PROMPT || ''
        };
    }

    /**
     * Request session summary from AI with flow-specific prompts
     * @param callSid - Call/session ID
     * @param connectionData - Connection data with Gemini session
     * @param contextManager - Context manager for saving summary state
     * @returns Success status
     */
    async requestSummary(callSid: string, connectionData: ConnectionData, contextManager: ContextManager): Promise<boolean> {
        const extConnectionData = connectionData as ExtendedConnectionData;

        if (!extConnectionData || extConnectionData.summaryRequested || extConnectionData.summaryReceived) {
            sessionLogger.debug(`[${callSid}] Summary request skipped (already requested/received or no connection data)`);
            return false;
        }

        if (this.summaryInProgress.has(callSid)) {
            sessionLogger.debug(`[${callSid}] Summary generation already in progress`);
            return false;
        }

        // Determine flow type for appropriate summary prompt
        const flowType = this.determineFlowType(extConnectionData);
        const summaryPrompt = this.getSummaryPrompt(flowType);

        sessionLogger.info(`[${callSid}] Requesting ${flowType} session summary from AI`);

        extConnectionData.summaryRequested = true;
        extConnectionData.summaryText = '';
        extConnectionData.summaryFlowType = flowType;
        this.summaryInProgress.add(callSid);

        try {
            // Check if we have a valid Gemini session
            if (!extConnectionData.geminiSession) {
                sessionLogger.warn(`[${callSid}] No active Gemini session for summary request`);
                return await this.generateFallbackSummary(callSid, extConnectionData, contextManager);
            }

            // Set timeout for summary generation
            const timeoutId = setTimeout(async () => {
                sessionLogger.warn(`[${callSid}] Summary generation timeout - using fallback`);
                await this.handleSummaryTimeout(callSid, extConnectionData, contextManager);
            }, this.defaultSummaryTimeout);

            extConnectionData.summaryTimeoutId = timeoutId;
            this.summaryTimeouts.set(callSid, timeoutId);

            // Send flow-specific summary request to AI using Live API - FIXED METHOD
            // Use sendRealtimeInput with proper text data format
            await extConnectionData.geminiSession.sendRealtimeInput({
                media: {
                    data: Buffer.from(summaryPrompt, 'utf-8').toString('base64'),
                    mimeType: 'text/plain'
                }
            });

            sessionLogger.debug(`[${callSid}] Summary request sent to AI with ${flowType} prompt`);
            return true;

        } catch (error) {
            sessionLogger.error(`[${callSid}] Error requesting summary:`, error as Error);
            this.summaryInProgress.delete(callSid);
            extConnectionData.summaryRequested = false;
            return await this.generateFallbackSummary(callSid, extConnectionData, contextManager);
        }
    }

    /**
     * Determine the flow type from connection data
     * @param connectionData - Connection data
     * @returns Flow type
     */
    private determineFlowType(connectionData: ExtendedConnectionData): string {
        // Check for flow type in connection data
        if (connectionData.flowType) {
            return connectionData.flowType;
        }

        // Fallback logic based on connection properties
        if (connectionData.isIncomingCall) {
            return connectionData.isTestMode ? 'inbound_test' : 'inbound_call';
        } else {
            return connectionData.isTestMode ? 'outbound_test' : 'outbound_call';
        }
    }

    /**
     * Get appropriate summary prompt for flow type
     * @param flowType - Flow type
     * @returns Summary prompt
     */
    private getSummaryPrompt(flowType: string): string {
        return this.summaryPrompts[flowType] || this.summaryPrompts.default;
    }

    /**
     * Handle summary response from AI
     * @param callSid - Call/session ID
     * @param summaryText - Summary text from AI
     * @param connectionData - Connection data
     * @param contextManager - Context manager
     */
    async handleSummaryResponse(callSid: string, summaryText: string, connectionData: ConnectionData, contextManager: ContextManager): Promise<void> {
        const extConnectionData = connectionData as ExtendedConnectionData;

        if (!this.summaryInProgress.has(callSid)) {
            sessionLogger.warn(`[${callSid}] Received summary but no generation in progress`);
            return;
        }

        sessionLogger.info(`[${callSid}] Received summary response from AI (${summaryText.length} chars)`);

        // Clear timeout since we received a response
        this.clearSummaryTimeout(callSid, extConnectionData);

        // Store the summary text
        extConnectionData.summaryText = summaryText;
        extConnectionData.summaryReceived = true;

        // Save summary to file with flow type
        await this.saveSummaryInfo(
            callSid,
            summaryText,
            'neutral', // Default sentiment
            'completed',
            extConnectionData.targetName,
            extConnectionData.targetPhoneNumber,
            extConnectionData.summaryFlowType
        );

        // Save context with summary (using saveSessionContext)
        const existingContext = contextManager.getSessionContext(callSid);
        if (existingContext) {
            // Update existing context with summary info
            existingContext.conversationState.summaryText = summaryText;
            existingContext.conversationState.summaryReceived = true;
            contextManager.contextStore.set(callSid, existingContext);
        }

        this.summaryInProgress.delete(callSid);
        sessionLogger.info(`[${callSid}] Summary processing completed`);
    }

    /**
     * Generate fallback summary from conversation data with flow-specific formatting
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     * @param contextManager - Context manager
     * @returns Success status
     */
    private async generateFallbackSummary(callSid: string, connectionData: ExtendedConnectionData, contextManager: ContextManager): Promise<boolean> {
        try {
            sessionLogger.info(`[${callSid}] Generating fallback summary from conversation data`);

            // Get conversation context (using getSessionContext method)
            const context = contextManager.getSessionContext(callSid);
            const conversationLog = context?.conversationState?.conversationLog || [];
            const fullTranscript = context?.conversationState?.fullTranscript || [];

            // Generate basic summary from available data
            const flowType = connectionData.summaryFlowType || this.determineFlowType(connectionData);
            const duration = Math.round((Date.now() - (connectionData.sessionStartTime || Date.now())) / 1000);
            const summaryText = `[${flowType.toUpperCase()} SUMMARY]\n\nCall Duration: ${duration}s\nConversation Entries: ${conversationLog.length}\nTranscript Entries: ${fullTranscript.length}\n\nStatus: Session completed with fallback summary generation.`;

            // Save fallback summary
            await this.saveSummaryInfo(
                callSid,
                summaryText,
                'neutral',
                'fallback',
                connectionData.targetName,
                connectionData.targetPhoneNumber,
                flowType
            );

            connectionData.summaryText = summaryText;
            connectionData.summaryReceived = true;
            this.summaryInProgress.delete(callSid);

            sessionLogger.info(`[${callSid}] Fallback summary generated successfully`);
            return true;

        } catch (error) {
            sessionLogger.error(`[${callSid}] Error generating fallback summary:`, error as Error);
            this.summaryInProgress.delete(callSid);
            return false;
        }
    }

    /**
     * Handle summary timeout
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     * @param contextManager - Context manager
     */
    private async handleSummaryTimeout(callSid: string, connectionData: ExtendedConnectionData, contextManager: ContextManager): Promise<void> {
        sessionLogger.warn(`[${callSid}] Summary generation timed out`);
        this.clearSummaryTimeout(callSid, connectionData);

        if (connectionData.summaryText && connectionData.summaryText.length > 0) {
            // Use partial summary if available
            sessionLogger.info(`[${callSid}] Using partial summary received before timeout`);
            await this.saveSummaryInfo(
                callSid,
                connectionData.summaryText,
                'neutral',
                'timeout',
                connectionData.targetName,
                connectionData.targetPhoneNumber
            );
        } else {
            // Generate fallback summary
            await this.generateFallbackSummary(callSid, connectionData, contextManager);
        }

        this.summaryInProgress.delete(callSid);
    }

    /**
     * Clear summary timeout
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     */
    private clearSummaryTimeout(callSid: string, connectionData: ExtendedConnectionData): void {
        if (connectionData.summaryTimeoutId) {
            clearTimeout(connectionData.summaryTimeoutId);
            connectionData.summaryTimeoutId = undefined;
        }

        if (this.summaryTimeouts.has(callSid)) {
            const timeoutId = this.summaryTimeouts.get(callSid);
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
            this.summaryTimeouts.delete(callSid);
        }
    }

    /**
     * Save summary information to file with flow type support
     * @param callSid - Call/session ID
     * @param rawSummaryText - Raw summary text
     * @param defaultSentiment - Default sentiment
     * @param status - Call status
     * @param targetName - Target name
     * @param targetPhoneNumber - Target phone number
     * @param flowType - Flow type (outbound_call, inbound_call, etc.)
     */
    async saveSummaryInfo(
        callSid: string,
        rawSummaryText: string,
        defaultSentiment: 'positive' | 'negative' | 'neutral' = 'neutral',
        status: string = 'completed',
        targetName: string | null = null,
        targetPhoneNumber: string | null = null,
        flowType: string | null = null
    ): Promise<void> {
        const dataDir = path.join(process.cwd(), 'data');
        const infoFilePath = path.join(dataDir, `${callSid}_info.json`);

        try {
            // Ensure data directory exists
            await fs.mkdir(dataDir, { recursive: true });

            // Parse summary for structured data
            const summaryInfo = {
                callSid,
                timestamp: new Date().toISOString(),
                status,
                flowType: flowType || 'unknown',
                targetName: targetName || 'Unknown',
                targetPhoneNumber: targetPhoneNumber || 'Unknown',
                rawSummary: rawSummaryText,
                sentiment: defaultSentiment,
                duration: 'Unknown',
                outcome: 'Unknown',
                notes: rawSummaryText
            };

            // Save to file
            await fs.writeFile(infoFilePath, JSON.stringify(summaryInfo, null, 2), 'utf-8');
            sessionLogger.info(`[${callSid}] Summary info saved to ${infoFilePath}`);

        } catch (error) {
            sessionLogger.error(`[${callSid}] Error saving summary info:`, error as Error);
        }
    }

    /**
     * Check if summary is in progress
     * @param callSid - Call/session ID
     * @returns True if summary is in progress
     */
    isSummaryInProgress(callSid: string): boolean {
        return this.summaryInProgress.has(callSid);
    }

    /**
     * Get summary status
     * @param callSid - Call/session ID
     * @returns Summary status
     */
    getSummaryStatus(callSid: string): SummaryStatus {
        return {
            inProgress: this.summaryInProgress.has(callSid),
            completed: false, // Would need to track this separately
            failed: false, // Would need to track this separately
            timestamp: Date.now()
        };
    }

    /**
     * Clean up summary tracking for a session
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     */
    cleanupSummary(callSid: string, connectionData: ConnectionData): void {
        this.clearSummaryTimeout(callSid, connectionData as ExtendedConnectionData);
        this.summaryInProgress.delete(callSid);
        sessionLogger.debug(`[${callSid}] Summary tracking cleaned up`);
    }

    /**
     * Clean up all summary resources (for shutdown)
     */
    cleanup(): void {
        const timeoutCount = this.summaryTimeouts.size;
        const progressCount = this.summaryInProgress.size;

        // Clear all summary timeouts
        for (const [, timeout] of this.summaryTimeouts.entries()) {
            clearTimeout(timeout);
        }

        this.summaryTimeouts.clear();
        this.summaryInProgress.clear();

        sessionLogger.info(`SummaryManager: Cleared ${timeoutCount} timeouts and ${progressCount} in-progress summaries`);
    }
}