import { EventDrivenComponent } from '@/modules/operator/event-system/event-patterns';
import { SessionCreator } from '../handlers/session/create-session.js';
import { SessionRecoveryHandler } from '../handlers/session/recover-session.js';
import { SessionCleanupHandler } from '../handlers/session/cleanup-session.js';
import { GeminiSessionService } from '../gemini/session-service.js';
import { SessionMetricsService } from '../session/metrics-service.js';
import { AudioRouter } from '../handlers/audio/route-audio.js';
import { ContextManager } from './context-manager.js';
import { AudioProcessor } from '../audio/audio-processor.js';
import { 
    ConnectionData, 
    GeminiClient,
    GeminiSession,
    ExtendedConnectionData
} from './types/global.js';
import { SessionConfig } from './types/websocket.js';
import { SessionMetrics } from './metrics.js';

/**
 * Refactored SessionManager using micro-components
 * Each component is under 150 lines following KISS principle
 */
export class SessionManagerRefactored extends EventDrivenComponent {
    private sessionCreator: SessionCreator;
    private recoveryHandler: SessionRecoveryHandler;
    private cleanupHandler: SessionCleanupHandler;
    private geminiService: GeminiSessionService;
    private metricsService: SessionMetricsService;
    private audioRouter: AudioRouter;
    private audioProcessor: AudioProcessor;
    
    constructor(
        private contextManager: ContextManager,
        private geminiClient: GeminiClient,
        private activeConnections: Map<string, ConnectionData> | null = null
    ) {
        super('SessionManagerRefactored');
        
        // Initialize micro-components
        this.sessionCreator = new SessionCreator(geminiClient);
        this.recoveryHandler = new SessionRecoveryHandler();
        this.cleanupHandler = new SessionCleanupHandler();
        this.geminiService = new GeminiSessionService();
        this.metricsService = new SessionMetricsService();
        this.audioRouter = new AudioRouter();
        this.audioProcessor = new AudioProcessor();
        
        // Subscribe to relevant events
        this.setupEventHandlers();
    }
    
    private setupEventHandlers(): void {
        // Handle session lifecycle events
        this.subscribe('session:created', this.handleSessionCreated.bind(this));
        this.subscribe('session:error', this.handleSessionError.bind(this));
    }
    
    async createGeminiSession(
        callSid: string,
        config: SessionConfig,
        connectionData: ConnectionData
    ): Promise<GeminiSession | null> {
        return this.sessionCreator.createSession(callSid, config, connectionData);
    }
    
    async recoverSession(callSid: string, reason: string): Promise<void> {
        return this.recoveryHandler.recoverSession(callSid, reason);
    }
    
    async cleanupSession(callSid: string): Promise<void> {
        return this.cleanupHandler.cleanupSession(callSid);
    }
    
    async sendTextToGemini(
        sessionId: string, 
        geminiSession: GeminiSession, 
        text: string
    ): Promise<void> {
        await geminiSession.sendRealtimeInput({ 
            media: { 
                data: Buffer.from(text, 'utf-8').toString('base64'), 
                mimeType: 'text/plain' 
            } 
        });
        await this.geminiService.sendText(sessionId, text);
    }
    
    async sendTurnComplete(
        sessionId: string, 
        geminiSession: GeminiSession
    ): Promise<void> {
        // Send turn complete message to Gemini
        await geminiSession.send({
            clientContent: {
                turns: [{
                    role: 'user',
                    parts: []
                }],
                turnComplete: true
            }
        });
        
        // Turn completed - event removed as it's not in the event types
    }
    
    async sendAudioToGemini(
        callSid: string, 
        geminiSession: GeminiSession, 
        audioBuffer: Buffer
    ): Promise<void> {
        await this.audioRouter.routeAudio(callSid, geminiSession, audioBuffer);
    }
    
    async sendBrowserAudioToGemini(
        callSid: string, 
        geminiSession: GeminiSession, 
        base64Audio: string
    ): Promise<void> {
        await this.audioRouter.routeBrowserAudio(callSid, geminiSession, base64Audio);
    }
    
    getSessionMetrics(callSid: string): SessionMetrics | null {
        return this.metricsService.getSessionMetrics(callSid);
    }
    
    setAudioSettings(settings: any): boolean {
        this.audioProcessor.setSettings(settings);
        return true;
    }
    
    getAudioSettings(): any {
        // Return current audio settings
        return {
            enableDeEssing: false,
            enableNoiseReduction: false,
            enableCompression: false,
            enableAGC: false,
            compressionRatio: 0.5,
            noiseThreshold: -60,
            agcTargetLevel: -20
        };
    }
    
    async generateSummary(sessionId: string): Promise<string> {
        // Delegate to the context manager for summary generation
        const context = this.contextManager.getSessionContext(sessionId);
        if (!context) {
            throw new Error(`Session ${sessionId} not found`);
        }
        
        // Generate summary from conversation log
        const summary = context.conversationState.conversationLog
            .map((entry: any) => `${entry.speaker}: ${entry.text}`)
            .join('\n');
            
        this.emit('transcription:summary:generated', { sessionId, summary });
        return summary;
    }
    
    private handleSessionCreated(payload: { 
        sessionId: string; 
        phoneNumber?: string; 
        timestamp: number 
    }): void {
        // Additional session setup if needed
        const connectionData = this.activeConnections?.get(payload.sessionId);
        if (connectionData) {
            this.contextManager.saveSessionContext(payload.sessionId, {
                ...connectionData,
                aiInstructions: connectionData.originalAIInstructions || '',
                voice: 'Aoede',  // Default voice
                model: 'gemini-2.0-flash-exp',  // Default model
                conversationLog: [],
                fullTranscript: [],
                speechTranscript: [],
                maxConversationLogSize: 1000,
                maxTranscriptSize: 2000,
                maxSpeechTranscriptSize: 2000
            });
        }
    }
    
    private handleSessionError(payload: { 
        sessionId: string; 
        error: Error; 
        context: string 
    }): void {
        // Handle errors centrally
        if (payload.context === 'session_creation' || payload.context === 'gemini_connection') {
            this.contextManager.markSessionInterrupted(payload.sessionId, payload.context);
        }
    }
    
    cleanup(): void {
        // Cleanup all components
        this.sessionCreator.cleanup();
        this.recoveryHandler.cleanup();
        this.cleanupHandler.cleanup();
        this.geminiService.cleanup();
        this.metricsService.cleanup();
        this.audioRouter.cleanup();
        super.cleanup();
    }
}

// Export alias for backward compatibility
export { SessionManagerRefactored as SessionManager };