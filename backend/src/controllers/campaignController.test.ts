import { FastifyRequest, FastifyReply } from 'fastify';
import fs from 'fs';
import path from 'path';
import { getCampaignScriptContent, getCampaignScript, handleCampaignRequest } from './campaignController.js';
import { AppError, ErrorCodes, ErrorSeverity } from '../utils/errorHandler.js';
import logger from '../utils/logger.js';
// Actual Zod schema will be imported and used by the module, so we just need to provide data that passes/fails it.

// Mock logger
jest.mock('../utils/logger', () => ({
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
}));

// Mock fs.promises
jest.mock('fs', () => ({
  ...jest.requireActual('fs'), // Keep other fs parts if any, though we only mock promises here
  promises: {
    readFile: jest.fn(),
    access: jest.fn(),
  },
}));

// Helper to create mock request/reply for Fastify
const mockRequest = (data: any = {}): FastifyRequest => ({
  params: data.params || {},
  body: data.body || {},
  query: data.query || {},
  // Add other properties if your controller uses them
  ...data.requestExtras,
} as FastifyRequest);

const mockReply = (): FastifyReply => {
  const reply: any = {};
  reply.status = jest.fn().mockReturnValue(reply);
  reply.send = jest.fn().mockReturnValue(reply);
  reply.header = jest.fn().mockReturnValue(reply); // If used
  return reply as FastifyReply;
};


describe('Campaign Controller', () => {
  beforeEach(() => {
    // Clear all mock implementations and calls before each test
    (fs.promises.readFile as jest.Mock).mockReset();
    (fs.promises.access as jest.Mock).mockReset();
    (logger.error as jest.Mock).mockClear();
    (logger.warn as jest.Mock).mockClear();
  });

  describe('getCampaignScriptContent', () => {
    const validCampaignId = 'testCampaign123';
    const validCampaignContent = {
      name: 'Test Campaign',
      script: { startNode: 'greeting' },
      // other fields to satisfy CampaignFileContentSchema
    };

    it('should return parsed content for a valid campaign ID and valid JSON', async () => {
      (fs.promises.readFile as jest.Mock).mockResolvedValue(JSON.stringify(validCampaignContent));

      const result = await getCampaignScriptContent(validCampaignId);
      expect(result).toEqual(validCampaignContent);
      expect(fs.promises.readFile).toHaveBeenCalledWith(
        path.resolve(__dirname, '../../campaign-scripts', `${validCampaignId}.json`),
        'utf-8'
      );
    });

    it('should throw VALIDATION_ERROR AppError for invalid campaignId format', async () => {
      const invalidCampaignId = '../invalid-id';
      await expect(getCampaignScriptContent(invalidCampaignId)).rejects.toThrow(AppError);
      try {
        await getCampaignScriptContent(invalidCampaignId);
      } catch (e: any) {
        expect(e.code).toBe(ErrorCodes.VALIDATION_ERROR.code);
        expect(e.message).toContain('Invalid campaign ID format');
      }
    });

    it('should throw NOT_FOUND AppError if file does not exist (ENOENT)', async () => {
      const error: any = new Error('File not found');
      error.code = 'ENOENT';
      (fs.promises.readFile as jest.Mock).mockRejectedValue(error);

      await expect(getCampaignScriptContent(validCampaignId)).rejects.toThrow(AppError);
      try {
        await getCampaignScriptContent(validCampaignId);
      } catch (e: any) {
        expect(e.code).toBe(ErrorCodes.NOT_FOUND.code);
      }
    });

    it('should throw VALIDATION_ERROR AppError for malformed JSON content', async () => {
      (fs.promises.readFile as jest.Mock).mockResolvedValue('this is not json');

      await expect(getCampaignScriptContent(validCampaignId)).rejects.toThrow(AppError);
      try {
        await getCampaignScriptContent(validCampaignId);
      } catch (e: any) {
        expect(e.code).toBe(ErrorCodes.VALIDATION_ERROR.code);
        expect(e.message).toContain('is not valid JSON');
      }
    });

    it('should throw VALIDATION_ERROR AppError for JSON content failing Zod schema (missing name)', async () => {
      const invalidSchemaContent = { script: { node1: 'test'} /* missing name */ };
      (fs.promises.readFile as jest.Mock).mockResolvedValue(JSON.stringify(invalidSchemaContent));

      await expect(getCampaignScriptContent(validCampaignId)).rejects.toThrow(AppError);
      try {
        await getCampaignScriptContent(validCampaignId);
      } catch (e: any) {
        expect(e.code).toBe(ErrorCodes.VALIDATION_ERROR.code);
        expect(e.message).toContain('Invalid campaign script format');
        expect(e.context.errors).toBeDefined(); // Zod errors should be in context
      }
    });

    it('should throw VALIDATION_ERROR AppError for JSON content failing Zod schema (missing script/content)', async () => {
      const invalidSchemaContent = { name: 'Test Campaign' /* missing script and content */ };
      (fs.promises.readFile as jest.Mock).mockResolvedValue(JSON.stringify(invalidSchemaContent));

      await expect(getCampaignScriptContent(validCampaignId)).rejects.toThrow(AppError);
      try {
        await getCampaignScriptContent(validCampaignId);
      } catch (e: any) {
        expect(e.code).toBe(ErrorCodes.VALIDATION_ERROR.code);
        expect(e.message).toContain('Invalid campaign script format');
        expect(e.context.errors).toBeDefined();
        expect(e.context.errors[0].message).toBe("Either 'script' or 'content' must be defined in the campaign file.");
      }
    });

     it('should throw INTERNAL_SERVER_ERROR AppError for other fs.promises.readFile errors', async () => {
      const otherError = new Error('Disk is full'); // Not ENOENT
      (fs.promises.readFile as jest.Mock).mockRejectedValue(otherError);

      await expect(getCampaignScriptContent(validCampaignId)).rejects.toThrow(AppError);
      try {
        await getCampaignScriptContent(validCampaignId);
      } catch (e: any) {
        expect(e.code).toBe(ErrorCodes.INTERNAL_SERVER_ERROR.code);
      }
    });
  });

  describe('getCampaignScript Route Handler', () => {
    it('should throw AppError with VALIDATION_ERROR if campaignId is missing', async () => {
      const req = mockRequest({ params: {} }); // No campaignId
      const rep = mockReply();

      // This route handler now directly throws, assuming a global error handler picks it up
      await expect(getCampaignScript(req, rep)).rejects.toThrow(AppError);
       try {
        await getCampaignScript(req, rep);
      } catch (e: any) {
        expect(e.code).toBe(ErrorCodes.VALIDATION_ERROR.code);
        expect(e.message).toContain('Campaign ID is required');
      }
    });

    it('should call getCampaignScriptContent and reply.send on success', async () => {
      const campaignId = 'testCampaign';
      const scriptContent = { name: 'Test', script: {} };
      (fs.promises.readFile as jest.Mock).mockResolvedValue(JSON.stringify(scriptContent)); // Mock for getCampaignScriptContent

      const req = mockRequest({ params: { campaignId } });
      const rep = mockReply();

      await getCampaignScript(req, rep);

      expect(rep.send).toHaveBeenCalledWith(scriptContent);
    });

     it('should propagate AppError from getCampaignScriptContent', async () => {
      const campaignId = '../testCampaign'; // Invalid ID
      const req = mockRequest({ params: { campaignId } });
      const rep = mockReply();

      // Expect getCampaignScriptContent to throw, and getCampaignScript to propagate it
      await expect(getCampaignScript(req, rep)).rejects.toThrow(AppError);
      try {
          await getCampaignScript(req, rep);
      } catch (e: any) {
          expect(e.code).toBe(ErrorCodes.VALIDATION_ERROR.code); // Error from getCampaignScriptContent
          expect(e.message).toContain('Invalid campaign ID format');
      }
      expect(rep.send).not.toHaveBeenCalled();
    });
  });

  describe('handleCampaignRequest Route Handler', () => {
    it('should throw AppError with VALIDATION_ERROR if campaignType is missing', async () => {
      const req = mockRequest({ body: { language: 'en' } }); // No campaignType
      const rep = mockReply();

      await expect(handleCampaignRequest(req, rep)).rejects.toThrow(AppError);
      try {
        await handleCampaignRequest(req, rep);
      } catch (e: any) {
        expect(e.code).toBe(ErrorCodes.VALIDATION_ERROR.code);
        expect(e.message).toContain('Campaign type is required');
      }
    });

    it('should throw AppError with VALIDATION_ERROR if campaignType is invalid', async () => {
      const req = mockRequest({ body: { campaignType: '../invalid', language: 'en' } });
      const rep = mockReply();

      await expect(handleCampaignRequest(req, rep)).rejects.toThrow(AppError);
       try {
        await handleCampaignRequest(req, rep);
      } catch (e: any) {
        expect(e.code).toBe(ErrorCodes.VALIDATION_ERROR.code);
        expect(e.message).toContain('Invalid campaign type format');
      }
    });

    it('should call getCampaignScriptContent with campaignType if language is "en"', async () => {
      const campaignType = 'baseCampaign';
      const scriptContent = { name: 'Base', script: {} };
      (fs.promises.readFile as jest.Mock).mockResolvedValue(JSON.stringify(scriptContent));

      const req = mockRequest({ body: { campaignType, language: 'en' } });
      const rep = mockReply();

      await handleCampaignRequest(req, rep);
      // getCampaignScriptContent will be called with 'baseCampaign'
      expect(fs.promises.readFile).toHaveBeenCalledWith(
        expect.stringContaining(`${campaignType}.json`), 'utf-8'
      );
      expect(rep.send).toHaveBeenCalledWith(scriptContent);
    });

    it('should try language-specific campaignId then fallback if fs.access fails', async () => {
      const campaignType = 'survey';
      const language = 'fr';
      const langCampaignId = `${campaignType}_${language}`; // survey_fr
      const baseScriptContent = { name: 'Base Survey', script: {} };

      // Mock fs.access to fail (language-specific does not exist)
      (fs.promises.access as jest.Mock).mockRejectedValue(new Error('File not found for access check'));
      // Mock readFile to return the base campaign content when called with 'survey'
      (fs.promises.readFile as jest.Mock).mockImplementation(filePath => {
        if (filePath.includes(`${campaignType}.json`) && !filePath.includes(langCampaignId)) {
          return Promise.resolve(JSON.stringify(baseScriptContent));
        }
        return Promise.reject(new Error('Should not try to read language specific if access failed'));
      });

      const req = mockRequest({ body: { campaignType, language } });
      const rep = mockReply();

      await handleCampaignRequest(req, rep);

      expect(fs.promises.access).toHaveBeenCalledWith(
        expect.stringContaining(`${langCampaignId}.json`)
      );
      // Should have fallen back to base campaignType
      expect(fs.promises.readFile).toHaveBeenCalledWith(
        expect.stringContaining(`${campaignType}.json`), // Not langCampaignId
        'utf-8'
      );
      expect(rep.send).toHaveBeenCalledWith(baseScriptContent);
      expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining(`Language-specific campaign not found: ${langCampaignId}`));
    });

    it('should use language-specific campaignId if fs.access succeeds', async () => {
      const campaignType = 'promo';
      const language = 'es';
      const langCampaignId = `${campaignType}_${language}`; // promo_es
      const langScriptContent = { name: 'Promo ES', script: {} };

      (fs.promises.access as jest.Mock).mockResolvedValue(undefined); // File exists
      (fs.promises.readFile as jest.Mock).mockResolvedValue(JSON.stringify(langScriptContent));

      const req = mockRequest({ body: { campaignType, language } });
      const rep = mockReply();

      await handleCampaignRequest(req, rep);

      expect(fs.promises.access).toHaveBeenCalledWith(
        expect.stringContaining(`${langCampaignId}.json`)
      );
      expect(fs.promises.readFile).toHaveBeenCalledWith(
        expect.stringContaining(`${langCampaignId}.json`),
        'utf-8'
      );
      expect(rep.send).toHaveBeenCalledWith(langScriptContent);
    });
  });
});
