/**
 * Unit tests for Operator Live Audio functionality
 * Tests the integration between Twilio and Gemini Live API
 */

import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals'
import { OperatorLiveAudioHandler } from '../../services/websocket/operatorLiveAudioHandler.js'
import { GeminiLiveAudioService } from '../../services/ai/geminiLiveAudioService.js'
import WebSocket from 'ws'

// Mock dependencies
jest.mock('../../services/ai/geminiLiveAudioService')
jest.mock('../../utils/logger', () => ({
  default: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }
}))

describe('OperatorLiveAudioHandler', () => {
  let handler: OperatorLiveAudioHandler
  let mockGeminiService: jest.Mocked<GeminiLiveAudioService>

  beforeEach(() => {
    handler = new OperatorLiveAudioHandler()
    mockGeminiService = GeminiLiveAudioService as any
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('createSession', () => {
    it('should create a live audio session successfully', async () => {
      const callSid = 'CALL123'
      const operatorId = 'OP456'
      const metadata = {
        targetPhoneNumber: '+1234567890',
        targetName: 'John Doe',
        campaignId: 'CAMP789'
      }

      // Mock Gemini service response
      const mockLiveSession = {
        id: 'session_123',
        callSid,
        isActive: true,
        isRecording: false,
        startTime: new Date(),
        lastActivity: new Date()
      }

      mockGeminiService.prototype.createSession = jest.fn().mockResolvedValue(mockLiveSession)

      const session = await handler.createSession(callSid, operatorId, metadata)

      expect(session).toBeDefined()
      expect(session.sessionId).toContain('op_audio_')
      expect(session.operatorId).toBe(operatorId)
      expect(session.callSid).toBe(callSid)
      expect(session.status).toBe('connected')
      expect(session.metadata).toEqual(metadata)
    })

    it('should handle session creation failure', async () => {
      const callSid = 'CALL123'
      const operatorId = 'OP456'

      mockGeminiService.prototype.createSession = jest.fn()
        .mockRejectedValue(new Error('Failed to create Gemini session'))

      await expect(handler.createSession(callSid, operatorId))
        .rejects.toThrow('Failed to create Gemini session')
    })
  })

  describe('controlAudio', () => {
    it('should mute audio successfully', async () => {
      // First create a session
      const sessionId = 'test_session_123'
      const session = {
        sessionId,
        operatorId: 'OP456',
        callSid: 'CALL123',
        status: 'active' as const,
        startTime: new Date(),
        muted: false,
        volume: 75,
        metadata: {}
      }

      // Manually add session to handler's internal map
      ;(handler as any).sessions.set(sessionId, session)

      const result = await handler.controlAudio(sessionId, 'mute')

      expect(result).toBe(true)
      expect(session.muted).toBe(true)
    })

    it('should adjust volume successfully', async () => {
      const sessionId = 'test_session_123'
      const session = {
        sessionId,
        operatorId: 'OP456',
        callSid: 'CALL123',
        status: 'active' as const,
        startTime: new Date(),
        muted: false,
        volume: 75,
        metadata: {}
      }

      ;(handler as any).sessions.set(sessionId, session)

      const result = await handler.controlAudio(sessionId, 'adjustVolume', { volume: 50 })

      expect(result).toBe(true)
      expect(session.volume).toBe(50)
    })

    it('should return false for non-existent session', async () => {
      const result = await handler.controlAudio('non_existent_session', 'mute')
      expect(result).toBe(false)
    })
  })

  describe('WebSocket handling', () => {
    it('should handle Twilio WebSocket connection', async () => {
      const sessionId = 'test_session_123'
      const session = {
        sessionId,
        operatorId: 'OP456',
        callSid: 'CALL123',
        status: 'connected' as const,
        startTime: new Date(),
        muted: false,
        volume: 75,
        metadata: {}
      }

      ;(handler as any).sessions.set(sessionId, session)

      const mockWs = {
        on: jest.fn(),
        send: jest.fn(),
        close: jest.fn()
      } as unknown as WebSocket

      const result = await handler.connectTwilioWebSocket(sessionId, mockWs, 'stream_123')

      expect(result).toBe(true)
      expect(session.status).toBe('active')
      expect(mockWs.on).toHaveBeenCalledWith('message', expect.any(Function))
      expect(mockWs.on).toHaveBeenCalledWith('close', expect.any(Function))
      expect(mockWs.on).toHaveBeenCalledWith('error', expect.any(Function))
    })
  })

  describe('getStats', () => {
    it('should return correct statistics', () => {
      // Add some test sessions
      const sessions = [
        {
          sessionId: 'session1',
          operatorId: 'OP1',
          callSid: 'CALL1',
          status: 'active' as const,
          startTime: new Date(),
          muted: true,
          volume: 80,
          metadata: {}
        },
        {
          sessionId: 'session2',
          operatorId: 'OP2',
          callSid: 'CALL2',
          status: 'active' as const,
          startTime: new Date(),
          muted: false,
          volume: 70,
          metadata: {}
        }
      ]

      sessions.forEach(session => {
        ;(handler as any).sessions.set(session.sessionId, session)
        ;(handler as any).operatorSessions.set(session.operatorId, new Set([session.sessionId]))
      })

      const stats = handler.getStats()

      expect(stats.totalSessions).toBe(2)
      expect(stats.activeSessions).toBe(2)
      expect(stats.operatorsWithSessions).toBe(2)
      expect(stats.mutedSessions).toBe(1)
      expect(stats.averageVolume).toBe(75)
    })
  })
})

describe('Audio Processing', () => {
  it('should convert μ-law to PCM correctly', () => {
    // Test data: simple μ-law encoded buffer
    const mulawData = Buffer.from([0xFF, 0x7F, 0x00, 0x80])
    
    // This would be in the actual service, but we're testing the concept
    const convertUlawToPCM = (audioBuffer: Buffer): Buffer => {
      const ULAW_TO_LINEAR = new Int16Array(256)
      for (let i = 0; i < 256; i++) {
        const ulaw = ~i
        let t = ((ulaw & 0x0F) << 3) + 0x84
        t <<= ((ulaw & 0x70) >> 4)
        ULAW_TO_LINEAR[i] = (ulaw & 0x80) ? (0x84 - t) : (t - 0x84)
      }

      const int16Buffer = Buffer.alloc(audioBuffer.length * 2)
      for (let i = 0; i < audioBuffer.length; i++) {
        const ulawSample = audioBuffer[i]
        const pcmSample = ULAW_TO_LINEAR[ulawSample]
        int16Buffer.writeInt16LE(pcmSample, i * 2)
      }

      return int16Buffer
    }

    const pcmData = convertUlawToPCM(mulawData)
    
    expect(pcmData).toBeInstanceOf(Buffer)
    expect(pcmData.length).toBe(mulawData.length * 2) // PCM is 16-bit, μ-law is 8-bit
  })

  it('should handle audio resampling from 8kHz to 16kHz', () => {
    const input8kHz = new Float32Array([0.1, 0.2, 0.3, 0.4])
    
    const upsample8kTo16k = (data: Float32Array): Float32Array => {
      const outputLength = data.length * 2
      const output = new Float32Array(outputLength)

      for (let i = 0; i < data.length - 1; i++) {
        const current = data[i]
        const next = data[i + 1]
        output[i * 2] = current
        output[i * 2 + 1] = (current + next) / 2
      }

      if (data.length > 0) {
        output[outputLength - 2] = data[data.length - 1]
        output[outputLength - 1] = data[data.length - 1]
      }

      return output
    }

    const output16kHz = upsample8kTo16k(input8kHz)
    
    expect(output16kHz.length).toBe(input8kHz.length * 2)
    expect(output16kHz[0]).toBe(0.1) // First sample unchanged
    expect(output16kHz[1]).toBeCloseTo(0.15) // Interpolated between 0.1 and 0.2
    expect(output16kHz[2]).toBe(0.2) // Second original sample
  })
})