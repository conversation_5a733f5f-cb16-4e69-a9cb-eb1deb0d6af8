import {
  AppError,
  <PERSON>rror<PERSON>ever<PERSON>,
  ErrorCodes,
  createErrorResponse,
  createDetailedErrorResponse,
  handleTRPCError,
  logError, // Import to allow spying if needed, though not directly tested here
} from './errorHandler.js';
import { TRPCError } from '@trpc/server'; // For mocking
import logger from './logger.js'; // To mock its methods

// Mock logger to prevent actual logging during tests
jest.mock('./logger', () => ({
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  log: jest.fn(),
}));

// Mock @trpc/server
jest.mock('@trpc/server', () => ({
  TRPCError: jest.fn(),
}));

describe('Error Handling Utilities', () => {
  let originalNodeEnv: string | undefined;

  beforeEach(() => {
    originalNodeEnv = process.env.NODE_ENV;
    // Reset mocks for each test
    (logger.error as jest.Mock).mockClear();
    (TRPCError as jest.Mock).mockClear();
  });

  afterEach(() => {
    process.env.NODE_ENV = originalNodeEnv;
  });

  describe('createErrorResponse', () => {
    it('should map specific AppError codes from ErrorCodes correctly', () => {
      const notFoundError = new AppError({ ...ErrorCodes.NOT_FOUND, code: 'NOT_FOUND' });
      const resourceLockedError = new AppError({ ...ErrorCodes.RESOURCE_LOCKED, code: 'RESOURCE_LOCKED' });

      expect(createErrorResponse(notFoundError).status).toBe(404);
      expect(createErrorResponse(resourceLockedError).status).toBe(423);
    });

    it('should map AppError with UNKNOWN_ERROR code and severity HIGH/CRITICAL to 500', () => {
      const highSeverityError = new AppError({ code: 'CUSTOM_HIGH_ERROR', message: 'High sev', severity: ErrorSeverity.HIGH });
      const criticalSeverityError = new AppError({ code: 'CUSTOM_CRITICAL_ERROR', message: 'Crit sev', severity: ErrorSeverity.CRITICAL });

      expect(createErrorResponse(highSeverityError).status).toBe(500);
      expect(createErrorResponse(criticalSeverityError).status).toBe(500);
    });

    it('should map AppError with severity MEDIUM to 400 if no specific code matches', () => {
        const mediumError = new AppError({code: 'SOME_MEDIUM_ERROR', message: 'Medium', severity: ErrorSeverity.MEDIUM });
        expect(createErrorResponse(mediumError).status).toBe(400);
    });

    it('should map AppError with severity LOW to 400 if no specific code matches', () => {
        const lowError = new AppError({code: 'SOME_LOW_ERROR', message: 'Low', severity: ErrorSeverity.LOW });
        expect(createErrorResponse(lowError).status).toBe(400);
    });

    it('should include generic Error message in development', () => {
      process.env.NODE_ENV = 'development';
      const genericError = new Error('Something went wrong');
      const response = createErrorResponse(genericError);
      expect(response.status).toBe(500);
      expect(response.body.error.message).toBe('Something went wrong');
    });

    it('should use default message for generic Error in production', () => {
      process.env.NODE_ENV = 'production';
      const genericError = new Error('Something went wrong');
      const response = createErrorResponse(genericError);
      expect(response.status).toBe(500);
      expect(response.body.error.message).toBe('An unexpected error occurred');
    });

    it('should use AppError message directly', () => {
      const appError = new AppError({ code: 'MY_CODE', message: 'My specific message' });
      const response = createErrorResponse(appError);
      expect(response.body.error.message).toBe('My specific message');
      expect(response.body.error.code).toBe('MY_CODE');
    });
  });

  describe('createDetailedErrorResponse', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'development'; // Most detailed tests are for dev
    });

    it('should include stack for generic Error in development', () => {
      const genericError = new Error('Generic error for details');
      genericError.stack = 'This is a stack trace';
      const response = createDetailedErrorResponse(genericError);
      expect(response.body.error.details?.stack).toBe('This is a stack trace');
    });

    it('should include AppError details in development', () => {
      const originalError = new Error('Original Cause');
      originalError.stack = 'Original stack';
      originalError.name = 'OriginalErrorName';

      const appError = new AppError({
        code: 'DETAILED_CODE',
        message: 'Detailed message',
        severity: ErrorSeverity.MEDIUM,
        component: 'TestComponent',
        context: { key: 'value' },
        originalError,
      });
      appError.stack = 'AppError stack trace';

      const response = createDetailedErrorResponse(appError);
      expect(response.body.error.code).toBe('DETAILED_CODE');
      expect(response.body.error.message).toBe('Detailed message');
      expect(response.body.error.details?.component).toBe('TestComponent');
      expect(response.body.error.details?.severity).toBe(ErrorSeverity.MEDIUM);
      expect(response.body.error.details?.context).toEqual({ key: 'value' });
      expect(response.body.error.details?.stack).toBe('AppError stack trace');
      expect(response.body.error.details?.originalError).toEqual({
        name: 'OriginalErrorName',
        message: 'Original Cause',
        stack: 'Original stack',
      });
    });

    it('should not include details in production for AppError, except basic error info', () => {
      process.env.NODE_ENV = 'production';
      const appError = new AppError({ code: 'PROD_CODE', message: 'Prod message' });
      const response = createDetailedErrorResponse(appError);
      expect(response.body.error.details).toBeUndefined();
      expect(response.body.error.code).toBe('PROD_CODE');
      expect(response.body.error.message).toBe('Prod message');
    });
  });

  describe('handleTRPCError', () => {
    const mockCause = new Error('Test cause');

    it.each([
      // AppErrorCode, Expected TRPCCode, (Optional) httpStatus
      ['UNAUTHORIZED', 'UNAUTHORIZED', 401],
      ['FORBIDDEN', 'FORBIDDEN', 403],
      ['NOT_FOUND', 'NOT_FOUND', 404],
      ['VALIDATION_ERROR', 'BAD_REQUEST', 400],
      ['RESOURCE_LOCKED', 'CONFLICT', 423],
      ['TIMEOUT', 'TIMEOUT', 408],
      ['TWILIO_ERROR', 'BAD_GATEWAY', 502],
      ['DATABASE_ERROR', 'SERVICE_UNAVAILABLE', 503],
      ['SERVICE_UNAVAILABLE', 'SERVICE_UNAVAILABLE', 503],
      ['INTERNAL_SERVER_ERROR', 'INTERNAL_SERVER_ERROR', 500],
    ])('should map AppError with code %s to TRPC code %s', (appErrorCode, expectedTRPCCode, httpStatus) => {
      const errorInfo = ErrorCodes[appErrorCode as keyof typeof ErrorCodes] || { status: httpStatus || 500, message: 'Default message' };
      const appError = new AppError({ code: appErrorCode, message: errorInfo.message, originalError: mockCause, severity: ErrorSeverity.HIGH });

      expect(() => handleTRPCError(appError)).toThrow();
      expect(TRPCError).toHaveBeenCalledWith({
        code: expectedTRPCCode,
        message: errorInfo.message,
        cause: appError,
      });
    });

    it('should map an AppError with a code not in ErrorCodes to INTERNAL_SERVER_ERROR by default path', () => {
      const appError = new AppError({ code: 'VERY_CUSTOM_UNKNOWN_CODE', message: 'Custom unknown message', originalError: mockCause });
      // This error will take the fallback path in handleTRPCError after failing ErrorCodes lookup
      expect(() => handleTRPCError(appError)).toThrow();
      expect(TRPCError).toHaveBeenCalledWith({
        code: 'INTERNAL_SERVER_ERROR', // Default from the fallback within handleTRPCError
        message: appError.message,
        cause: appError,
      });
    });

    it('should re-throw non-AppError errors', () => {
      const genericError = new Error('Some generic error');
      expect(() => handleTRPCError(genericError)).toThrow(genericError);
      expect(TRPCError).not.toHaveBeenCalled();
    });
  });
});
