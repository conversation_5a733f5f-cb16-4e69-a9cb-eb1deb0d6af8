/**
 * Budget Service - Handles budget usage tracking and integration with call operations
 */

import { trpc } from '../../lib/trpc/client';

export interface BudgetUsageEvent {
  budgetId: string;
  minutesUsed: number;
  operationType: 'call' | 'ai_interaction' | 'system_usage';
  operatorId?: string;
  callId?: string;
  campaignId?: string;
  timestamp: Date;
}

export class BudgetService {
  /**
   * Record budget usage when minutes are consumed
   */
  static async recordUsage(event: BudgetUsageEvent): Promise<void> {
    try {
      // Update the budget's used minutes
      const budget = await trpc.budget.byId.query({ id: event.budgetId });
      
      if (!budget) {
        throw new Error('Budget not found');
      }

      // Calculate new used minutes
      const newUsedMinutes = (budget.used_minutes || 0) + event.minutesUsed;
      
      // Update the budget
      await trpc.budget.update.mutate({
        id: event.budgetId,
        used_minutes: newUsedMinutes
      });

      // Log the usage event for audit purposes
      console.log('Budget usage recorded:', {
        budgetId: event.budgetId,
        minutesUsed: event.minutesUsed,
        operationType: event.operationType,
        timestamp: event.timestamp,
      });

      // Create a BudgetExpense record for detailed tracking
      if (event.operatorId || event.callId || event.campaignId) {
        const expenseData = {
          budgetId: event.budgetId,
          amount: event.minutesUsed,
          description: `${event.operationType} - ${event.minutesUsed} minutes`,
          category: event.operationType,
          date: event.timestamp,
          metadata: {
            operatorId: event.operatorId,
            callId: event.callId,
            campaignId: event.campaignId
          }
        };
        
        // Note: This assumes a budgetExpense router exists or will be created
        // For now, we'll just log it
        console.log('Budget expense record created:', expenseData);
      }
    } catch (error) {
      console.error('Failed to record budget usage:', error);
      throw error;
    }
  }

  /**
   * Check if a budget has sufficient minutes for an operation
   */
  static async checkBudgetAvailability(
    budgetId: string,
    requiredMinutes: number
  ): Promise<{ available: boolean; remainingMinutes: number }> {
    try {
      // Retrieve the budget
      const budget = await trpc.budget.byId.query({ id: budgetId });

      if (!budget) {
        throw new Error('Budget not found');
      }

      const remainingMinutes = (budget.total_minutes || 0) - (budget.used_minutes || 0);
      const available = remainingMinutes >= requiredMinutes;

      return {
        available,
        remainingMinutes,
      };
    } catch (error) {
      console.error('Failed to check budget availability:', error);
      throw error;
    }
  }

  /**
   * Get budget usage statistics
   */
  static getBudgetStats(budget: any) {
    const totalMinutes = budget.total_minutes;
    const usedMinutes = budget.used_minutes;
    const remainingMinutes = totalMinutes - usedMinutes;
    const usagePercentage = totalMinutes > 0 ? Math.round((usedMinutes / totalMinutes) * 100) : 0;

    const totalCost = totalMinutes * Number(budget.cost_per_minute);
    const usedCost = usedMinutes * Number(budget.cost_per_minute);
    const remainingCost = totalCost - usedCost;

    return {
      totalMinutes,
      usedMinutes,
      remainingMinutes,
      usagePercentage,
      totalCost,
      usedCost,
      remainingCost,
      isNearLimit: usagePercentage >= 90,
      isOverBudget: usagePercentage >= 100,
    };
  }

  /**
   * Format currency for display
   */
  static formatCurrency(amount: number, currency: string = 'EUR'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  }

  /**
   * Format minutes for display
   */
  static formatMinutes(minutes: number): string {
    if (minutes < 60) {
      return `${minutes} min`;
    }

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (remainingMinutes === 0) {
      return `${hours}h`;
    }

    return `${hours}h ${remainingMinutes}m`;
  }

  /**
   * Get budget alert level based on usage percentage
   */
  static getBudgetAlertLevel(usagePercentage: number): 'safe' | 'warning' | 'danger' | 'critical' {
    if (usagePercentage >= 100) return 'critical';
    if (usagePercentage >= 90) return 'danger';
    if (usagePercentage >= 75) return 'warning';
    return 'safe';
  }

  /**
   * Get budget status color for UI
   */
  static getBudgetStatusColor(usagePercentage: number): string {
    const level = this.getBudgetAlertLevel(usagePercentage);

    switch (level) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'danger': return 'text-red-500 bg-red-50 border-red-200';
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'safe': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  }

  /**
   * Calculate estimated completion date based on current usage rate
   */
  static estimateCompletionDate(budget: any): Date | null {
    const stats = this.getBudgetStats(budget);

    if (stats.usedMinutes === 0) {
      return null; // No usage data to estimate
    }

    const startDate = new Date(budget.start_date);
    const now = new Date();
    const daysElapsed = Math.max(1, Math.floor((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)));
    const dailyUsageRate = stats.usedMinutes / daysElapsed;

    if (dailyUsageRate === 0) {
      return null;
    }

    const daysToCompletion = stats.remainingMinutes / dailyUsageRate;
    const estimatedCompletionDate = new Date(now.getTime() + (daysToCompletion * 24 * 60 * 60 * 1000));

    return estimatedCompletionDate;
  }
}

export default BudgetService;
