{"name": "call_node", "version": "1.0.0", "description": "", "type": "module", "engines": {"node": ">=22.14.0"}, "packageManager": "pnpm@10.11.0", "main": "index.js", "scripts": {"test": "jest --config tests/config/jest.config.js", "test:unit": "jest --config tests/config/jest.unit.config.js", "test:integration": "jest --config tests/config/jest.integration.config.js", "test:e2e:jest": "jest --config tests/config/jest.e2e.config.js", "test:coverage": "jest --config tests/config/jest.config.js --coverage", "test:watch": "jest --config tests/config/jest.config.js --watch", "test:ci": "npm run test:unit && npm run test:integration", "test:full": "npm run test:unit && npm run test:integration && npm run test:e2e:jest", "test:comprehensive": "./scripts/test-comprehensive.sh", "test:comprehensive:coverage": "./scripts/test-comprehensive.sh --coverage", "test:comprehensive:performance": "./scripts/test-comprehensive.sh --performance", "test:features": "npm run test:unit -- --testPathPattern=\"(assistant|operator|supervisor|studio|piano|quality).service.test.ts\"", "test:services": "jest --config tests/config/jest.unit.config.js tests/unit/backend/services", "test:components": "jest --config tests/config/jest.unit.config.js tests/unit/frontend/components", "test:advanced-features": "npm run test:unit -- --testPathPattern=\"(timeline|budgets|projects|reporting).service.test.ts\"", "test:database-performance": "npm run test:integration -- --testPathPattern=\"database-performance.test.ts\"", "test:all-features": "npm run test:unit -- --testPathPattern=\"(assistant|operator|supervisor|studio|piano|quality|timeline|budgets|projects|reporting).service.test.ts\"", "test:final-features": "npm run test:unit -- --testPathPattern=\"(settings|account|payment).service.test.ts\"", "test:complete-features": "npm run test:unit -- --testPathPattern=\"(assistant|operator|supervisor|studio|piano|quality|timeline|budgets|projects|reporting|settings|account|payment).service.test.ts\"", "test:e2e-workflows": "npm run test:e2e:jest -- --testPathPattern=\"complete-workflows.test.ts\"", "test:production-readiness": "npm run test:integration -- --testPathPattern=\"production-readiness.test.ts\"", "start": "node dist/index.js", "start:ts": "NODE_OPTIONS='--loader ts-node/esm' node backend/src/index.ts", "start:trpc": "node dist/trpc-server.js", "start:backend": "node dist/trpc-server.js", "dev": "turbo run dev --parallel", "build": "pnpm run build:prod", "lint": "turbo run lint", "test:all": "npm run test:unit && npm run test:integration", "clean": "turbo run clean && rm -rf node_modules", "format": "prettier --write .", "pm2": "node node_modules/pm2/bin/pm2", "pm2:logs": "node node_modules/pm2/bin/pm2 logs", "build:watch": "tsc -w", "build:transpile": "tsc --noEmit false --emitDeclarationOnly false --skipLibCheck true", "build:ignoreErrors": "tsc -p tsconfig.build.json", "monitor": "NODE_OPTIONS='--loader ts-node/esm' node backend/src/services/monitoringService.ts", "twilio": "DEBUG=twilio-handler NODE_OPTIONS='--loader ts-node/esm' node backend/src/controllers/twilioMessageHandler.ts", "start:all": "concurrently \"pnpm run monitor\" \"pnpm run twilio\"", "prebuild": "rimraf dist && pnpm prisma generate", "build:prod": "pnpm run prebuild && tsc && pnpm run postbuild", "build:nocheck": "node scripts/build-esbuild.js", "build:legacy": "node scripts/build-ts.js", "build:esbuild": "node scripts/build-esbuild.js", "build:fast": "node scripts/build-esbuild.js", "postbuild": "mkdir -p dist/data && cp -r backend/src/campaigns dist/ 2>/dev/null || :", "setup:supabase": "node scripts/setup-supabase.js", "postinstall": "chmod +x scripts/node-websocat.js", "websocat": "node scripts/node-websocat.js", "test:ws": "node scripts/test-websocket.js", "prisma": "prisma", "prisma:generate": "prisma generate", "schema:merge": "node scripts/merge-schema.js", "schema:watch": "node scripts/watch-schema.js", "db:test-connection": "NODE_OPTIONS='--loader ts-node/esm' node scripts/test-db-connection.ts", "prepare": "pnpm exec husky || true", "test:all-comprehensive": "./scripts/run-all-tests.sh", "security:scan": "./scripts/security-scan.sh", "security:deps": "./scripts/security-scan.sh --dependencies-only", "security:code": "./scripts/security-scan.sh --code-only", "security:secrets": "./scripts/security-scan.sh --secrets-only", "security:audit": "node scripts/security-audit.js", "security:comprehensive": "npm run security:audit && npm run security:scan", "security:fix": "npm audit fix && cd backend && npm audit fix && cd ../frontend && npm audit fix", "security:setup": "./scripts/setup-security.sh", "load:test": "k6 run tests/load/k6-load-test.js", "load:test:local": "BASE_URL=http://localhost:3000 k6 run tests/load/k6-load-test.js", "monitoring:start": "docker-compose -f monitoring/docker-compose.yml up -d", "monitoring:stop": "docker-compose -f monitoring/docker-compose.yml down", "deploy:staging": "./deploy.sh staging", "deploy:production": "./deploy.sh production", "health:check": "curl -f http://localhost:3100/trpc/health.healthcheck || exit 1", "ci:prepare": "pnpm install --frozen-lockfile && pnpm run build:nocheck", "ci:test": "pnpm run test:ci && pnpm run security:scan", "ci:deploy": "pnpm run ci:prepare && pnpm run ci:test && pnpm run deploy:staging", "test:unit:backend": "jest --config tests/config/jest.unit.config.js tests/unit/backend", "test:unit:frontend": "jest --config tests/config/jest.unit.config.js tests/unit/frontend", "test:e2e": "playwright test --config tests/config/playwright.config.js", "test:load": "k6 run tests/load/", "test:all:comprehensive": "./scripts/run-all-tests.sh", "test:controllers": "jest --config tests/config/jest.unit.config.js tests/unit/backend/controllers", "test:utils": "jest --config tests/config/jest.unit.config.js tests/unit/backend/utils", "test:legacy": "NODE_OPTIONS=--experimental-vm-modules DOTENV_CONFIG_PATH=.env.test jest"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.826.0", "@aws-sdk/lib-storage": "^3.826.0", "@aws-sdk/s3-request-presigner": "^3.826.0", "@deepgram/sdk": "^3.12.1", "@eventstore/db-client": "^6.2.1", "@fastify/cors": "^10.0.0", "@fastify/formbody": "^8.0.2", "@fastify/static": "^8.0.0", "@fastify/websocket": "^11.0.0", "@google-cloud/aiplatform": "^3.13.0", "@google-cloud/vertexai": "^0.5.0", "@google/genai": "1.4.0", "@openai/agents": "^0.0.3", "@prisma/client": "^6.8.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/stripe-js": "^7.3.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.76.0", "@temporalio/activity": "^1.11.8", "@temporalio/client": "^1.11.8", "@temporalio/worker": "^1.11.8", "@temporalio/workflow": "^1.11.8", "@trpc/client": "^11.1.4", "@trpc/server": "^11.1.2", "add": "^2.0.6", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "bullmq": "^5.0.0", "chalk": "^5.4.1", "class-variance-authority": "^0.7.1", "countries-and-timezones": "^3.8.0", "dotenv": "^16.4.5", "fastify": "^5.3.2", "google-auth-library": "^9.15.1", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.12.7", "lucide-react": "^0.503.0", "nats": "^2.29.3", "next": "^15.3.1", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "openai": "^4.96.0", "or": "^0.2.0", "pino-pretty": "^13.0.0", "prism-react-renderer": "^2.4.1", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.56.4", "recharts": "^2.15.3", "redis": "^4.6.13", "sqlite3": "^5.1.7", "stripe": "^18.2.0", "superjson": "^2.2.2", "tailwind-merge": "^3.3.0", "tsx": "^4.19.4", "twilio": "^5.5.2", "uuid": "^11.1.0", "wavesurfer.js": "^7.9.5", "ws": "^8.16.0", "zod": "^3.25.56", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/preset-env": "^7.26.9", "@faker-js/faker": "^8.3.1", "@jest/globals": "^29.7.0", "@tailwindcss/postcss": "^4.1.4", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.29", "@types/node-fetch": "^2.6.12", "@types/supertest": "^6.0.2", "@types/ws": "^8.18.1", "axios": "^1.8.4", "babel-jest": "^29.7.0", "concurrently": "^9.1.2", "esbuild": "^0.25.8", "eslint": "^9.27.0", "husky": "^9.1.7", "jest": "^29.7.0", "nodemon": "^3.1.0", "pm2": "^6.0.6", "prettier": "^3.2.5", "prisma": "^6.8.2", "rimraf": "^6.0.1", "supabase": "^2.22.12", "supertest": "^6.3.3", "testcontainers": "^10.2.1", "ts-node": "^10.9.2", "turbo": "^2.5.3", "typescript": "^5.8.3"}}