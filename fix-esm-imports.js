#!/usr/bin/env node

/**
 * Fix ESM import paths by adding .js extensions
 * This is required for Node.js ESM modules
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function findTsFiles(dir, fileList = []) {
  const files = await fs.readdir(dir, { withFileTypes: true });
  
  for (const file of files) {
    const fullPath = path.join(dir, file.name);
    
    if (file.isDirectory()) {
      if (!['node_modules', 'dist', 'coverage', '.git'].includes(file.name)) {
        await findTsFiles(fullPath, fileList);
      }
    } else if (file.name.endsWith('.ts') || file.name.endsWith('.tsx')) {
      fileList.push(fullPath);
    }
  }
  
  return fileList;
}

async function fixImports(filePath) {
  let content = await fs.readFile(filePath, 'utf-8');
  let modified = false;
  
  // Match relative imports without .js extension
  const importRegex = /from\s+['"](\.[^'"]+)(?<!\.js)(?<!\.json)(?<!\.css)(?<!\.scss)['"];?/g;
  
  content = content.replace(importRegex, (match, importPath) => {
    // Skip if it already has an extension
    if (path.extname(importPath)) {
      return match;
    }
    
    modified = true;
    return match.replace(importPath, importPath + '.js');
  });
  
  // Also fix dynamic imports
  const dynamicImportRegex = /import\(['"](\.[^'"]+)(?<!\.js)(?<!\.json)(?<!\.css)(?<!\.scss)['"]\)/g;
  
  content = content.replace(dynamicImportRegex, (match, importPath) => {
    if (path.extname(importPath)) {
      return match;
    }
    
    modified = true;
    return match.replace(importPath, importPath + '.js');
  });
  
  if (modified) {
    await fs.writeFile(filePath, content);
    console.log(`✅ Fixed imports in: ${path.relative(process.cwd(), filePath)}`);
    return true;
  }
  
  return false;
}

async function main() {
  console.log('🔧 Fixing ESM imports in TypeScript files...\n');
  
  const backendDir = path.join(__dirname, 'backend', 'src');
  const files = await findTsFiles(backendDir);
  
  console.log(`Found ${files.length} TypeScript files to check\n`);
  
  let fixedCount = 0;
  for (const file of files) {
    if (await fixImports(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✅ Fixed imports in ${fixedCount} files`);
}

main().catch(console.error);