# CLAUDE.md - AI Development Rules for CC-V1
**Auto-generated from AGENTS.md with context-aware enhancements**
**Generated: 2025-08-04 11:00:01 | Workflow Position: completed-deployment-pipeline**

## 🎯 **Current Development Context**

**Active Workflow Position**: completed-deployment-pipeline
**Priority Tasks**: Connect live-demo components to main operator interface, Update operator dashboard routing for demo features, Update environment configuration for new services
**Framework Status**: Active and Learning

## 🚀 **Mandatory Automation Framework Usage**

**CRITICAL: All complex development tasks (3+ steps) MUST use the automation framework:**

```bash
# For bug fixes and analysis
python3 _dev-automation-beta/core/automation-engine.py --template "Analyze and Fix Bug" --context "specific-issue-description"

# For testing and verification
python3 _dev-automation-beta/core/automation-engine.py --template "Run Testing Suite" --context "testing-scope"

# For deployment preparation
python3 _dev-automation-beta/core/automation-engine.py --template "Prepare Deployment" --context "deployment-type"
```

**Recently Proven Effective Approaches**:
- No recent approaches recorded

## 📊 **Template Effectiveness Data**

**Current Template Performance** (Updated: 2025-08-04 11:00:01):
- **Analyze and Fix Bug**: 95% success, 42 minutes avg time
  - Most Effective For: TypeScript compilation errors, Import/export mismatches, Configuration issues
  - Common Pitfalls: Assuming fixes work without verification, Skipping pattern analysis for similar issues
- **Run Testing Suite**: 90% success, 28 minutes avg time
  - Most Effective For: Pre-deployment verification, Bug fix validation, Regression testing
  - Common Pitfalls: Assuming test failures are environmental, Not capturing actual error output
- **Complete Frontend Integration**: 85% success, 105 minutes avg time
  - Most Effective For: Component integration tasks, Demo migration projects, UI consistency updates
  - Common Pitfalls: Not identifying shared component patterns early, Ignoring TypeScript compilation during integration
- **Analyze Current State**: 100% success, 18 minutes avg time
  - Most Effective For: Project state assessment, Gap identification, Planning and prioritization
  - Common Pitfalls: Not capturing specific evidence for findings, Focusing on minor issues over major blockers
- **Prepare Deployment**: 95% success, 22 minutes avg time
  - Most Effective For: Production deployment preparation, Environment configuration verification, Rollback plan preparation
  - Common Pitfalls: Not testing deployment script syntax, Assuming environment is ready without verification

## 🔧 **Current Development Standards**

### **Project Structure Compliance**
- **Frontend**: Next.js 15 with App Router (NO src/ directory)
- **Backend**: Fastify with tRPC routers
- **Database**: Prisma-first architecture with Supabase
- **Testing**: Distributed testing structure with centralized coordination

### **Current Active Features** (14 features):
- **Operator Interface** (active)
  - Status: Integration in progress
  - Next Steps: Complete live-demo integration
  - Related Templates: Complete Frontend Integration, Run Testing Suite
- **Assistant Feature** (active)
  - Status: Core functionality complete
  - Next Steps: Performance optimization
  - Related Templates: Analyze and Fix Bug, Run Testing Suite
- **Authentication System** (stable)
  - Status: Production ready
  - Next Steps: Security audit
  - Related Templates: Run Testing Suite, Verify Deployment

## ⚡ **Experience-Driven Workflow Rules**

### **Proven Patterns from Recent Executions**:
**No proven patterns recorded yet** - Patterns will appear after 3+ successful uses

### **Anti-Patterns to Avoid**:
**No anti-patterns recorded** - Will be populated based on failures and ineffective approaches

## 🧠 **Memory-Enhanced Development**

### **Current Session Memory**:
- **Session ID**: 2025-08-03T23:26:23.255181
- **Started**: 2025-08-04T11:00:01.093525
- **Completed Tasks**: 4
- **Knowledge Captured**: 0 entries

### **Learning Integration**:
- All task completions MUST include experience reporting
- Template effectiveness tracked automatically
- Pattern recognition drives future recommendations
- Continuous improvement through structured feedback

## 🔄 **Workflow State Integration**

### **Current State Persistence**:
```json
{
  "workflow_position": "completed-deployment-pipeline",
  "active_tasks": ["Connect live-demo components to main operator interface", "Update operator dashboard routing for demo features", "Update environment configuration for new services"],
  "completed_tasks": ["Set up parallel operator structure in CC-V1 backend", "Migrate core Gemini services from demo", "Migrate Twilio services with WebSocket handling", "Port advanced audio processing pipeline"],
  "session_metrics": {
    "templates_used": 0,
    "success_rate": "100%",
    "time_efficiency": "100%"
  }
}
```

### **Next Recommended Actions**:
1. **Implement: Connect live-demo components to main operator inte...** (Priority: medium)
   - Template: Complete Frontend Integration
   - Estimated Time: 90 minutes
   - Context: Feature development workflow
2. **Implement: Update operator dashboard routing for demo feature...** (Priority: medium)
   - Template: Complete Frontend Integration
   - Estimated Time: 90 minutes
   - Context: Feature development workflow
3. **Implement: Update environment configuration for new services...** (Priority: medium)
   - Template: Complete Frontend Integration
   - Estimated Time: 90 minutes
   - Context: Feature development workflow
4. **Verify deployment health and performance** (Priority: high)
   - Template: Verify Deployment
   - Estimated Time: 15 minutes
   - Context: Post-deployment verification

## 📈 **Continuous Improvement System**

### **Template Evolution Status**:
- **Templates Updated**: 4 in last 30 days
- **New Patterns Identified**: 0
- **Automation Opportunities**: 7
- **Framework Effectiveness**: 92%

### **Quality Metrics**:
- **First-Time Success Rate**: 87%
- **Average Task Completion Time**: 42 minutes
- **Knowledge Retention Rate**: 95%
- **Pattern Recognition Accuracy**: 88%

---

**This file is auto-synchronized every hour and after major workflow state changes.**
**Manual edits will be overwritten - update AGENTS.md instead.**
**Last Sync**: 2025-08-04 11:00:01 | **Next Sync**: 2025-08-04 11:00:00

## 🔗 **Integration with Development Tools**

### **Automation Framework Integration**:
- State persisted in SQLite: `_dev-automation-beta/memory/automation.db`
- Template index: `_dev-automation-beta/TEMPLATE_INDEX.yaml`
- Experience tracking: Automatic after each template execution
- Memory layers: Short-term (session), Mid-term (patterns), Long-term (knowledge)

### **Cross-System Synchronization**:
- **AGENTS.md** → All AI assistant rule files (hourly)
- **Template effectiveness** → Rule recommendations (real-time)
- **Workflow state** → Context-aware rule generation (continuous)
- **Learning patterns** → Template evolution (weekly)

**For detailed technical implementation, see**: `_dev-automation-beta/UNIFIED_WORKFLOW_INTEGRATION.md`